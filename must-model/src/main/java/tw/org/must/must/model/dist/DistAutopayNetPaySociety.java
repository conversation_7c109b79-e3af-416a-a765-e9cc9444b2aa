package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_autopay_net_pay_society`")
public class DistAutopayNetPaySociety extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 协会编号
     */
    @Column(name = "`society_code`")
    private Integer societyCode;

    /**
     * 支付金额=royalty_amount+fee_in_error_amount+adj_royalty_amount-bank_chagres
     */
    @Column(name = "`pay_amount`")
    private BigDecimal payAmount;

    /**
     * retain编码
     */
    @Column(name = "`retain_code`")
    private String retainCode;

    /**
     * 本地入账金额
     */
    @Column(name = "`local_taxable_income`")
    private BigDecimal localTaxableIncome;

    /**
     * 海外入账金额
     */
    @Column(name = "`overseas_taxable_income`")
    private BigDecimal overseasTaxableIncome;

    /**
     * --废弃所得税字段
     */
    @Column(name = "`withheld_tax`")
    private BigDecimal withheldTax;

    /**
     * 管理费金额
     */
    @Column(name = "`commission_amount`")
    private BigDecimal commissionAmount;

    /**
     * 废弃 ----，decuction，扣除费用
     */
    @Column(name = "`non_taxable_income`")
    private BigDecimal nonTaxableIncome;

    /**
     * 支付日期
     */
    @Column(name = "`pay_date`")
    private Date payDate;

    /**
     * 支付方式T=telegraphic transfer, A=Auto transfer, D=Draft, C=Cheque, O=Overseas Draft, X=Not provided'
     */
    @Column(name = "`payment_method`")
    private String paymentMethod;

    /**
     * 支付编号
     */
    @Column(name = "`autopay_no`")
    private String autopayNo;

    /**
     * 备注
     */
    @Column(name = "`remark`")
    private String remark;

    /**
     * 是否支付N   ，Y
     */
    @Column(name = "`is_pay`")
    private String pay;

    /**
     * 协会名称
     */
    @Column(name = "`society_name`")
    private String societyName;

    /**
     * 管理费比例
     */
    @Column(name = "`commission_rate`")
    private BigDecimal commissionRate;

    /**
     * 所得税率
     */
    @Column(name = "`taxable_rate`")
    private BigDecimal taxableRate;

    /**
     * 所得税费
     */
    @Column(name = "`taxable_amount`")
    private BigDecimal taxableAmount;

    /**
     * fIE返款金额
     */
    @Column(name = "`fee_in_error_amount`")
    private BigDecimal feeInErrorAmount;

    /**
     * 银行手续费
     */
    @Column(name = "`bank_chagres`")
    private BigDecimal bankChagres;

    /**
     * 权利金金额=local_taxable_income-commission_amount-taxable_amount
     */
    @Column(name = "`royalty_amount`")
    private BigDecimal royaltyAmount;

    /**
     * 汇率
     */
    @Column(name = "`exchange_rate`")
    private BigDecimal exchangeRate;

    /**
     * 支付货币，默认USD
     */
    @Column(name = "`pay_currency`")
    private String payCurrency;

    /**
     * 权利金调整金额
     */
    @Column(name = "`adj_royalty_amount`")
    private BigDecimal adjRoyaltyAmount;

    /**
     * 银行编号
     */
    @Column(name = "`bank_no`")
    private Long bankNo;

    /**
     * 账户名称
     */
    @Column(name = "`bank_account_name`")
    private String bankAccountName;

    /**
     * 银行账户id
     */
    @Column(name = "`bank_account_no`")
    private String bankAccountNo;

    /**
     * 特殊扣除费用，负数
     */
    @Column(name = "`deduction`")
    private BigDecimal deduction;

    /**
     * 预付款，正数
     */
    @Column(name = "`advance`")
    private BigDecimal advance;

    /**
     * 基础金额，UPA费用
     */
    @Column(name = "`basic_amount`")
    private BigDecimal basicAmount;

    /**
     * 公益金比例
     */
    @Column(name = "`reciprocal_rate`")
    private BigDecimal reciprocalRate;

    /**
     * 公益金费用
     */
    @Column(name = "`reciprocal_amount`")
    private BigDecimal reciprocalAmount;

    @Column(name = "tax_year")
    private Integer taxYear;

    @Column(name = "`retain_id`")
    private Long retainId ;

    public Integer getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(Integer taxYear) {
        this.taxYear = taxYear;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public Integer getSocietyCode() {
        return societyCode;
    }

    public void setSocietyCode(Integer societyCode) {
        this.societyCode = societyCode;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getRetainCode() {
        return retainCode;
    }

    public void setRetainCode(String retainCode) {
        this.retainCode = retainCode;
    }

    public BigDecimal getLocalTaxableIncome() {
        return localTaxableIncome;
    }

    public void setLocalTaxableIncome(BigDecimal localTaxableIncome) {
        this.localTaxableIncome = localTaxableIncome;
    }

    public BigDecimal getOverseasTaxableIncome() {
        return overseasTaxableIncome;
    }

    public void setOverseasTaxableIncome(BigDecimal overseasTaxableIncome) {
        this.overseasTaxableIncome = overseasTaxableIncome;
    }

    public BigDecimal getWithheldTax() {
        return withheldTax;
    }

    public void setWithheldTax(BigDecimal withheldTax) {
        this.withheldTax = withheldTax;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public BigDecimal getNonTaxableIncome() {
        return nonTaxableIncome;
    }

    public void setNonTaxableIncome(BigDecimal nonTaxableIncome) {
        this.nonTaxableIncome = nonTaxableIncome;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getAutopayNo() {
        return autopayNo;
    }

    public void setAutopayNo(String autopayNo) {
        this.autopayNo = autopayNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPay() {
        return pay;
    }

    public void setPay(String pay) {
        this.pay = pay;
    }

    public String getSocietyName() {
        return societyName;
    }

    public void setSocietyName(String societyName) {
        this.societyName = societyName;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getTaxableRate() {
        return taxableRate;
    }

    public void setTaxableRate(BigDecimal taxableRate) {
        this.taxableRate = taxableRate;
    }

    public BigDecimal getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(BigDecimal taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public BigDecimal getFeeInErrorAmount() {
        return feeInErrorAmount;
    }

    public void setFeeInErrorAmount(BigDecimal feeInErrorAmount) {
        this.feeInErrorAmount = feeInErrorAmount;
    }

    public BigDecimal getBankChagres() {
        return bankChagres;
    }

    public void setBankChagres(BigDecimal bankChagres) {
        this.bankChagres = bankChagres;
    }

    public BigDecimal getRoyaltyAmount() {
        return royaltyAmount;
    }

    public void setRoyaltyAmount(BigDecimal royaltyAmount) {
        this.royaltyAmount = royaltyAmount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getPayCurrency() {
        return payCurrency;
    }

    public void setPayCurrency(String payCurrency) {
        this.payCurrency = payCurrency;
    }

    public BigDecimal getAdjRoyaltyAmount() {
        return adjRoyaltyAmount;
    }

    public void setAdjRoyaltyAmount(BigDecimal adjRoyaltyAmount) {
        this.adjRoyaltyAmount = adjRoyaltyAmount;
    }

    public Long getBankNo() {
        return bankNo;
    }

    public void setBankNo(Long bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public BigDecimal getDeduction() {
        return deduction;
    }

    public void setDeduction(BigDecimal deduction) {
        this.deduction = deduction;
    }

    public BigDecimal getAdvance() {
        return advance;
    }

    public void setAdvance(BigDecimal advance) {
        this.advance = advance;
    }

    public BigDecimal getBasicAmount() {
        return basicAmount;
    }

    public void setBasicAmount(BigDecimal basicAmount) {
        this.basicAmount = basicAmount;
    }

    public BigDecimal getReciprocalRate() {
        return reciprocalRate;
    }

    public void setReciprocalRate(BigDecimal reciprocalRate) {
        this.reciprocalRate = reciprocalRate;
    }

    public BigDecimal getReciprocalAmount() {
        return reciprocalAmount;
    }

    public void setReciprocalAmount(BigDecimal reciprocalAmount) {
        this.reciprocalAmount = reciprocalAmount;
    }

    public Long getRetainId() {
        return retainId;
    }

    public void setRetainId(Long retainId) {
        this.retainId = retainId;
    }
}