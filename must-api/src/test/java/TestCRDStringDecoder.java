import com.firstbrave.api.base.CrdRecord;
import com.firstbrave.api.parser.CrdFileDecoder;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class TestCRDStringDecoder {

    @Test
    public void finalTest() throws Exception {
        String file = "G:\\项目资料\\MUST\\document\\解析接口文檔\\编码测试文件\\SGAE_CRD20171218124851161072.021";
        Path path = Paths.get(file);
        if(!path.toFile().exists()) return;
        List<String> list =new ArrayList<>();
        Reader fin = new FileReader(file);

        BufferedReader bin = new BufferedReader(fin);
        String str;
        do {
            str = bin.readLine();
            if (str != null){
                list.add(str);
            }
        }
        while (str != null);

        StringBuffer errorMsg=new StringBuffer();
        List<CrdRecord> ls = CrdFileDecoder.decode(errorMsg,new LinkedList<>(list));

        System.out.println("=====");
    }

/*
        @Test
        public void finalTest() throws Exception {
        ListOverseasFileBase listOverseasFileBase = new ListOverseasFileBase();
        //        String file = listOverseasFileBase.getFilePath();
        String file = "C:\\Users\\<USER>\\Desktop\\CRD\\CRD2019051615283400286726033161.020";
        Path path = Paths.get(file);
        if (!path.toFile().exists()) return;
        List<String> list = Files.readAllLines(path);

        List<CrdRecord> ls = CrdFileDecoder.decode(new LinkedList<>(list));
        if (!CollectionUtils.isEmpty(ls)) {
            Integer groupCount = 1;
            CrdListOverseasFileWorkMapping crdListOverseasFileWorkMapping = new CrdListOverseasFileWorkMapping();
            List<GroupHeader> groupHeaderList = new ArrayList<>();
            List<ExploitationSourceInformation> exploitationSourceInformationList = new ArrayList<>();
            List<InterestedPartyInformation> interestedPartyInformationList = new ArrayList<>();
            List<AudioVisualProgramInformation> audioVisualProgramInformationList = new ArrayList<>();
            List<GroupTrailer> groupTrailerList = new ArrayList<>();
            List<MusicalWorkNotificationTransactionHeaderVo> musicalWorkNotificationTransactionHeaderVoList = new ArrayList<>();
            MusicalWorkNotificationTransactionHeaderVo musicalWorkNotificationTransactionHeaderVo = null;
            List<MusicalWorkInterestedParty> musicalWorkInterestedPartyList = new ArrayList<>();
            List<WorkExploitationPerformance> workExploitationPerformanceList = new ArrayList<>();
            for(CrdRecord crdRecord: ls){
                if(crdRecord instanceof TransmissionHeader){
                    crdListOverseasFileWorkMapping.setTransmissionHeader((TransmissionHeader) crdRecord);
                    continue;
                }
                if(groupCount == 1){
                    if(crdRecord instanceof GroupHeader){
                        groupHeaderList.add((GroupHeader) crdRecord);
                        continue;
                    }if( crdRecord instanceof SocietyDistributionNotificationTransactionHeader){
                        crdListOverseasFileWorkMapping.setSocietyDistributionNotificationTransactionHeader((SocietyDistributionNotificationTransactionHeader) crdRecord);
                        continue;
                    }if( crdRecord instanceof ExploitationSourceInformation){
                        exploitationSourceInformationList.add((ExploitationSourceInformation) crdRecord);
                        continue;
                    }if( crdRecord instanceof InterestedPartyInformation){
                        interestedPartyInformationList.add((InterestedPartyInformation) crdRecord);
                        continue;
                    }if( crdRecord instanceof AudioVisualProgramInformation){
                        audioVisualProgramInformationList.add((AudioVisualProgramInformation) crdRecord);
                        continue;
                    }if( crdRecord instanceof GroupTrailer){
                        groupTrailerList.add((GroupTrailer) crdRecord);
                        groupCount ++;
                        continue;
                    }
                }else if(groupCount ==2){
                    if(crdRecord instanceof GroupHeader){
                        groupHeaderList.add((GroupHeader) crdRecord);
                        continue;
                    }if( crdRecord instanceof MusicalWorkNotificationTransactionHeader){
                        if(musicalWorkNotificationTransactionHeaderVo != null){
                            musicalWorkNotificationTransactionHeaderVo.setMusicalWorkInterestedPartyList(musicalWorkInterestedPartyList);
                            musicalWorkNotificationTransactionHeaderVo.setWorkExploitationPerformanceList(workExploitationPerformanceList);
                            musicalWorkNotificationTransactionHeaderVoList.add(musicalWorkNotificationTransactionHeaderVo);
                        }
                        musicalWorkNotificationTransactionHeaderVo = new MusicalWorkNotificationTransactionHeaderVo();
                        musicalWorkInterestedPartyList = new ArrayList<>();
                        workExploitationPerformanceList = new ArrayList<>();
                        musicalWorkNotificationTransactionHeaderVo.setMusicalWorkNotificationTransactionHeader((MusicalWorkNotificationTransactionHeader) crdRecord);
                        continue;
                    }if( crdRecord instanceof MusicalWorkDistributionStatus){
                        musicalWorkNotificationTransactionHeaderVo.setMusicalWorkDistributionStatus((MusicalWorkDistributionStatus) crdRecord);
                        continue;
                    }if( crdRecord instanceof MusicalWorkDistributionStatusRight){
                        musicalWorkNotificationTransactionHeaderVo.setMusicalWorkDistributionStatusRight((MusicalWorkDistributionStatusRight) crdRecord);
                        continue;
                    }if( crdRecord instanceof MusicalWorkDistributionStatusTerritory){
                        musicalWorkNotificationTransactionHeaderVo.setMusicalWorkDistributionStatusTerritory((MusicalWorkDistributionStatusTerritory) crdRecord);
                        continue;
                    }if( crdRecord instanceof MusicalWorkInterestedParty){
                        musicalWorkInterestedPartyList.add((MusicalWorkInterestedParty) crdRecord);
                        continue;
                    }if( crdRecord instanceof WorkExploitationPerformance){
                        workExploitationPerformanceList.add((WorkExploitationPerformance) crdRecord);
                        continue;
                    }if( crdRecord instanceof GroupTrailer){
                        groupTrailerList.add((GroupTrailer) crdRecord);
                        groupCount ++;
                        continue;
                    }
                }else if(groupCount == 3){
                    if(crdRecord instanceof GroupHeader){
                        groupHeaderList.add((GroupHeader) crdRecord);
                        if(musicalWorkNotificationTransactionHeaderVo != null){
                            musicalWorkNotificationTransactionHeaderVo.setMusicalWorkInterestedPartyList(musicalWorkInterestedPartyList);
                            musicalWorkNotificationTransactionHeaderVo.setWorkExploitationPerformanceList(workExploitationPerformanceList);
                            musicalWorkNotificationTransactionHeaderVoList.add(musicalWorkNotificationTransactionHeaderVo);
                        }
                        continue;
                    }
                    if(crdRecord instanceof RoyaltyGrandTotalsTransactionHeader){
                        crdListOverseasFileWorkMapping.setRoyaltyGrandTotalsTransactionHeader((RoyaltyGrandTotalsTransactionHeader) crdRecord);
                    }if( crdRecord instanceof GroupTrailer){
                        groupTrailerList.add((GroupTrailer) crdRecord);
        //                        groupCount ++;
                        continue;
                    }
                    if( crdRecord instanceof TransmissionTrailer){
                        crdListOverseasFileWorkMapping.setTransmissionTrailer((TransmissionTrailer) crdRecord);
                        continue;
                    }
                }
            }
            crdListOverseasFileWorkMapping.setInterestedPartyInformation(interestedPartyInformationList);
            crdListOverseasFileWorkMapping.setGroupHeaderList(groupHeaderList);
            crdListOverseasFileWorkMapping.setGroupTrailerList(groupTrailerList);
            crdListOverseasFileWorkMapping.setExploitationSourceInformationList(exploitationSourceInformationList);
            crdListOverseasFileWorkMapping.setAudioVisualProgramInformationList(audioVisualProgramInformationList);
            crdListOverseasFileWorkMapping.setMusicalWorkNotificationTransactionHeaderVoList(musicalWorkNotificationTransactionHeaderVoList);
            //封装解析
            if(crdListOverseasFileWorkMapping != null){
                List<InterestedPartyInformation> interestedPartyInformationList1 = crdListOverseasFileWorkMapping.getInterestedPartyInformation();
                SocietyDistributionNotificationTransactionHeader societyDistributionNotificationTransactionHeader = crdListOverseasFileWorkMapping.getSocietyDistributionNotificationTransactionHeader();
                List<MusicalWorkNotificationTransactionHeaderVo> musicalWorkNotificationTransactionHeaderVoList1 = crdListOverseasFileWorkMapping.getMusicalWorkNotificationTransactionHeaderVoList();
                if(CollectionUtils.isNotEmpty(musicalWorkNotificationTransactionHeaderVoList1)){
                    List<ListOverseasFileWorkMapping> listOverseasFileWorkMappings = new ArrayList<>();
                    musicalWorkNotificationTransactionHeaderVoList1.forEach(musicalWorkNotificationTransactionHeaderVo1 ->{
                        MusicalWorkNotificationTransactionHeader musicalWorkNotificationTransactionHeader =musicalWorkNotificationTransactionHeaderVo1.getMusicalWorkNotificationTransactionHeader();
                        MusicalWorkDistributionStatus musicalWorkDistributionStatus = musicalWorkNotificationTransactionHeaderVo1.getMusicalWorkDistributionStatus();
                        MusicalWorkDistributionStatusRight musicalWorkDistributionStatusRight = musicalWorkNotificationTransactionHeaderVo1.getMusicalWorkDistributionStatusRight();
                        MusicalWorkDistributionStatusTerritory musicalWorkDistributionStatusTerritory = musicalWorkNotificationTransactionHeaderVo1.getMusicalWorkDistributionStatusTerritory();
                        List<MusicalWorkInterestedParty> musicalWorkInterestedPartyList1 = musicalWorkNotificationTransactionHeaderVo1.getMusicalWorkInterestedPartyList();//作品与人关系表
                        List<WorkExploitationPerformance> workExploitationPerformanceList1 = musicalWorkNotificationTransactionHeaderVo1.getWorkExploitationPerformanceList();//表演者
                        if(CollectionUtils.isNotEmpty(musicalWorkInterestedPartyList1)){
                            musicalWorkInterestedPartyList1.forEach(musicalWorkInterestedParty ->{//作品-人对应关系
                                if(CollectionUtils.isNotEmpty(workExploitationPerformanceList1)){
                                    workExploitationPerformanceList1.forEach(workExploitationPerformance -> {//表演者
                                        ListOverseasFileWorkMapping listOverseasFileWorkMapping = new ListOverseasFileWorkMapping();
                                        listOverseasFileWorkMapping.setFileBaseId(listOverseasFileBase.getId());
                                        listOverseasFileWorkMapping.setReceiptDetailsId(listOverseasFileBase.getReceiptDetailsId());
        //                                StringUtils.isBlank(societyDistributionNotificationTransactionHeader.getRemittingSocietyDistributionIdentifier())?0:Integer.valueOf(societyDistributionNotificationTransactionHeader.getRemittingSocietyDistributionIdentifier())
                                        listOverseasFileWorkMapping.
                                                setRemitSociety(listOverseasFileBase.getRemitSocietyCode());
                                        listOverseasFileWorkMapping.
                                                setReceiptSociety(listOverseasFileBase.getReceiptSocietyCode());
                                        listOverseasFileWorkMapping.setSourceWorkCode(musicalWorkNotificationTransactionHeader.getCompositeWorkIdentifier());
                                        listOverseasFileWorkMapping.setIswc(musicalWorkNotificationTransactionHeader.getIswc());
                                        if(CollectionUtils.isNotEmpty(workExploitationPerformanceList1)){
                                            listOverseasFileWorkMapping.setIsrc(workExploitationPerformanceList1.stream().map(WorkExploitationPerformance::getIsrc).collect(Collectors.joining(",")));
                                        }
                                        listOverseasFileWorkMapping.setOriginalTitle(musicalWorkNotificationTransactionHeader.getOriginalWorkTitle());
                                        listOverseasFileWorkMapping.setSubTitle(musicalWorkNotificationTransactionHeader.getCompositeWorkTitle());
                                        String composeName = musicalWorkInterestedParty.getIpRole();
                                        if(StringUtils.isNotBlank(composeName) && (composeName.equals("C")||composeName.equals("AC"))){
                                            listOverseasFileWorkMapping.setComposerName(composeName);
                                        }
                                        listOverseasFileWorkMapping.setArtistName(workExploitationPerformanceList1.stream().map(workExploitationPerformance1 ->{
                                            String name = "";
                                            String lastName = workExploitationPerformance1.getPerformingArtistLastName();
                                            String firstName = workExploitationPerformance1.getPerformingArtistFirstName();
                                            if(StringUtils.isNotBlank(lastName)){
                                                name = lastName;
                                            } if(StringUtils.isNotBlank(firstName)){
                                                name = name + lastName;
                                            }
                                            return workExploitationPerformance.getPerformingArtistLastName()+workExploitationPerformance.getPerformingArtistFirstName();
                                        }).collect(Collectors.joining(",")));
                                        if(StringUtils.isNotBlank(composeName) && (composeName.equals("A")||composeName.equals("AC"))){
                                            listOverseasFileWorkMapping.setAuthorName(composeName);
                                        }
                                        listOverseasFileWorkMapping.setAuthorComposer(listOverseasFileWorkMapping.getComposerName()+listOverseasFileWorkMapping.getAuthorName());
                                        listOverseasFileWorkMapping.setStatus(0);//待匹配
                                        listOverseasFileWorkMapping.setDistributionCategory(workExploitationPerformance.getDistributionCategory());//19
                                        String dataUniqueKeyStr = listOverseasFileWorkMapping.getSourceWorkCode()+"_"+listOverseasFileWorkMapping.getIswc()+"_"+
                                                listOverseasFileWorkMapping.getReceiptSociety()+"_"+listOverseasFileWorkMapping.getReferenceNumber();
                                        listOverseasFileWorkMapping.setDataUniqueKeyStr(dataUniqueKeyStr);
                                        listOverseasFileWorkMapping.setDataUniqueKey(Md5.getMd5(dataUniqueKeyStr));
                                        listOverseasFileWorkMapping.setRemitDistNo(societyDistributionNotificationTransactionHeader.getRemittingSocietyDistributionIdentifier());
        //                                listOverseasFileWorkMapping.setReferenceNumber();//FIXME
                                        listOverseasFileWorkMapping.setIpSocietyCode(musicalWorkInterestedParty.getIpSociety());
                                        List<InterestedPartyInformation> interestedPartyInformation2 = interestedPartyInformationList1.stream().
                                                filter(interestedPartyInformation->interestedPartyInformation.getInterestedPartyNumber() != null && interestedPartyInformation.getInterestedPartyNumber().equals(musicalWorkInterestedParty.getInterestedPartyNumber())).collect(Collectors.toList());
                                        if(CollectionUtils.isNotEmpty(interestedPartyInformation2)){
                                            InterestedPartyInformation interestedPartyInformation = interestedPartyInformation2.get(0);
                                            listOverseasFileWorkMapping.setIpName(interestedPartyInformation.getIpName());
                                        }
                                        listOverseasFileWorkMapping.setIpNameNo(musicalWorkInterestedParty.getInterestedPartyNumber());
                                        listOverseasFileWorkMapping.setWorkIpRole(musicalWorkInterestedParty.getIpRole());
                                        BigDecimal shareRatio = new BigDecimal(0);
                                        if(musicalWorkInterestedParty.getShareDenominator() != null){
                                            shareRatio = new BigDecimal(musicalWorkInterestedParty.getShareNumerator()*100).divide(new BigDecimal(musicalWorkInterestedParty.getShareDenominator()));
                                            listOverseasFileWorkMapping.setShareRatio(shareRatio);//FIXME
                                        }
                                        listOverseasFileWorkMapping.setAmount(new BigDecimal(workExploitationPerformance.getGrossRoyaltyAmount()).multiply(shareRatio));//FIXME
                                        listOverseasFileWorkMapping.setTerritoryCode(musicalWorkDistributionStatusTerritory.getInterestTerritoryTisn()+"");
                                        listOverseasFileWorkMapping.setTerritoryName(musicalWorkDistributionStatusTerritory.getInterestTerritoryTisan());//37
                                        listOverseasFileWorkMapping.setPoolCode(DistributionCategoryMenu.getValueByCode(workExploitationPerformance.getDistributionCategory()));
                                        listOverseasFileWorkMapping.setShareDenominator(new BigDecimal(musicalWorkInterestedParty.getShareDenominator() == null?0:musicalWorkInterestedParty.getShareDenominator()));
                                        listOverseasFileWorkMapping.setNumberOfPerformances(1);//播放次数
                                        listOverseasFileWorkMappings.add(listOverseasFileWorkMapping);
                                    });
                                }
                            });
                        }
                    });
                    BigDecimal nomalAmount = listOverseasFileWorkMappings.stream().map(ListOverseasFileWorkMapping::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    listOverseasFileBase.setNomalAmount(nomalAmount);
                    listOverseasFileBase.setTotalAmount(nomalAmount);
                       */
/* System.out.println(toString(listOverseasFileWorkMappings));
                        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream("C:\\Users\\<USER>\\Desktop\\CRD\\listOverseasFileWorkMappings.txt"));
                        outputStreamWriter.write(toString(listOverseasFileWorkMappings));
                        outputStreamWriter.flush();
                        outputStreamWriter.close();*//*

                }
            }




        }
        }

        public String toString(Object object) {
            return JSON.toJSONString(object, new SerializerFeature[] {
                    SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteNullListAsEmpty,
                    SerializerFeature.WriteNullStringAsEmpty,
                    SerializerFeature.WriteNullNumberAsZero,
                    SerializerFeature.WriteNullBooleanAsFalse,
                    SerializerFeature.UseISO8601DateFormat });
        }
*/



}
