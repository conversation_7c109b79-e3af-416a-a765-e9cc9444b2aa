package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description:
 * @author: handa
 * @time: 2019/12/6 9:57
 */
@Table(name = "dist_param_number")
public class DistParamNumber extends BaseEntity {

    @Column(name = "dist_no")
    private String distNo;

    @Column(name = "dist_describe")
    private String distDescribe;

    @Column(name = "dist_year")
    private String distYear;

    @Column(name = "dist_type")
    private String distType;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getDistDescribe() {
        return distDescribe;
    }

    public void setDistDescribe(String distDescribe) {
        this.distDescribe = distDescribe;
    }

    public String getDistYear() {
        return distYear;
    }

    public void setDistYear(String distYear) {
        this.distYear = distYear;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType;
    }

    public DistParamNumber(String distNo, String distDescribe) {
        this.distNo = distNo;
        this.distDescribe = distDescribe;
        this.distType= distNo.substring(0,1);
        this.distYear = "20" + distNo.substring(1,3);
    }

    public DistParamNumber(String distNo) {
        this.distNo = distNo;
    }

    public DistParamNumber() {
    }
}
