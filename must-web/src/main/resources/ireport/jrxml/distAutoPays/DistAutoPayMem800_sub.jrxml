<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="800_sub1" pageWidth="900" pageHeight="600" columnWidth="900" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="c5162ba3-a6b8-4eae-9bad-7dbe2f8cbf62">
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="paName" class="java.lang.String"/>
	<field name="payableTo" class="java.lang.String"/>
	<field name="royalties" class="java.lang.String"/>
	<field name="payAbleAmount" class="java.lang.String"/>
	<field name="distNoLists" class="java.lang.String"/>
	<field name="payee" class="java.lang.String"/>
	<field name="paNameNo" class="java.lang.String"/>
	<field name="handlingCharge" class="java.lang.String"/>
	<field name="paymentDesc" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="180" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="110" height="28" uuid="75927d12-8496-4809-a3e8-99939f984db9"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="315" y="0" width="90" height="30" uuid="470fe1e6-da76-4f80-93be-8b4e394933eb"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $F{royalties}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="475" y="0" width="60" height="30" uuid="e27b87fc-8a4f-4dd9-a22c-e82282d59f69"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $F{payAbleAmount}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="10" y="38" width="440" height="142" uuid="1b40b38b-f466-431a-ad86-749da8464a82"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distNoLists")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPayMem800_child.jasper")]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="210" y="0" width="100" height="30" uuid="710bda3d-93c0-4455-8109-ef811aae2940"/>
				<textFieldExpression><![CDATA[$F{paNameNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="410" y="0" width="60" height="30" uuid="479bbadc-8ec6-4a01-8c7a-851e31220a23"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $F{handlingCharge}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="695" y="5" width="40" height="23" uuid="c3d99903-253d-40cb-a2ad-1f45d2c29abd"/>
				<text><![CDATA[PAYEE :]]></text>
			</staticText>
			<staticText>
				<reportElement x="695" y="30" width="80" height="20" uuid="a7b0095c-eb47-4eb2-ac48-9a100d2ec343"/>
				<text><![CDATA[DESCRIPTION:]]></text>
			</staticText>
			<textField>
				<reportElement x="775" y="28" width="140" height="22" uuid="5275a28d-1e10-4f54-a8e1-ef3b35c6a788"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paymentDesc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="735" y="5" width="160" height="22" uuid="7d02a5c6-dfe5-4c38-887c-63f41e4f4cbb"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{payee}]]></textFieldExpression>
			</textField>
		</band>
		<band height="19">
			<line>
				<reportElement x="0" y="10" width="900" height="1" uuid="30f6d69d-ca2f-4fce-8351-c74569838997"/>
			</line>
		</band>
	</detail>
</jasperReport>
