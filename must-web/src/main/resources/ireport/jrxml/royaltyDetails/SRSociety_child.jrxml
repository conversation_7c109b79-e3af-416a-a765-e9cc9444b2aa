<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SRSociety_sub" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="780adfea-e91a-4aec-9b32-a499a5ea9f87">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="IP" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="dist" class="java.lang.String"/>
	<field name="correct" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="38" splitType="Stretch">
			<textField>
				<reportElement x="480" y="0" width="115" height="30" uuid="6ee482d2-90d2-4f59-8d1e-c72e3db15dc0"/>
				<textFieldExpression><![CDATA[$F{correct}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="280" height="30" uuid="a7bbaac9-a463-4218-a9dc-08d2fc18f737"/>
				<textFieldExpression><![CDATA[$F{IP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="0" width="100" height="30" uuid="18f64e9f-0191-4dcc-9166-fe110e8ebd80"/>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="0" width="100" height="30" uuid="2f09642a-ae72-46f0-8c4b-edd55ae928bc"/>
				<textFieldExpression><![CDATA[$F{dist}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
