package tw.org.must.must.core.parse.spotify.tsv.entity;

import tw.org.must.must.core.parse.spotify.tsv.entity.BaseTsvEntity;

public class Footer  extends BaseTsvEntity {

    private String NumberOfLinesInFile;

    private String NumberOfLinesInReport;

    private String NumberOfSummaryRecords;

    private String NumberOfBlocksInFile;

    private String NumberOfBlocksInReport;



    public String getNumberOfLinesInFile() {
        return NumberOfLinesInFile;
    }

    public void setNumberOfLinesInFile(String numberOfLinesInFile) {
        NumberOfLinesInFile = numberOfLinesInFile;
    }

    public String getNumberOfLinesInReport() {
        return NumberOfLinesInReport;
    }

    public void setNumberOfLinesInReport(String numberOfLinesInReport) {
        NumberOfLinesInReport = numberOfLinesInReport;
    }

    public String getNumberOfSummaryRecords() {
        return NumberOfSummaryRecords;
    }

    public void setNumberOfSummaryRecords(String numberOfSummaryRecords) {
        NumberOfSummaryRecords = numberOfSummaryRecords;
    }

    public String getNumberOfBlocksInFile() {
        return NumberOfBlocksInFile;
    }

    public void setNumberOfBlocksInFile(String numberOfBlocksInFile) {
        NumberOfBlocksInFile = numberOfBlocksInFile;
    }

    public String getNumberOfBlocksInReport() {
        return NumberOfBlocksInReport;
    }

    public void setNumberOfBlocksInReport(String numberOfBlocksInReport) {
        NumberOfBlocksInReport = numberOfBlocksInReport;
    }
}
