package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`wrk_work_other_soc_code`")
public class WrkWorkOtherSocCode extends BaseEntity {

    /**
     * 作品協會編號
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 作品編號
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 來源公司或者協會名稱
     */
    @Column(name = "`source_name`")
    private String sourceName;

    /**
     * 同步過來的公司編號,可以是協會編號，可以填寫ip base no，或者soc code
     */
    @Column(name = "`source_code`")
    private String sourceCode;

    /**
     * 協會作品編號，同步過來的協會
     */
    @Column(name = "`source_work_id`")
    private String sourceWorkId;

    /**
     * ref_source表,數據來源類別
     */
    @Column(name = "`source_type`")
    private String sourceType;

    /**
     * 
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    /**
     * 
     */
    @Column(name = "`create_user_id`")
    private Long createUserId;

    /**
     * 
     */
    @Column(name = "`create_user_name`")
    private String createUserName;

    @Transient
    private String action;

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getSourceWorkId() {
        return sourceWorkId;
    }

    public void setSourceWorkId(String sourceWorkId) {
        this.sourceWorkId = sourceWorkId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}