package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListFileQueue;

import java.util.List;

@Repository
public interface ListFileQueueMapper extends BaseMapper<ListFileQueue> {


    List<ListFileQueue> getListFileQueueList2(@Param("categoryCode") String categoryCode, @Param("fileName") String fileName, @Param("fileUploadStartDate") String fileUploadStartDate,
                                              @Param("fileUploadEndDate") String fileUploadEndDate, @Param("uploadType") String uploadType, @Param("status") Integer status,
                                              @Param("fileType") String fileType, @Param("isNoCategory") Integer isNoCategory, @Param("filePath") String filePath, @Param("usageTime") String usageTime);

    List<ListFileQueue> getListFileQueueListWithClaimBase(@Param("categoryCode") String categoryCode, @Param("fileName") String fileName, @Param("fileUploadStartDate") String fileUploadStartDate,
                                                          @Param("fileUploadEndDate") String fileUploadEndDate, @Param("uploadType") String uploadType, @Param("status") Integer status,
                                                          @Param("fileType") String fileType, @Param("isNoCategory") Integer isNoCategory, @Param("filePath") String filePath,
                                                          @Param("usageTime") String usageTime);

}