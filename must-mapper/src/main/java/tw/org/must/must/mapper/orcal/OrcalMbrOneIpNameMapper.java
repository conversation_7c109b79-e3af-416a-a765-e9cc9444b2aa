package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalMbrOneIpName;

@Repository
public interface OrcalMbrOneIpNameMapper {

	List<OrcalMbrOneIpName> getMbrOneIpNameByIpBaseNo(String ipBaseNo);

    List<OrcalMbrOneIpName> getMbrOneIpNameByIpNameNo(String ipNameNo);

    List<OrcalMbrOneIpName> getMbrOneIpNameByIpNameNoList(List<String> ipNameNoList);
}
