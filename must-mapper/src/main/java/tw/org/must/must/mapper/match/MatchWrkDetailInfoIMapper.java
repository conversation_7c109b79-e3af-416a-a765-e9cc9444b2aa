package tw.org.must.must.mapper.match;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWrkDetailInfoI;
import tw.org.must.must.model.match.vo.MatchWrkDetailInfoIVo;

import java.util.List;

@Repository
public interface MatchWrkDetailInfoIMapper extends BaseMapper<MatchWrkDetailInfoI> {

	Integer insertOrUpdateByList(List<MatchWrkDetailInfoI> matchWrkDetailInfoIList);

	Long countByStatus(List<Integer> statusList);

	List<MatchWrkDetailInfoIVo> getMatchWrkDetailInfoIByUniqueKeyMd5(String uniqueKeyMd5);

	List<MatchWrkDetailInfoI> getMatchWrkDetaiInfoByStatusAndLimit(int count, int index);


}