package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataDsp;

import java.util.List;
import java.util.Map;

@Repository
public interface ListMatchDataDspMapper extends BaseMapper<ListMatchDataDsp> {
    Map<String, Integer> calculateMatchUnMatchCount(@Param("fileBaseId") Long fileBaseId);

    List<Map<String, Integer>> calculateMatchUnMatchCountMap(@Param("fileBaseIds") List<Long> fileBaseIds);

    Integer insertDuplicate(List<ListMatchDataDsp> listMatchDataDspList);

    @Select("SELECT dsp.* FROM list_match_data_dsp dsp inner join list_dsp_file_base base " +
            "on dsp.file_base_id = base.id WHERE data_unique_key = #{dataUniqueKey} AND base.is_dist = 0")
    List<ListMatchDataDsp> getListMatchDataDspDist(@Param(value = "dataUniqueKey") String dataUniqueKey);

    Integer updateListMatchDataDspFromHistory(@Param("listMatchDataDsp") ListMatchDataDsp listMatchDataDsp, @Param("list") List<Long> list) ;

    @Select("select * from ${tableName} where file_base_id = #{fileBaseId} and id > #{id} order by id limit 10000" )
    List<ListMatchDataDsp> getListMatchDataDsp(@Param(value = "tableName") String tableName, @Param(value = "fileBaseId") Long fileBaseId,@Param(value = "id") Long id);


//    Integer insertBatchHistory(String tableName,List<ListMatchDataDsp> list);


}