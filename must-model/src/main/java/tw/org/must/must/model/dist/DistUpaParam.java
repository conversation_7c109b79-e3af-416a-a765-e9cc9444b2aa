package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "`dist_upa_param`")
public class DistUpaParam extends BaseEntity {

    /**
     * 
     */
    @Column(name = "local_default_amount")
    private BigDecimal localDefaultAmount;

	/**
	 *
	 */
	@Column(name = "local_total_amount")
	private BigDecimal localTotalAmount;

	@Column(name = "`overseas_total_amount`")
	private BigDecimal overseasTotalAmount;

	@Column(name = "`roy_average`")
	private BigDecimal royAverage;

	@Column(name = "`local_dist_amount`")
	private BigDecimal localDistAmount;

	@Column(name = "`overseas_dist_amount`")
	private BigDecimal overseasDistAmount;

	/**
     * 
     */
    @Column(name = "overseas_ratio")
    private BigDecimal overseasRatio;

	@Column(name = "diff")
	private BigDecimal diff;


    /**
     * 
     */
    @Column(name = "dist_param_info_id")
    private Long distParamInfoId;
    
    @Column(name = "dist_no")
    private String distNo;


    public BigDecimal getLocalDefaultAmount() {
		return localDefaultAmount;
	}

	public void setLocalDefaultAmount(BigDecimal localDefaultAmount) {
		this.localDefaultAmount = localDefaultAmount;
	}

	public BigDecimal getOverseasRatio() {
		return overseasRatio;
	}

	public void setOverseasRatio(BigDecimal overseasRatio) {
		this.overseasRatio = overseasRatio;
	}

	public BigDecimal getLocalTotalAmount() {
		return localTotalAmount;
	}

	public void setLocalTotalAmount(BigDecimal localTotalAmount) {
		this.localTotalAmount = localTotalAmount;
	}

	public BigDecimal getOverseasTotalAmount() {
		return overseasTotalAmount;
	}

	public void setOverseasTotalAmount(BigDecimal overseasTotalAmount) {
		this.overseasTotalAmount = overseasTotalAmount;
	}

	public Long getDistParamInfoId() {
        return distParamInfoId;
    }

    public void setDistParamInfoId(Long distParamInfoId) {
        this.distParamInfoId = distParamInfoId;
    }

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public BigDecimal getRoyAverage() {
		return royAverage;
	}

	public void setRoyAverage(BigDecimal royAverage) {
		this.royAverage = royAverage;
	}

	public BigDecimal getLocalDistAmount() {
		return localDistAmount;
	}

	public void setLocalDistAmount(BigDecimal localDistAmount) {
		this.localDistAmount = localDistAmount;
	}

	public BigDecimal getOverseasDistAmount() {
		return overseasDistAmount;
	}

	public void setOverseasDistAmount(BigDecimal overseasDistAmount) {
		this.overseasDistAmount = overseasDistAmount;
	}

	public BigDecimal getDiff() {
		return diff;
	}

	public void setDiff(BigDecimal diff) {
		this.diff = diff;
	}
}