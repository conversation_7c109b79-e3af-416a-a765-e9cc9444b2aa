package tw.org.must.must.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

@Service
public class NoGener {

    private static RedisTemplate<String,Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        NoGener.redisTemplate = redisTemplate;
    }

    public static long getIncrement(String key) {
        if (redisTemplate.getConnectionFactory() == null) {
            throw new RuntimeException("redisTemplate injection failure !");
        }
        RedisAtomicLong noCounter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        return noCounter.incrementAndGet();
    }

    /**
     *  从redis根据key生成序列，序列长度如果不足，前面补零 。自动添加redis key作为前缀
     * @param key
     * @param length
     * @return
     */
    public static String getSequence(String key, int length) {
    	
       return getSequence(key,length,key);
       
    }
    
    
    
    /**
     * 从redis根据key生成序列，序列长度如果不足，前面补零
     * @param key             序列名称
     * @param length         序列长度
     * @param prefixStr     指定序列前缀，不计入长度
     * @return
     */
    public static String getSequence(String key,int length,String prefixStr) {
    	  long seq = getIncrement(key);
          String str = String.valueOf(seq);
          if (str.length() > length) {
              return str;
          }
          int zeroNum = length - str.length();
          StringBuilder sb = new StringBuilder();
          if(prefixStr != null && !prefixStr.isEmpty()) {
        	  sb.append(prefixStr);
          }
          for (int i = 0; i < zeroNum; i++) {
              sb.append('0');
          }
          sb.append(str);
          return sb.toString();
    	
    }
    
    /**
     * 从redis根据key生成序列，序列长度如果不足，前面补零
     * @param key             序列名称
     * @param length         序列长度
     * @param prefixStr     指定序列前缀，不计入长度
     * @return
     */
    public static String getMaxLengthSequence(String key,int length,String prefixStr) {
    	  long seq = getIncrement(key);
          String str = String.valueOf(seq);
          
          int zeroNum = length - str.length();
          StringBuilder sb = new StringBuilder();
          if(prefixStr != null && !prefixStr.isEmpty()) {
        	  sb.append(prefixStr);
          }
          for (int i = 0; i < zeroNum; i++) {
              sb.append('0');
          }
          sb.append(str);
          return sb.toString();
    	
    }
    
    // 根据总长度length - start 进行最终组合
//    public static String getWrkSequence(String key,int length) {
//        long seq = getIncrement(key);
//    	String str = String.valueOf(seq);
//    	if (str.length() > length) {
//    		return str;
//    	}
//    	int zeroNum = length - str.length();
//    	StringBuilder sb = new StringBuilder();
//    	for (int i = 0; i < zeroNum; i++) {
//    		sb.append('0');
//    	}
//    	sb.append(str);
//    	return sb.toString();
//    }
}
