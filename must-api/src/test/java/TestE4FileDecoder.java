import com.firstbrave.api.base.E4Record;
import com.firstbrave.api.e4.vo.E4Trailer;
import com.firstbrave.api.parser.E4FileDecoder;
import com.firstbrave.api.util.ParserUtil;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.LinkedList;
import java.util.List;

public class TestE4FileDecoder {



    public static void main(String[] args) throws IOException, NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        String file = "C:\\Users\\<USER>\\Desktop\\SUISA_O181.E4";
        file = "C:\\Users\\<USER>\\Desktop\\AKM_O182.F2";
        file = "C:\\Users\\<USER>\\Desktop\\解析\\E4_F2\\ACDAM_I172.E4";
        List<String> list = Files.readAllLines(Paths.get(file));
        LinkedList<String> lines = new LinkedList<>(list);
         StringBuffer errorMsg = new StringBuffer();
        List<E4Record> e4Records = E4FileDecoder.decode(errorMsg,lines);

        for (E4Record e4Record : e4Records) {
           if (e4Record instanceof E4Trailer) {
                String code = ParserUtil.encodeByChar(e4Record);
                System.out.println(code);
            }
        }

        /*String encode = encoder.encode(decoder.getGroupHeader());
        System.out.println(encode);

        encode = encoder.encode(decoder.getGroupTrailer());
        System.out.println(encode);

        encode = encoder.encode(decoder.getRoyaltyDistributions().get(0));
        System.out.println(encode);*/
    }
}
