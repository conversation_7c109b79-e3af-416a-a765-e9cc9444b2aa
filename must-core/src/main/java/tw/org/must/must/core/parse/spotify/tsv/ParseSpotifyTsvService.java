package tw.org.must.must.core.parse.spotify.tsv;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.enums.DeletedEnum;
import tw.org.must.must.common.enums.IsShowEnum;
import tw.org.must.must.common.util.ConvertUtils;
import tw.org.must.must.core.parse.DspDataTemp;
import tw.org.must.must.core.parse.spotify.tsv.entity.*;
import tw.org.must.must.core.parse.spotify.tsv.entity.Footer;
import tw.org.must.must.core.parse.spotify.tsv.entity.Header;
import tw.org.must.must.core.parse.spotify.tsv.entity.TsvCommonObject;
import tw.org.must.must.core.service.claim.ClaimMaxRevenueService;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.dsp.DspListUniqueKeyHandler;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListFileQueue;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_1000;

/**
 * 解析 spotify   tsv文件 (optimization)
 *
 * <AUTHOR>
 */
@Service
public class ParseSpotifyTsvService extends DspListUniqueKeyHandler {

    Logger logger = LoggerFactory.getLogger(ParseSpotifyTsvService.class);

    List<SY0101> sy0101List = new ArrayList<>();

    Header header;
    Map<String, Long> connectListDspFileBaseMap = new HashMap<>();
    boolean isParse = false;
    String nowBlockId = "";
    BigDecimal totalSy0101Usage = BigDecimal.ZERO;
    BigDecimal totalRu0101Usage = BigDecimal.ZERO;
    List<TsvCommonObject> tsvCommonObjectList = new ArrayList<>();
    Footer footer;

    Map<Long,ListDspFileBase> baseMap = new HashMap<>();

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ListDspFileDataMappingService listDspFileDataMappingService;

    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;
    private ThreadLocal<TsvCommonObject> tsvCommonObjectThreadLocal = new ThreadLocal<>();
    private ListFileQueue listFileQueue;

    private static void dealWithString(String line, Map<String, String> map) {
        String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
        split[0] = split[0].replace("#", "");
        map.put(split[0], line);
    }

    @Transactional
    public void parse(ListFileQueue lfq) throws Exception {
        init();
        listFileQueue = lfq;
        parse(lfq.getFilePath());
       /* //解析完， 同步base表中mapping的数量
        ListDspFileBase update = new ListDspFileBase();
        listDspFileBaseService.update(listDspFileBase);*/
        /*List<ListDspFileBase> listDspFileBases = listDspFileBaseService.getListDspFileBaseByQueueId(lfq.getId());
        if (!listDspFileBases.isEmpty()) {
            listDspFileDataMappingService.updateFileBaseCountByDspFileBaseInfo(listDspFileBases, fileBaseIdTotalClickNumMap);
        }*/
    }

    /**
     * Spotify  解析tsv类型文件
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常
     */
    public void parse(String filepath) {
        LineIterator lineIterator = null;
        Map<String, String> headerMap = new HashMap<>();
        List<String> lines = new ArrayList<>();
        String result = "";
        boolean isBreak = false;
        //标志位，用于标识每一个AS01.01
        Boolean flag = false;
        int i = 0;
        try {
            Map<Long, String> fileBaseIdMap = new HashMap<>(); // <fileBaseId, tableName>;
            Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap = new HashMap<>(); // <fileBaseId, <mapping.id, mapping.clickNumber>>
            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            while (lineIterator.hasNext()) {
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                if(logger.isDebugEnabled()){
                    logger.debug("========处理第{}行, line: {}", i, line);
                }


                flag = this.dealLines(line, headerMap, flag, lines);

                // 每1000行入庫
                if (flag && tsvCommonObjectList.size() >= 1000) {
                    result = saveDspFileBase(filepath);
                    if (StringUtils.isNotBlank(result)) {
                        XxlJobLogger.log("解析异常~" + result);
                        listFileQueue.setStatus(3);
                        listFileQueue.setDescription(result);
                        tsvCommonObjectList.clear();
                        isBreak = true;
                        break;
                    }
                    saveDspFileDataMapping(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                    tsvCommonObjectList.clear();

                }
            }
            // 最後剩餘數據入庫
            if (tsvCommonObjectList.size() > 0) {
                saveDspFileDataMapping(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                tsvCommonObjectList.clear();
            }

            // 数据排序后同步到正式表，删除临时表
            if (fileBaseIdMap.size() > 0) {
                CountDownLatch countDownLatch = new CountDownLatch(fileBaseIdMap.size());
                ReentrantLock lock = new ReentrantLock(true); // 公平锁，写锁（排他锁），用来顺序（唯一）插入mapping数据

                long startTime = System.currentTimeMillis();
                for (Long fileBaseId : fileBaseIdMap.keySet()) {
                    List<DspDataTemp> dspDataTemps = fileBaseIdPlayCountMap.getOrDefault(fileBaseId,new ArrayList<>());
                    if (CollectionUtils.isEmpty(dspDataTemps)) {
                        countDownLatch.countDown();
                        continue;
                    }
                    String tableName = fileBaseIdMap.get(fileBaseId);
                    // 异步方法里不共用事务
                    CompletableFuture.runAsync(() -> { // 该方法默认线程池大小是当前电脑核数 - 1
                        // TODO: 2021-08-02 huyong: 如果该任务卡主，可能是该方法异步线程出错不会报错的，偶发性问题，需要使用自定义线程池，同时中断其他线程并向上抛出异常，可参考 syncOfficialTable 方法
                        long listSize = 0;
                        long a = System.currentTimeMillis();
                        List<Long> collect = dspDataTemps.stream().sorted(Comparator.comparing(DspDataTemp::getWorkPrice, Comparator.reverseOrder()).thenComparing(DspDataTemp::getClickNumber, Comparator.reverseOrder())).map(DspDataTemp::getId).collect(Collectors.toList());
                        BigDecimal clickNumber = dspDataTemps.stream().map(DspDataTemp::getClickNumber).reduce(BigDecimal.ZERO,BigDecimal::add);
//                        BigDecimal netRevenue = dspDataTemps.stream().map(DspDataTemp::getWorkPrice).reduce(BigDecimal.ZERO,BigDecimal::add);
                        long b = System.currentTimeMillis() - a;
                        logger.info("临时表【{}】，workPrice, clickNumber倒叙排序耗时：【{}】", tableName, b > 1000 ? (b / 1000) + " 秒" : b + " 毫秒");
                        List<List<Long>> partition = Lists.partition(collect, BATCH_SIZE_1000);
                        for (List<Long> ids : partition) {
                            XxlJobLogger.log("临时表【{}】，查询id个数：【{}】", tableName, ids.size());
                            List<ListDspFileDataMapping> listDspFileDataMappings = listDspFileDataMappingService.selectTemporaryByIds(ids, tableName);
                            try {
                                lock.lock();
                                XxlJobLogger.log("开始插入正式表，fid:【{}】, size:【{}】", fileBaseId, listDspFileDataMappings.size());
                                listDspFileDataMappingService.addList(listDspFileDataMappings);
                                listSize += listDspFileDataMappings.size();
                            } catch (Exception e) {
                                XxlJobLogger.log("tzk001,msg: ");
                                XxlJobLogger.log(e);
                            } finally {
                                lock.unlock();
                            }
                        }
                        // 跟新base的关于mapping的个数
                        ListDspFileBase base = new ListDspFileBase();
                        base.setId(fileBaseId);
                        base.setListFileTotal(listSize);
                        base.setClickNumber(clickNumber);
//                        base.setListTotalRoy(netRevenue);
                        base.setFileStatus(1);
                        base.init();
                        XxlJobLogger.log("更新base: {}", JSON.toJSONString(base));
                        listDspFileBaseService.updateSelective(base);
                        XxlJobLogger.log("更新base完成: {}", JSON.toJSONString(base));
                        countDownLatch.countDown();
                        XxlJobLogger.log("临时表【{}】end，countDownLatch个数：【{}】", tableName, countDownLatch.getCount());
                    });
                }
                countDownLatch.await();
                XxlJobLogger.log("countDownLatch end");
                while (lock.isLocked()) {
                    //自旋，等待最后一个公平锁释放
                }
                for (String tableName : fileBaseIdMap.values()) {
                    // 需要等待异步线程执行完后，才能删除表，否则会出现查询的时候表没了，导致程序卡死
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】", tableName);
                    listDspFileDataMappingService.dropTemporaryTable(tableName);
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】成功！", tableName);
                }
                XxlJobLogger.log("lock end");
                long consuming = System.currentTimeMillis() - startTime;
                XxlJobLogger.log("fid:【{}】, 临时表->正式表完成, time consuming：【{}】", fileBaseIdMap.keySet(), consuming > 1000 ? (consuming / 1000) + " 秒" : consuming + " 毫秒");
            }
            if (!isBreak) {
                listFileQueue.setStatus(2);
                listFileQueue.setDescription("解析完成~~~~");
                //XxlJobLogger.log("totalSy0202Usage is " + totalSy0202Usage);
               // XxlJobLogger.log("totalRu0101Usage is " + totalRu0101Usage);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            XxlJobLogger.log("================== "+ message);
            if (null != message && message.length() > 5000) {
                message = message.substring(0, 4900);
            }
            listFileQueue.setStatus(3);
            if (StringUtils.isNotBlank(result)) {
                listFileQueue.setDescription(String.format("解析出错：%s", result));
            } else {
                listFileQueue.setDescription(String.format("解析出错：%s", message));
            }
            e.printStackTrace();
        } finally {
            LineIterator.closeQuietly(lineIterator);
        }
    }


    /**
     * 测试 dml,ddl,同步异步等多重组合起来的事务问题
     */
    @Transactional
    public void testTranscational() {


        ListDspFileBase b = new ListDspFileBase();
        b.setFileName("1111测试事务");
        b.setListFileTotal(99999L);
        b.init();
        logger.info("更新base: {}", JSON.toJSONString(b));
        listDspFileBaseService.add(b);
        logger.info("更新base完成: {}", JSON.toJSONString(b));

        List<String> tableNames = new ArrayList<>();
        for (long i = 999901; i < 999904; i++) {
            String temporaryTable = listDspFileDataMappingService.createTemporaryTable(i);
            tableNames.add(temporaryTable);
            for (int j = 0; j < 2; j++) {
                ListDspFileDataMapping mapping = new ListDspFileDataMapping();
                mapping.init();
                mapping.setFileBaseId(i);
                mapping.setTitle(j + " 测试事务问题");
                listDspFileDataMappingService.addListTemporary(temporaryTable, Arrays.asList(mapping));
            }
        }
        System.out.println("插入完成");

        for (String tableName : tableNames) {
            for (int j = 2; j < 4; j++) {
                ListDspFileDataMapping mapping = new ListDspFileDataMapping();
                mapping.init();
                mapping.setFileBaseId(10000L);
                mapping.setTitle(j + " 测试事务问题");
                listDspFileDataMappingService.addListTemporary(tableName, Arrays.asList(mapping));
            }
        }

        System.out.println("再次插入完成");

        Page page = new Page(1, 10);
        for (String tableName : tableNames) {
            List<ListDspFileDataMapping> fileDataMappings = listDspFileDataMappingService.selectListTemporaryOrderClikcNumberDescPage(page, tableName);
            System.out.println("同步查询结果：" + tableName);
            System.out.println(JSON.toJSONString(fileDataMappings));
        }
        ReentrantLock lock = new ReentrantLock();
        CompletableFuture.supplyAsync(() -> {
            lock.lock();
            List<Long> result = new ArrayList<>();
            for (String tableName : tableNames) {
                List<ListDspFileDataMapping> fileDataMappings = listDspFileDataMappingService.selectListTemporaryOrderClikcNumberDescPage(page, tableName);
                result.addAll(fileDataMappings.stream().map(ListDspFileDataMapping::getId).collect(Collectors.toList()));
                System.out.println("异步查询结果：" + tableName);
                System.out.println(JSON.toJSONString(fileDataMappings));
            }

            ListDspFileBase base = new ListDspFileBase();
            base.setFileName("测试事务");
            base.setListFileTotal(10000L);
            base.init();
            logger.info("更新base: {}", JSON.toJSONString(base));
            listDspFileBaseService.add(base);
            logger.info("更新base完成: {}", JSON.toJSONString(base));

            return result;
        }).thenAccept(t -> {
            System.out.println("异步结束，结果： " + JSON.toJSONString(t));
            lock.unlock();
        });
        System.out.println("同步 end");
        while (lock.isLocked()) {
            System.out.println("++++++++++等待锁释放+++++++");
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        for (String tableName : tableNames) {
            System.out.println("drop表 " + tableName);
            listDspFileDataMappingService.dropTemporaryTable(tableName);
            System.out.println("drop表 " + tableName + "成功！");
        }
        System.out.println("******************** ALL ENB ********************");
    }

    /**
     * @param line      当前解析的内容（标题头+数据）
     * @param headerMap 标题头
     * @param flag      标志位，用于标识每一个AS01.01, 通过异或来判断一条数据的开闭
     * @param lines     之前未解析的内容（数据）
     * @return
     */
    protected Boolean dealLines(String line, Map<String, String> headerMap, Boolean flag, List<String> lines) {
        if (line.startsWith("#")) {
            //解析标题，全部解析完再去解析内容
            dealWithString(line, headerMap);
        } else {
            lines.add(line);
            if (headerMap.isEmpty() || headerMap.size() < 3) {
                //如果格式不变的话，head实在#head前面，先解析#head, #SY09...标题
                //不处理
            } else if (lines.size() > 0) {
                //逐行解析内容
                for (String it : lines) {
                    flag = this.dealLine(it, headerMap, flag);
                }
                lines.clear();
            }
        }
        return flag;
    }

    /**
     * @param line      当前解析的内容（数据）
     * @param headerMap 标题头
     * @param flag      标志位，用于标识每一个AS01.01, 通过异或来判断一条数据的开闭
     * @return
     */
    protected Boolean dealLine(String line, Map<String, String> headerMap, Boolean flag) {
        line = line.replace("\\\\\t", "/").replace("\\\t", "\\").replace("\\", "/");
        String[] lineSplit = StringUtils.splitPreserveAllTokens(line, "\t");
        String header = headerMap.get(lineSplit[0]);
        String[] headerSplit = StringUtils.splitPreserveAllTokens(header, "\t");
        if (lineSplit.length != headerSplit.length) {
            //数据与标题个数不一致，则忽略该行后面的，提示下
            logger.info("=================数据与标题匹配不上======================");
            logger.info("head: " + header);
            logger.info("line: " + line);
        }
        try {
            Map<String, String> map = this.convert2Map(headerSplit, lineSplit);
            return this.parseData(map, flag);
        } catch (Exception e) {
            logger.error("convert error,the error is " + e);
        }
        return flag;
    }

    private Map<String, String> convert2Map(String[] headerSplit, String[] lineSplit) {
        Map<String, String> map = new HashMap<>();
        headerSplit[0] = headerSplit[0].replace("#", "");
        for (int i = 0; i < headerSplit.length; i++) {
            String value = null;
            // 当标题与数据的长度不一致，忽略缺失数据（多余数据）
            if (i < lineSplit.length) {
                value = lineSplit[i];
            }
            map.put(headerSplit[i], value);
        }
        return map;
    }

    /**
     * 解析数据
     *
     * @param map
     * @param flag
     * @return
     */
    private Boolean parseData(Map<String, String> map, Boolean flag) {

        // 处理文件头部		header  一个文件只有一个头
        if (map.containsKey("HEAD")) {
            header = JSON.parseObject(JSON.toJSONString(map), Header.class);
            header.setName("HEAD");
            header.setMessageCreatedDateTime(header.getMessageCreatedDateTime().replace("T", " ").replace("Z", ""));
        }

        //  SY0101节点  汇总信息
        if (map.containsKey("SY01.01")) {
            SY0101 sy0101 = JSON.parseObject(JSON.toJSONString(map), SY0101.class);
            sy0101.setName("SY01.01");
            sy0101List.add(sy0101);
        }

        // 处理块节点 	RE01	BlockId
        if (map.containsKey("RE01")) {
            TsvCommonObject tsvCommonObject = new TsvCommonObject();
            RE01 re01 = JSON.parseObject(JSON.toJSONString(map), RE01.class);
            re01.setName("RE01");
            tsvCommonObject.setRe01(re01);
            tsvCommonObjectThreadLocal.set(tsvCommonObject);
            nowBlockId = re01.getBlockId();

            flag = false ;
        }

        if( map.containsKey("AS02.02")){
            TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
            AS0202 as0202 = JSON.parseObject(JSON.toJSONString(map), AS0202.class);
            if (!nowBlockId.equals(as0202.getBlockId())) {
                //验证下数据是否正确，其实可以删除
                throw new RuntimeException("当前AS02.02与RE01的blockId不一致，数据错误！ RE01:" + JSON.toJSONString(tsvCommonObject.getRe01()) + "; AS02.02: " + JSON.toJSONString(AS0202.class));
            }

            /*Matcher m = Pattern.compile("PT(?<H>\\d+)H(?<M>\\d+)M(?<S>\\d+)S").matcher(map.get("Duration"));
            if (m.find()) {
                as0202.setDurationM(m.group("M"));
                as0202.setDurationS(m.group("S"));
            }*/
            parseDuration(map.get("Duration"),as0202);
            as0202.setName("AS02.02");
            tsvCommonObject.setResourceReference(Integer.valueOf(as0202.getResourceReference()));
            List<AS0202> as0202List = tsvCommonObject.getAs0202List();
            if(as0202List == null){
                as0202List = new ArrayList<>() ;
                tsvCommonObject.setAs0202List(as0202List);
                tsvCommonObjectList.add(tsvCommonObject);
            }
            as0202List.add(as0202);
        }

        //处理块 节点   SU02	BlockId  汇总
        if (map.containsKey("SU02")) {
            SU02 su02 = JSON.parseObject(JSON.toJSONString(map), SU02.class);
            TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
            if (!nowBlockId.equals(su02.getBlockId())) {
                //验证下数据是否正确，其实可以删除
                throw new RuntimeException("当前SU02与RE01的blockId不一致，数据错误！ RE01:" + JSON.toJSONString(tsvCommonObject.getRe01()) + "; SU02: " + JSON.toJSONString(SU02.class));
            }
            su02.setName("SU02");
            List<SU02> su02List = tsvCommonObject.getSu02List();
            if (su02List == null) {
                su02List = new ArrayList<>();
                tsvCommonObject.setSu02List(su02List);
            }
            su02List.add(su02);

            Integer transactedRelease = Integer.valueOf(su02.getTransactedResource());
            if(transactedRelease.equals(tsvCommonObject.getResourceReference())){
                flag = true;
            }

        }

        return flag;

    }

    private String saveDspFileBase(String filepath) throws ParseException {
        String result = "";
        if (isParse) {
            return result;
        }
        String extJson = listFileQueue.getExtJson();
        JSONObject object = JSONObject.fromObject(extJson);
        Long claimSetInfoId = object.getLong("claimSetInfoId");
        if (null == claimSetInfoId) {
            result = "队列表中缺少claimSetid!联系工作人员~";
            return result;
        }
        if (!CollectionUtils.isEmpty(sy0101List)) {

            for(SY0101 sy0101 : sy0101List){
                // 此处可能会出现异常，查询出来多个结果！
                ClaimMinimaInfo spotify = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId(sy0101.getServiceDescription(), claimSetInfoId);
                if (null == spotify) {
                    result = "productShorName为spotify,claimSetInfoId为" + claimSetInfoId + ",没有找到claimMinimaInfo!请先配置~~~";
                    return result;
                }

                // 此处可修改成Map 按照contentCategory分组

                createListDspFileBase(spotify, filepath,sy0101);
                isParse = true;
            }
        }
        return result;
    }


    private void createListDspFileBase(ClaimMinimaInfo spotify, String filepath, SY0101 sy0101) throws ParseException{
        // 此处可能会出现异常，查询出来多个结果！

        Map<String, Object> extJson = new HashMap<>();
        ListDspFileBase listDspFileBase = new ListDspFileBase();
        listDspFileBase.setCurrency(sy0101.getCurrencyOfReporting());
        listDspFileBase.setUseType(sy0101.getUseType());
        // 对应的产品需要固定为某一个，默认为数据的
        listDspFileBase.setListFileStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(header.getUsageStartDate()));
        listDspFileBase.setListFileEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(header.getUsageEndDate()));
//			String fileNumber = header.getFileNumber();
        listDspFileBase.setDspCompany(header.getSenderName());
        listDspFileBase.setDist(0);
        listDspFileBase.setDistStatus(0);
        listDspFileBase.setFilePath(filepath);
        String fileName = listFileQueue.getFileName();
//            String fileName = "DSR_MUST-CS_YouTube_Usage-AdSupport-Music-All_2019-Q4_TW_1of1_20200122T132333.tsv";
        listDspFileBase.setFileName(fileName);
        listDspFileBase.setFileStatus(1);
        listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
        listDspFileBase.setFileQuenceId(listFileQueue.getId());
        listDspFileBase.setDeleted(DeletedEnum.normal.code());
        listDspFileBase.setFileExt(".tsv");
        listDspFileBase.setCommercialModel(sy0101.getCommercialModel());


        listDspFileBase.setListTotalRoy(ConvertUtils.tryStrToDecimal(sy0101.getNetRevenue()));
        String summaryRecordId = sy0101.getSummaryRecordId();

//            listDspFileBase.setFileQuenceId(400l);
        String usages = sy0101.getUsages();
        if (StringUtils.isNotBlank(usages)) {
            totalSy0101Usage = totalSy0101Usage.add(new BigDecimal(usages));
            listDspFileBase.setListTotalClickCount(new BigDecimal(usages));
        }

        String numberOfFiles = header.getNumberofFiles();
        if (StringUtils.isNotBlank(numberOfFiles)) {
            listDspFileBase.setListFileTotal(Long.valueOf(numberOfFiles));
        }
        listDspFileBase.setClaimMinimaInfoId(spotify.getId());
        listDspFileBase.setIsShow(IsShowEnum.show.code());

        String netRevenue = sy0101.getNetRevenue();
        BigDecimal netRevenueValue = BigDecimal.ZERO;
        if(StringUtils.isNotBlank(netRevenue)){
            netRevenueValue = new BigDecimal(netRevenue);
        }
        listDspFileBase.setListTotalRoy(netRevenueValue);
        String subscribers = sy0101.getSubscribers();
        if(StringUtils.isNotBlank(subscribers)){
            listDspFileBase.setListSubscribeCount(new BigDecimal(subscribers));
        }

        listDspFileBase.setProductName(sy0101.getServiceDescription());
        listDspFileBase.setMinimaCalcStatus(0);

        extJson.put("claimSetInfoId", spotify.getId());
        listDspFileBase.setExtJson(JSON.toJSONString(extJson));
        listDspFileBase.setClickNumber(listDspFileBase.getListTotalClickCount());
        listDspFileBase.setMatchMark(listFileQueue.getMatchMark());

        listDspFileBase.init();
        listDspFileBaseService.add(listDspFileBase);
        connectListDspFileBaseMap.put(summaryRecordId, listDspFileBase.getId());
        baseMap.put(listDspFileBase.getId(),listDspFileBase);
    }


    /**
     * 校验排序字段项，防止排序报空指针异常
     *
     * @param listDspFileDataMapping
     */
    protected void checkSortedField(ListDspFileDataMapping listDspFileDataMapping) {
        if (Objects.isNull(listDspFileDataMapping.getClickNumber())) {
            listDspFileDataMapping.setClickNumber(BigDecimal.ZERO);
        }
        if (Objects.isNull(listDspFileDataMapping.getWorkPrice())) {
            listDspFileDataMapping.setWorkPrice(BigDecimal.ZERO);
        }
    }

    private void saveDspFileDataMapping(List<TsvCommonObject> tsvCommonObjectList, Map<Long, String> fileBaseIdMap, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) {
        List<ListDspFileDataMapping> ldfdmList = new ArrayList<>();
        for (TsvCommonObject tsvCommonObject : tsvCommonObjectList) {
            // 一个block下的title和subTitle都需要去重后拼接在一起
            tsvCommonObjectChangeToDspFileDataMapping(ldfdmList, tsvCommonObject);
        }

        if(!CollectionUtils.isEmpty(ldfdmList)){
            Long fileBaseId = ldfdmList.get(0).getFileBaseId() ;
            if (fileBaseIdMap.containsKey(fileBaseId)) {
                int i = listDspFileDataMappingService.addListTemporary(fileBaseIdMap.get(fileBaseId), ldfdmList);
            } else {
                String temporaryTable = listDspFileDataMappingService.createTemporaryTable(fileBaseId);
                fileBaseIdMap.put(fileBaseId, temporaryTable);
                int i = listDspFileDataMappingService.addListTemporary(temporaryTable, ldfdmList);
            }
            List<DspDataTemp> transfer = transfer(ldfdmList);
            List<DspDataTemp> orDefault = fileBaseIdPlayCountMap.getOrDefault(fileBaseId, new ArrayList<>());
            orDefault.addAll(transfer);
            fileBaseIdPlayCountMap.put(fileBaseId, orDefault);
        }
    }

    private void tsvCommonObjectChangeToDspFileDataMapping(List<ListDspFileDataMapping> ldfdmList, TsvCommonObject tsvCommonObject) {
        List<AS0202> as0202List = tsvCommonObject.getAs0202List();
        List<SU02> su02List = tsvCommonObject.getSu02List();
        RE01 re01 = tsvCommonObject.getRe01();
        Map<String,SU02> su02Map = new HashMap<>();
        if(!CollectionUtils.isEmpty(su02List)){
            su02Map = su02List.stream().collect(Collectors.toMap(SU02 :: getSalesTransactionId, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(as0202List)) {
            for (AS0202 as0202 : as0202List) {
                ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
                listDspFileDataMapping.init();
                JSONObject object = new JSONObject();
                createListDspFileDataMappingByAs0202(object, listDspFileDataMapping, as0202);
                // 生成dataUniqueKey  和 dataUniqueKeyStr
                setUniqueKey(listDspFileDataMapping);
                checkSortedField(listDspFileDataMapping);
//                System.out.println(String.format("%s-%s-1",as0202.getBlockId(),as0202.getResourceReference()));
                SU02 su02 = su02Map.get(as0202.getBlockId() + "-" + as0202.getResourceReference() + "-1") ;
                if(su02 != null){
                    String summaryRecordId  = su02.getSummaryRecordId();
                    Long listDspFileBaseId = connectListDspFileBaseMap.get(summaryRecordId);
                    listDspFileDataMapping.setFileBaseId(listDspFileBaseId);
                    String NumberOfStreams = su02.getNumberOfStreams();
                    BigDecimal clickNumber = StringUtils.isBlank(NumberOfStreams) ? BigDecimal.ZERO : new BigDecimal(NumberOfStreams) ;
                    listDspFileDataMapping.setClickNumber(clickNumber);
                    listDspFileDataMapping.setDistRatio(clickNumber);
                    ListDspFileBase ldfb = baseMap.get(listDspFileBaseId);
                    listDspFileDataMapping.setListFileStartTime(ldfb.getListFileStartTime());
                    listDspFileDataMapping.setListFileEndTime(ldfb.getListFileEndTime());
                    String filepath = ldfb.getFilePath();
                    String fileParentPath = filepath.substring(0,filepath.lastIndexOf(File.separator)) ;
                    listDspFileDataMapping.setListParentMd5(DigestUtils.md5DigestAsHex(fileParentPath.getBytes()));
                    object.put("su02", su02);
                }
                listDspFileDataMapping.setReleaseId(re01.getDspReleaseId());

                listDspFileDataMapping.setLabel(setFieldOverLength(re01.getLabel(),object));
                if(listFileQueue.getMatchMark().equals("Y")){
                    listDspFileDataMapping.setStatus(0);
                } else {
                    listDspFileDataMapping.setStatus(5);
                }
                listDspFileDataMapping.setMusicShare(new BigDecimal(100));
                listDspFileDataMapping.setWorkSource("Spotifyddex");

                object.put("re01", re01);
                listDspFileDataMapping.setExtJson(object.toString());

                ldfdmList.add(listDspFileDataMapping);
            }
        }
    }

    private void createListDspFileDataMappingByAs0202(JSONObject object, ListDspFileDataMapping listDspFileDataMapping, AS0202 as0202) {
        listDspFileDataMapping.setGroupCode(as0202.getBlockId());// 用于分组
        listDspFileDataMapping.setIsrc(as0202.getISRC());
        listDspFileDataMapping.setIswc(as0202.getISWC());
        listDspFileDataMapping.setResourceId(as0202.getDspResourceId());
        listDspFileDataMapping.setBusinessId(as0202.getDspResourceId());
        listDspFileDataMapping.setWorkArtist(as0202.getDisplayArtistName());
        listDspFileDataMapping.setComposer(as0202.getComposerAuthor());
        listDspFileDataMapping.setAuthor(as0202.getComposerAuthor());
        listDspFileDataMapping.setDurationStr(as0202.getDuration());
        listDspFileDataMapping.setPublisher(as0202.getMusicPublisher());
        setDuration(as0202,listDspFileDataMapping);
//        listDspFileDataMapping.setDurationM(StringUtils.isNotBlank(as0202.getDurationM()) ? Integer.valueOf(as0202.getDurationM()) : null);
//        listDspFileDataMapping.setDurationS(StringUtils.isNotBlank(as0202.getDurationS()) ? Double.valueOf(as0202.getDurationS()).intValue() : null);
        listDspFileDataMapping.setReleaseType(as0202.getResourceType());
        String as0202Title = as0202.getTitle();
        if (StringUtils.isNotBlank(as0202Title)) {
            listDspFileDataMapping.setTitle(as0202Title);
        }
        String as0202SubTitle = as0202.getSubTitle();
        if (StringUtils.isNotBlank(as0202SubTitle)) {
            listDspFileDataMapping.setAlbumTitle(as0202SubTitle);
        }
        listDspFileDataMapping.setCompany("SPOTIFY");
        listDspFileDataMapping.setProduct(header.getServiceDescription());
        listDspFileDataMapping.setFreeClickNumber(BigDecimal.ZERO);
        listDspFileDataMapping.setMusicShare(new BigDecimal(100));
        listDspFileDataMapping.setUsage(sy0101List.get(0).getUseType());
        object.put("as0202", as0202);
    }

    protected void init() {
        //每次执行前初始化下公用类，防止程序出错，遗留脏数据（由于定时任务执行是设置串行执行的，暂时没有多线程安全问题；后期定时任务执行设置成了并行执行，需要重构）
        sy0101List.clear();
        header = new Header();
        connectListDspFileBaseMap.clear();
        isParse = false;
        nowBlockId = "";
        totalSy0101Usage = BigDecimal.ZERO;
        totalRu0101Usage = BigDecimal.ZERO;
        tsvCommonObjectList.clear();
        footer = new Footer();
        baseMap.clear();
    }

    public void parseDuration(String Duration, AS0202 as0202){
        if(StringUtils.isNotBlank(Duration) && StringUtils.startsWith(Duration,"PT")){
            int index_h = Duration.indexOf("H") , index_m = Duration.indexOf("M"),index_s = Duration.indexOf("S");
            int nextIndex = 2;
            if(index_h > -1){
                as0202.setDurationH(Duration.substring(nextIndex,index_h));
                nextIndex = index_h + 1;
            }
            if(index_m > -1){
                as0202.setDurationM(Duration.substring(nextIndex,index_m));
                nextIndex = index_m + 1 ;
            }
            if(index_s > -1) {
                as0202.setDurationS(Duration.substring(nextIndex,index_s));
            }
        }
    }

    public void setDuration(AS0202 as0202, ListDspFileDataMapping listDspFileDataMapping){
        int durationM = 0 ,durationS = 0;
        if(as0202.getDurationH() != null){
            durationM = Integer.valueOf(as0202.getDurationH()) * 60 ;
        }

        if(as0202.getDurationM() != null){
            durationM = durationM + Integer.valueOf(as0202.getDurationM());
            listDspFileDataMapping.setDurationM(durationM);
        }

        if(as0202.getDurationS() != null){
            durationS = (int)Double.valueOf(as0202.getDurationS()).doubleValue();
            listDspFileDataMapping.setDurationS(durationS);
        }
    }

    public String setFieldOverLength(String field,JSONObject object){
        int length = 200;
        if(StringUtils.isNotBlank(field)){
            byte[] bytes = field.getBytes(StandardCharsets.UTF_8) ;
            if(bytes.length <= length){
                return field;
            }else {
                byte b = bytes[length] ;
                object.put("label",field);
                if(b > 0){ //单字符
                    return new String(bytes,0,length,StandardCharsets.UTF_8);
                } else { //截取完整字符，utf-8
                    int end = length ;
                    for (int i = 0; i < 6 ;i ++ ){ // >=0 单字符; < 0  >= -64 多字符的首字符
                        if(bytes[end] >= -64){
                            break;
                        }
                        end ++ ;
                    }
                    return  new String(bytes,0,end,StandardCharsets.UTF_8);
                }
            }
        }
        return field;
    }

    public String setFieldOverLength(String field){
        int length = 200;
        if(StringUtils.isNotBlank(field)){
            byte[] bytes = field.getBytes(StandardCharsets.UTF_8) ;
            if(bytes.length <= length){
                return field;
            }else {
                byte b = bytes[length] ;
                if(b > 0){ //单字符
                    return new String(bytes,0,length,StandardCharsets.UTF_8);
                } else { //截取完整字符，utf-8
                    int end = length ;
                    for (int i = 0; i < 6 ;i ++ ){ // >=0 单字符; < 0  >= -64 多字符的首字符
                        if(bytes[end] >= -64){
                            break;
                        }
                        end ++ ;
                    }
                    return  new String(bytes,0,end,StandardCharsets.UTF_8);
                }
            }
        }
        return field;
    }

    public ListFileQueue getListFileQueue() {
        return listFileQueue;
    }

    public void setListFileQueue(ListFileQueue listFileQueue) {
        this.listFileQueue = listFileQueue;
    }

    @Override
    public String getCompanyIdentification() {
        return "spotify";
    }

    /**
     * Youtube  解析tsv类型文件
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常h
     */
    public void parseTest(String filepath) {
        LineIterator lineIterator = null;
        Map<String, String> headerMap = new HashMap<>();
        List<String> lines = new ArrayList<>();
        String result = "";
        //标志位，用于标识每一个AS01.01
        Boolean flag = false;
        int i = 0;
        try {
            Map<Long, String> fileBaseIdMap = new HashMap<>(); // <fileBaseId, tableName>;
            Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap = new HashMap<>(); // <fileBaseId, <mapping.id, mapping.clickNumber>>
            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            while (lineIterator.hasNext()) {
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                if(logger.isDebugEnabled()){
                    logger.debug("========处理第{}行, line: {}", i, line);
                }


                flag = this.dealLines(line, headerMap, flag, lines);

                // 每1000行入庫
                if (flag && tsvCommonObjectList.size() >= 1000) {
                    saveDspFileDataMappingTest(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                    tsvCommonObjectList.clear();

                }
            }
            // 最後剩餘數據入庫
            if (tsvCommonObjectList.size() > 0) {
                saveDspFileDataMappingTest(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                tsvCommonObjectList.clear();
            }

        } catch (Exception e) {
//            String message = e.getMessage();
            logger.error("error",e);
        } finally {
            LineIterator.closeQuietly(lineIterator);
        }
    }

    private void saveDspFileDataMappingTest(List<TsvCommonObject> tsvCommonObjectList, Map<Long, String> fileBaseIdMap, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) {
        List<ListDspFileDataMapping> ldfdmList = new ArrayList<>();
        for (TsvCommonObject tsvCommonObject : tsvCommonObjectList) {
            // 一个block下的title和subTitle都需要去重后拼接在一起
            tsvCommonObjectChangeToDspFileDataMapping(ldfdmList, tsvCommonObject);
        }
        List<ListDspFileDataMapping> errorList = ldfdmList.stream().filter(m -> (m.getPerShare() != null && new BigDecimal(1000).compareTo(m.getPerShare()) <1) || (m.getMecShare() != null && new BigDecimal(1000).compareTo(m.getMecShare()) <1)).collect(Collectors.toList());
        errorList.forEach(e -> logger.info("business_id:{},per:{},mec:{}", e.getBusinessId(), e.getPerShare(),e.getMecShare()));
    }
}
