package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Table(name = "`wrk_isrc`")
public class WrkIsrc extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 
     */
    @Column(name = "work_id")
    @NotNull(message="WrkIsrc中的workId不能为空！")
    private Long workId;

    /**
     * 
     */
    @Column(name = "work_society")
    @NotNull(message="WrkIsrc中的workSociety不能为空！")
    private Integer workSociety;

    /**
     * 
     */
    @Column(name = "isrc")
    @NotBlank(message="WrkIsrc中isrc不能为空！")
    private String isrc;

    /**
     * 
     */
    @Column(name = "input_soc")
    private Integer inputSoc;
    
    
    @Column(name = "work_unique_key")
    private String workUniqueKey;

    /**
     * 此处用于同步标识，方便更新操作
     */
    @Transient
    private String transferStatus;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSociety() {
        return workSociety;
    }

    public void setWorkSociety(Integer workSociety) {
        this.workSociety = workSociety;
    }

    public String getIsrc() {
        return isrc;
    }

    public void setIsrc(String isrc) {
        this.isrc = isrc;
    }

    public Integer getInputSoc() {
        return inputSoc;
    }

    public void setInputSoc(Integer inputSoc) {
        this.inputSoc = inputSoc;
    }

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}