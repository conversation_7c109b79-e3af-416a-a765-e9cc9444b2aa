package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Table(name = "`wrk_work_component`")
public class WrkWorkComponent extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 作品编号
     */
    @Column(name = "work_id")
    @NotNull(message="WrkWorkComponent中的workId不能为空！")
    private Long workId;

    /**
     * 作品所属协会
     */
    @Column(name = "work_society_code")
    @NotNull(message="WrkWorkComponent中的workSocietyCode不能为空！")
    private Integer workSocietyCode;

    /**
     * 
     */
    @Column(name = "sub_title_id")
    private Long subTitleId;

    /**
     * 组曲作品编号
     */
    @Column(name = "component_work_id")
    @NotNull(message="componentWorkId不能为空！")
    private Long componentWorkId;

    /**
     * 组曲作品协会
     */
    @Column(name = "com_work_society")
    private Integer comWorkSociety;

    /**
     * 时长，分钟
     */
    @Column(name = "duration_m")
    private Integer durationM;

    /**
     * 时长，秒
     */
    @Column(name = "duration_s")
    private Integer durationS;

    /**
     * 使用类别
     */
    @Column(name = "usage_type")
    @NotBlank(message="comWorkSociety不能为空！")
    private String usageType;

    /**
     * 
     */
    @Column(name = "adj_status_flag")
    private Integer adjStatusFlag;
    
    @Column(name = "work_unique_key")
    private String workUniqueKey;

    @Transient
    private String title;
    
    @Transient
    private String genre;
    
    @Transient
    private String episodeNo;
    
    @Transient
    private String source;
    
    @Transient
    private Date notifyDate;
    
    @Transient
    private String type;
    
    @Transient
    private Date publisherAirDate;

    @Transient
    private Long titleId;

    public String getGenre() {
		return genre;
	}

	public void setGenre(String genre) {
		this.genre = genre;
	}

	public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getSubTitleId() {
        return subTitleId;
    }

    public void setSubTitleId(Long subTitleId) {
        this.subTitleId = subTitleId;
    }

    public Long getComponentWorkId() {
        return componentWorkId;
    }

    public void setComponentWorkId(Long componentWorkId) {
        this.componentWorkId = componentWorkId;
    }

    public Integer getComWorkSociety() {
        return comWorkSociety;
    }

    public void setComWorkSociety(Integer comWorkSociety) {
        this.comWorkSociety = comWorkSociety;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public String getUsageType() {
		return usageType;
	}

	public void setUsageType(String usageType) {
		this.usageType = usageType;
	}

	public Integer getAdjStatusFlag() {
        return adjStatusFlag;
    }

    public void setAdjStatusFlag(Integer adjStatusFlag) {
        this.adjStatusFlag = adjStatusFlag;
    }

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getEpisodeNo() {
		return episodeNo;
	}

	public void setEpisodeNo(String episodeNo) {
		this.episodeNo = episodeNo;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public Date getNotifyDate() {
		return notifyDate;
	}

	public void setNotifyDate(Date notifyDate) {
		this.notifyDate = notifyDate;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getPublisherAirDate() {
		return publisherAirDate;
	}

	public void setPublisherAirDate(Date publisherAirDate) {
		this.publisherAirDate = publisherAirDate;
	}

    public Long getTitleId() {
        return titleId;
    }

    public void setTitleId(Long titleId) {
        this.titleId = titleId;
    }
}