package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_name_split_transfer`")
public class DistNameSplitTransfer extends BaseEntity {

    /**
     *
     */
    @Column(name = "`pa_name_no`")
    private String paNameNo;

    /**
     *
     */
    @Column(name = "`name`")
    private String name;

    /**
     *
     */
    @Column(name = "`split_name`")
    private String splitName;

    /**
     *
     */
    @Column(name = "`remark`")
    private String remark;

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSplitName() {
        return splitName;
    }

    public void setSplitName(String splitName) {
        this.splitName = splitName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
