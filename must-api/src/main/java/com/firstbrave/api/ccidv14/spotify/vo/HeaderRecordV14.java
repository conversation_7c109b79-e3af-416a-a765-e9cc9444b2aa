package com.firstbrave.api.ccidv14.spotify.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.ccid.base.spotify.CCIDRecordPrefixSpotify;

import java.math.BigDecimal;

public class HeaderRecordV14 extends CCIDRecordPrefixSpotify {

    /**
     * Format: YYYYMMDD
     */
    @ApiContentType(index=1,length = 8)
    private String date;
    

    /**
     * 发送方 "PRS for Music"
     */
    @ApiContentType(index=2,length = 45)
    private String sender;
    
    /**
     * 接收方 "Spotify"
     */
    @ApiContentType(index=3,length = 45)
    private String receiver;
    
    /**
     * 区域
     */
    @ApiContentType(index=4,length = 2)
    private String territory;
    
    /**
     * 数据开始日期“20070101” 
     */
    @ApiContentType(index=5,length = 8)
    private String startDate;
    
    /**
     *Type of the Work Code used in the file 
     */
    @ApiContentType(index=6,length = 30)
    private String workCodeType;
    
    /**
     * Name of Sales Channel,Portal etc."Spotify"
     */
    @ApiContentType(index=7,length = 30)
    private String serviceDescription;
    
    /**
     * 重製比例
     */
    @ApiContentType(index=8,length = 6)
    private String mechanicalPercentageSplit;
    
    /**
     * 公傳比例
     */
    @ApiContentType(index=9,length = 6)
    private String performancePercentageSplit;
    
	/**
	 * Currency in which licensor is claiming. If currency
	 * exchange is required, the exchange rate specified must
	 * be applied
	 */
	@ApiContentType(index=10,length = 3)
	private String currency;

	@ApiContentType(index=11,length = 6)
	private String totalProRataShare;

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getTerritory() {
		return territory;
	}

	public void setTerritory(String territory) {
		this.territory = territory;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getWorkCodeType() {
		return workCodeType;
	}

	public void setWorkCodeType(String workCodeType) {
		this.workCodeType = workCodeType;
	}

	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	public String getMechanicalPercentageSplit() {
		return mechanicalPercentageSplit;
	}

	public void setMechanicalPercentageSplit(String mechanicalPercentageSplit) {
		this.mechanicalPercentageSplit = mechanicalPercentageSplit;
	}

	public String getPerformancePercentageSplit() {
		return performancePercentageSplit;
	}

	public void setPerformancePercentageSplit(String performancePercentageSplit) {
		this.performancePercentageSplit = performancePercentageSplit;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getTotalProRataShare() {
		return totalProRataShare;
	}

	public void setTotalProRataShare(String totalProRataShare) {
		this.totalProRataShare = totalProRataShare;
	}

	@Override
	public String toString() {
		return "HeaderRecord [date=" + date + ", sender=" + sender + ", receiver=" + receiver
				+ ", territory=" + territory + ", startDate=" + startDate + ", endDate="
				+ ", currency=" + currency  + ", workCodeType=" + workCodeType
				+ ", serviceDescription=" + serviceDescription
				+ ", mechPercSplit=" + mechanicalPercentageSplit + ", perfPercSplit=" + performancePercentageSplit + ", totalProRataShare=" + totalProRataShare + "]";
	}
	

}
