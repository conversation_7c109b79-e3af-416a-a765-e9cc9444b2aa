package com.firstbrave.api.ccidv14.vo;

import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.base.ApiContentType;

import java.math.BigDecimal;

public class HeaderRecordV14 extends CCIDRecordPrefix {

    /**
     * CCID版本(14 version)
     */
    @ApiContentType(index=2,length = 3)
	private String version;
    
    /**
     * Format: YYYYMMDD
     */
    @ApiContentType(index=3,length = 8)
    private String date;
    

    /**
     * 发送方 "PRS for Music"
     */
    @ApiContentType(index=4,length = 45)
    private String sender;
    
    /**
     * 接收方 "Spotify"
     */
    @ApiContentType(index=5,length = 45)
    private String receiver;
    
    /**
     *报告生成的时间 “2007012345” 
     */
    @ApiContentType(index=6,length = 10)
    private String ccidId;
    
    /**
     * 区域
     */
    @ApiContentType(index=7,length = 2)
    private String territory;
    
    /**
     * 数据开始日期“20070101” 
     */
    @ApiContentType(index=8,length = 8)
    private String startDate;
    
    /**
     * 数据结束“20070131” 
     */
    @ApiContentType(index=9,length = 8)
    private String endDate;
      
    /**
     * 支付金額幣別"EUR" in accordance to ISO 4217
     */
    @ApiContentType(index=10,length = 3)
    private String royaltyCurrency;
    
    /**
     * 原始收入幣別in accordance to ISO 4217
     */
    @ApiContentType(index=11,length = 3)
    private String originalRevenueBasisCurrency;
    
    /**
     * 5個位1個."1.04356 for 1,04356"
     */
    @ApiContentType(index=12,length = 20)
    private BigDecimal conversionRate;
    
    /**
     *Type of the Work Code used in the file 
     */
    @ApiContentType(index=13,length = 30)
    private String workCodeType;
    
    /**
     *  If applicable the field contains the
		original invoice details file id for
		which this correction is made
		against. It will be populated only if
		the CCID file is a COR file, for ORI
		file this would be blank. This field
		enforces one COR file can contain
		only one ORG claim file.“2007012345”
     */
    @ApiContentType(index=14,length = 10)
    private String ccidIdCorrectionReference;
    
    /**
     * Only values invoice detail “IN” and vlaim “CL” are allowed
     */
    @ApiContentType(index=15,length = 2)
    private String typeOfClaim;
    
    /**
     * Business model, e.g. single buy vs
     */
    @ApiContentType(index=16,length = 10)
    private String commercialModel;
    
    /**
     * Name of Sales Channel,Portal etc."Spotify"
     */
    @ApiContentType(index=17,length = 30)
    private String serviceDescription;
    
    /**
     * 文檔5-2Use type of license and claims
		(Values in accordance to ddex values) “PermanentDownload”
     */
    @ApiContentType(index=18,length = 25)
    private String useType;
    
    /**
     * 重製比例
     */
    @ApiContentType(index=19,length = 6)
    private String mechPercSplit;
    
    /**
     * 公傳比例
     */
    @ApiContentType(index=20,length = 6)
    private String perfPercSplit;
    
    /**
     * To be filled in bilateral agreement between licensor and licensee.
		This is a numeric field that might be used to communicate the overall pro rata share of the licensor.
     */
    @ApiContentType(index=21,length = 6)
    private String aux;

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public String getCcidId() {
		return ccidId;
	}

	public void setCcidId(String ccidId) {
		this.ccidId = ccidId;
	}

	public String getTerritory() {
		return territory;
	}

	public void setTerritory(String territory) {
		this.territory = territory;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getRoyaltyCurrency() {
		return royaltyCurrency;
	}

	public void setRoyaltyCurrency(String royaltyCurrency) {
		this.royaltyCurrency = royaltyCurrency;
	}

	public String getOriginalRevenueBasisCurrency() {
		return originalRevenueBasisCurrency;
	}

	public void setOriginalRevenueBasisCurrency(String originalRevenueBasisCurrency) {
		this.originalRevenueBasisCurrency = originalRevenueBasisCurrency;
	}

	public BigDecimal getConversionRate() {
		return conversionRate;
	}

	public void setConversionRate(BigDecimal conversionRate) {
		this.conversionRate = conversionRate;
	}

	public String getWorkCodeType() {
		return workCodeType;
	}

	public void setWorkCodeType(String workCodeType) {
		this.workCodeType = workCodeType;
	}

	public String getCcidIdCorrectionReference() {
		return ccidIdCorrectionReference;
	}

	public void setCcidIdCorrectionReference(String ccidIdCorrectionReference) {
		this.ccidIdCorrectionReference = ccidIdCorrectionReference;
	}

	public String getTypeOfClaim() {
		return typeOfClaim;
	}

	public void setTypeOfClaim(String typeOfClaim) {
		this.typeOfClaim = typeOfClaim;
	}

	public String getCommercialModel() {
		return commercialModel;
	}

	public void setCommercialModel(String commercialModel) {
		this.commercialModel = commercialModel;
	}

	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	public String getUseType() {
		return useType;
	}

	public void setUseType(String useType) {
		this.useType = useType;
	}

	public String getMechPercSplit() {
		return mechPercSplit;
	}

	public void setMechPercSplit(String mechPercSplit) {
		this.mechPercSplit = mechPercSplit;
	}

	public String getPerfPercSplit() {
		return perfPercSplit;
	}

	public void setPerfPercSplit(String perfPercSplit) {
		this.perfPercSplit = perfPercSplit;
	}

	public String getAux() {
		return aux;
	}

	public void setAux(String aux) {
		this.aux = aux;
	}

	@Override
	public String toString() {
		return "HeaderRecord [version=" + version + ", date=" + date + ", sender=" + sender + ", receiver=" + receiver
				+ ", ccidId=" + ccidId + ", territory=" + territory + ", startDate=" + startDate + ", endDate="
				+ endDate + ", royaltyCurrency=" + royaltyCurrency + ", originalRevenueBasisCurrency="
				+ originalRevenueBasisCurrency + ", conversionRate=" + conversionRate + ", workCodeType=" + workCodeType
				+ ", ccidIdCorrectionReference=" + ccidIdCorrectionReference + ", typeOfClaim=" + typeOfClaim
				+ ", commercialModel=" + commercialModel + ", serviceDescription=" + serviceDescription + ", useType="
				+ useType + ", mechPercSplit=" + mechPercSplit + ", perfPercSplit=" + perfPercSplit + ", aux=" + aux
				+ ", recordType=" + recordType + "]";
	}
	

}
