package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistParamOverseas;

import java.math.BigDecimal;
import java.util.Map;

@Repository
public interface DistParamOverseasMapper extends BaseMapper<DistParamOverseas> {

    BigDecimal getSumData(Map param);

    /**
     * 根据receiptId更新is_latest字段为1（非最新）
     */
    int updateIsLatestByReceiptId(@Param("receiptId") Long receiptId);

    /**
     * 根据receiptDetailsId更新is_latest字段为1（非最新）
     */
    int updateIsLatestByReceiptDetailsId(@Param("receiptDetailsId") Long receiptDetailsId);

}