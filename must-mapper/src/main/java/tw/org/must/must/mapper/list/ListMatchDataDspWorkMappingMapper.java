package tw.org.must.must.mapper.list;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataBaseInfo;
import tw.org.must.must.model.list.ListMatchDataDspWorkMapping;

@Repository
public interface ListMatchDataDspWorkMappingMapper extends BaseMapper<ListMatchDataDspWorkMapping> {

	List<ListMatchDataBaseInfo> getListMatchDataBaseInfoList(String matchTitle, Integer matchType, Integer matchWorkId,
			Integer matchWorkSoc, Integer status, String title);


	int insertDuplicate(List<ListMatchDataDspWorkMapping> list);

	int insertForSplit(@Param("table_name_in")String table_name_in,
					   @Param("table_name_out")String table_name_out,
					   @Param("fileBaseId") Long fileBaseId,
					   @Param("list") List<Long> mappingIds);
}