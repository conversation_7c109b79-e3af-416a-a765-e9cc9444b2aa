<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="800MEN_main" pageWidth="1000" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="1000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ba8dba43-c113-483a-86e6-04dc0531e5a7">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="date" class="java.lang.String"/>
	<parameter name="reportId" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="count" class="java.lang.String"/>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="taxAmount" class="java.lang.String"/>
	<parameter name="commissionAmount" class="java.lang.String"/>
	<parameter name="paymentTotal" class="java.lang.String"/>
	<parameter name="autoPayNo" class="java.lang.String"/>
	<parameter name="handlingCharges" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="detailList" class="java.lang.Object"/>
	<field name="summaryTotal" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="46" splitType="Stretch"/>
	</title>
	<columnHeader>
		<band height="155" splitType="Stretch">
			<staticText>
				<reportElement x="840" y="80" width="60" height="20" uuid="d0306256-43b7-413a-882f-d264bac7230c"/>
				<text><![CDATA[REPORT ID: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="44" width="560" height="20" uuid="f104f319-8fff-4560-9e61-adb812cd0c78"/>
				<textElement>
					<font fontName="华文宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[
                    TAIWAN MEMBER ROYALTIES PAYMENT BY CABLE TRANSFER/DEMAND/CHEQUE LIST 台灣會員其他附款方式報表]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="14" width="380" height="20" uuid="2c165201-b1d5-4dbe-a7ff-ff54f780a8a6"/>
				<textElement>
					<font fontName="华文宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[MUSIC COPYRIGHT SOCIETY OF CHINESE TAIPEI 社團法人中華音樂著作權協會]]></text>
			</staticText>
			<staticText>
				<reportElement x="840" y="44" width="30" height="20" uuid="67dfef90-a592-42f9-abf8-f1de19997b11"/>
				<text><![CDATA[DATE:]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="80" width="219" height="20" uuid="dc172ae4-788a-4deb-93b1-68cc6bc13525"/>
				<text><![CDATA[ROYALTIES DISTRIBUTION BY PAYMENT NO ]]></text>
			</staticText>
			<line>
				<reportElement x="10" y="150" width="960" height="1" forecolor="#000000" uuid="cf917b94-d264-49e6-bc38-f755743d3c61"/>
			</line>
			<textField>
				<reportElement x="870" y="44" width="100" height="20" uuid="3cf16da3-16e9-4a29-a12b-93c4dbda5de2"/>
				<textFieldExpression><![CDATA[$P{date}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="901" y="80" width="80" height="20" uuid="9cf3909d-0209-497a-aa99-c3ff9ff9be21"/>
				<textFieldExpression><![CDATA[$P{reportId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="230" y="80" width="70" height="20" uuid="e25701e3-a8c0-410d-96fc-07df806c3be8"/>
				<textFieldExpression><![CDATA[$P{autoPayNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="220" y="117" width="100" height="20" uuid="e8ca0917-e558-42aa-9248-0dac9d53e7e9"/>
				<text><![CDATA[PA NAME NO.]]></text>
			</staticText>
			<staticText>
				<reportElement x="550" y="117" width="70" height="30" uuid="d471f86d-77b9-4eff-b311-107942950556"/>
				<text><![CDATA[CURRENCY
CONVERTED]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="117" width="90" height="20" uuid="34d8606a-4d25-4900-801d-c9eacd856625"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[ROYALTIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="485" y="117" width="60" height="30" uuid="3185b6a1-7dca-4a1c-b473-18fac598bd33"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[PAYABLE
AMOUNT]]></text>
			</staticText>
			<staticText>
				<reportElement x="625" y="127" width="66" height="20" uuid="eb30af01-81d7-4bc0-8328-038d898aac49"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[DRAWN ON]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="117" width="110" height="20" uuid="b7e5fd4f-0aaf-4311-88da-ffeaa9495475"/>
				<text><![CDATA[MEMBER]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="117" width="60" height="30" uuid="20274745-ee38-49e4-b82b-caa65cb7fde7"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[HANDLING
CHARGES]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="117" width="90" height="20" uuid="5c44e683-3a83-463f-bc01-501d9d09c1dd"/>
				<text><![CDATA[DIST NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="705" y="127" width="120" height="20" uuid="7fa0b372-3f5e-4a4c-8cdb-d66b5c3246c9"/>
				<text><![CDATA[PAYMENT DETAIL]]></text>
			</staticText>
			<image>
				<reportElement x="3" y="23" width="80" height="40" uuid="c32c57df-396c-425d-9fe3-59ab803b5a84"/>
				<imageExpression><![CDATA["ireport/logo.png"]]></imageExpression>
			</image>
		</band>
	</columnHeader>
	<detail>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement x="10" y="8" width="110" height="21" uuid="3dbeb44e-5b2a-4fb0-ad24-5afebf8676a0"/>
				<text><![CDATA[*** BY CHEQUE ***]]></text>
			</staticText>
			<subreport>
				<reportElement x="10" y="40" width="860" height="40" uuid="e080995e-ab6f-46cc-b695-ae19ef15b9db"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("detailList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPayMem800_sub.jasper")]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="55" splitType="Stretch">
			<textField>
				<reportElement x="400" y="20" width="100" height="30" uuid="c8c64a09-d39e-4cd9-ab73-1c2909633600"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="500" y="20" width="100" height="30" uuid="39637c9c-1265-4245-b855-6d82a8f3093d"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
	<summary>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement x="10" y="20" width="126" height="20" uuid="ae27d55d-7054-49c9-8fca-bc6dd66de0b5"/>
				<text><![CDATA[TOTAL NO. OF MEMBERS:]]></text>
			</staticText>
			<textField>
				<reportElement x="136" y="20" width="44" height="20" uuid="64afc3d4-3ead-44df-adb5-f1cfb7832144"/>
				<textFieldExpression><![CDATA[$P{count}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="220" y="20" width="80" height="20" uuid="9afebf3c-b4bd-4117-acfb-5b786b441091"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="40" width="80" height="20" uuid="2f5fe705-2595-429d-ad1a-b9089b735832"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Withheld Tax:]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="60" width="80" height="20" uuid="c96b1341-6be5-47d0-a4de-970e4a241dd3"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Commission:]]></text>
			</staticText>
			<textField>
				<reportElement x="325" y="60" width="90" height="20" uuid="6cf5203c-96c5-4c6e-9fef-2bb39a05a664"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{commissionAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="325" y="40" width="90" height="20" uuid="34c19f75-47b0-40cf-ae18-15a4e1e677b0"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{taxAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="325" y="20" width="90" height="20" uuid="b281131f-7e02-4146-a3bc-6cdc7645d6f2"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{total}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="485" y="20" width="60" height="20" uuid="c0063876-07a4-4d84-9890-bf3898b8cb94"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{paymentTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="420" y="20" width="60" height="20" uuid="eb0cb7cb-a407-4c47-8842-38c52fa2f41a"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{handlingCharges}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
