<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="880_sub1" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="b737d312-b5dc-4612-a061-7a35aa90da42">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="distNo" class="java.lang.String"/>
	<field name="totalAmount" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="22" splitType="Stretch">
			<textField>
				<reportElement x="90" y="0" width="120" height="20" uuid="301a1a3f-74cf-4a86-93d2-1567ae39a34f"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{totalAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="30" y="0" width="50" height="20" uuid="b2d8a250-1e20-4f2a-963e-1130aca800d6"/>
				<textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
