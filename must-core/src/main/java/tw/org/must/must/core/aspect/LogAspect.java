package tw.org.must.must.core.aspect;

import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tw.org.must.must.core.service.sys.SysLogService;
import tw.org.must.must.model.sys.SysLog;
import tw.org.must.must.model.sys.SysUser;

/**
 * 操作日志切面
 */
@Aspect
@Component
public class LogAspect {

    public static final Logger LOGGER = LoggerFactory.getLogger(LogAspect.class);

    private final SysLogService sysLogService;

    @Autowired
    public LogAspect(SysLogService sysLogService) {
        this.sysLogService = sysLogService;
    }

    @Pointcut("@annotation(tw.org.must.must.core.annotation.Log)")
    public void pointcut() {
        // do nothing
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result;
        long beginTime = System.currentTimeMillis();
        // 执行方法
        result = point.proceed();
        // 执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        // 保存日志
        SysUser user = (SysUser) SecurityUtils.getSubject().getPrincipal();
        if (user != null) {
            SysLog log = new SysLog();
            log.setConsumedTime(time);
            log.setAccount(user.getAccount());
            sysLogService.save(point, log);
        }
        return result;
    }
}
