package tw.org.must.must.model.dist;

import tw.org.must.must.common.annotation.DynaEntity;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;

import java.math.BigDecimal;


@DynaEntity
@Table(name = "`dist_param_source`")
public class DistParamSource extends BaseEntity {



    @Column(name = "`list_source_id`")
    private Long listSourceId;


    @Column(name = "`list_source_name`")
    private String listSourceName;

    /**
     * 废弃字段
     */
    @Column(name = "`indvance`")
    private String indvance;

    /**
     * 当前清单类别准备要分的钱
     */
    @Column(name = "`unexpired`")
    private BigDecimal unexpired;

    /**
     * 其他收入
     */
    @Column(name = "`other_income`")
    private BigDecimal otherIncome;


    /**
     * 当前要分的钱+other income
     */
    @Column(name = "`per_other_income`")
    private BigDecimal perOtherIncome;


    /**
     * 准备要分配的钱+other+income
     */
    @Column(name = "`net_dist_income`")
    private BigDecimal netDistIncome;


    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 是否使用source金额分配
     */
    @Column(name = "`source_distribute`")
    private String sourceDistribute;


    public Long getListSourceId() {
        return listSourceId;
    }

    public void setListSourceId(Long listSourceId) {
        this.listSourceId = listSourceId;
    }

    public String getListSourceName() {
        return listSourceName;
    }

    public void setListSourceName(String listSourceName) {
        this.listSourceName = listSourceName;
    }

    public String getIndvance() {
        return indvance;
    }

    public void setIndvance(String indvance) {
        this.indvance = indvance;
    }

    public BigDecimal getUnexpired() {
        return unexpired;
    }

    public void setUnexpired(BigDecimal unexpired) {
        this.unexpired = unexpired;
    }

    public BigDecimal getOtherIncome() {
        return otherIncome;
    }

    public void setOtherIncome(BigDecimal otherIncome) {
        this.otherIncome = otherIncome;
    }

    public BigDecimal getPerOtherIncome() {
        return perOtherIncome;
    }

    public void setPerOtherIncome(BigDecimal perOtherIncome) {
        this.perOtherIncome = perOtherIncome;
    }

    public BigDecimal getNetDistIncome() {
        return netDistIncome;
    }

    public void setNetDistIncome(BigDecimal netDistIncome) {
        this.netDistIncome = netDistIncome;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getSourceDistribute() {
        return sourceDistribute;
    }

    public void setSourceDistribute(String sourceDistribute) {
        this.sourceDistribute = sourceDistribute;
    }
}