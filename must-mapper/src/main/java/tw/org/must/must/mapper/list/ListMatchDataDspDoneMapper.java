package tw.org.must.must.mapper.list;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataDsp;
import tw.org.must.must.model.list.ListMatchDataDspDone;

@Repository
public interface ListMatchDataDspDoneMapper extends BaseMapper<ListMatchDataDspDone> {

	void insertDuplicate(List<ListMatchDataDspDone> listMatchDataDspDoneList);


    Long getListMatchDataDoneByFileBaseIdMin(List<Long> listDspFileBaseIds);

    Long getListMatchDataDoneByFileBaseIdMinAndWorkType(List<Long> listDspFileBaseIds);


    @Select("SELECT work_unique_key FROM  temp_sync_work")
    List<String> selectSysncWorkTest();

    @Select("SELECT * FROM  list_match_data_dsp_done_bak_20210409")
    List<ListMatchDataDspDone> getListByTest();

    @Select("SELECT * FROM  list_match_data_dsp_done_bak_20210416")
    List<ListMatchDataDspDone> getListByTest1();

    Integer updateListMatchDataDspDoneFromHistory(@Param("listMatchDataDspDone") ListMatchDataDspDone listMatchDataDspDone, @Param("list") List<Long> list) ;

    List<ListMatchDataDspDone> getListMatchDataDoneByDataUniqueKeyLimit1(@Param("dataUniqueKey") String dataUniqueKey);

    @Select("select * from ${tableName} where file_base_id = #{fileBaseId} and id > #{id} order by id limit 10000" )
    List<ListMatchDataDspDone> getListMatchDataDspDone(@Param(value = "tableName") String tableName, @Param(value = "fileBaseId") Long fileBaseId, @Param(value = "id") Long id);
}