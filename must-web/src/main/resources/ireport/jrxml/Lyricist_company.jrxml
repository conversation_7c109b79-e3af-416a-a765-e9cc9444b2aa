<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Lyricist_company" pageWidth="595" pageHeight="842" whenNoDataType="BlankPage" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="4e616e96-d2b2-4619-9afa-0d1db7ed36e4">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="name" class="java.lang.String"/>
	<field name="socName" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="16" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout"/>
			<elementGroup/>
			<textField isStretchWithOverflow="true" evaluationTime="Column" isBlankWhenNull="true">
				<reportElement x="310" y="0" width="200" height="16" uuid="3044c683-830e-4db4-b3d0-3b0e6dc1c581"/>
				<textElement>
					<font fontName="华文宋体" size="8"/>
					<paragraph lineSpacing="AtLeast"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{socName} == null || $F{socName}.equals("") ? "" : "("+$F{socName}+")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="300" height="16" uuid="9533cea7-2c10-46ed-ab16-4755dfe6e7e7"/>
				<textElement>
					<font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
