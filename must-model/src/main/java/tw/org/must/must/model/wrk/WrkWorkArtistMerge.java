package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Table(name = "`wrk_work_artist_merge`")
public class WrkWorkArtistMerge extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 作品编号
     */
    @Column(name = "work_id")
    @NotNull(message="WrkWorkArtistMerge中的workId不能为空！")
    private Long workId;

    /**
     * 作品所属协会
     */
    @Column(name = "work_society_code")
    @NotNull(message="WrkWorkArtistMerge中的workSocietyCode不能为空！")
    private Integer workSocietyCode;

    /**
     * 表演者id
     */
    @Column(name = "aritist_id")
    private Long aritistId;

    /**
     * 填写协会
     */
    @Column(name = "input_soc")
    private Integer inputSoc;

    /**
     * Original Artist of Work,用来定位版本
     */
    @Column(name = "ori_aritist")
    private String oriAritist;

    /**
     * Artist of a first publish Work
     */
    @Column(name = "first_pub")
    private String firstPub;
    
    @Column(name = "work_unique_key")
    private String workUniqueKey;

    @Column(name = "unique_key_md5")
    private String uniqueKeyMd5;

    public String getUniqueKeyMd5() {
        return uniqueKeyMd5;
    }

    public void setUniqueKeyMd5(String uniqueKeyMd5) {
        this.uniqueKeyMd5 = uniqueKeyMd5;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getAritistId() {
        return aritistId;
    }

    public void setAritistId(Long aritistId) {
        this.aritistId = aritistId;
    }

    public Integer getInputSoc() {
        return inputSoc;
    }

    public void setInputSoc(Integer inputSoc) {
        this.inputSoc = inputSoc;
    }

    public String getOriAritist() {
        return oriAritist;
    }

    public void setOriAritist(String oriAritist) {
        this.oriAritist = oriAritist;
    }

    public String getFirstPub() {
        return firstPub;
    }

    public void setFirstPub(String firstPub) {
        this.firstPub = firstPub;
    }

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

}