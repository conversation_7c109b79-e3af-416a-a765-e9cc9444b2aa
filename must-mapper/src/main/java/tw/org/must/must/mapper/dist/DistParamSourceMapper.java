package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistParamSource;

import java.math.BigDecimal;

@Repository
public interface DistParamSourceMapper extends BaseMapper<DistParamSource> {

    /**
     * @description:
     * @param distNo
     * @return
     */
    BigDecimal sumOfNetDistIncomeWithDistNo(@Param("distNo") String distNo);


}