<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="DistAutoPay700_child" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0"
              rightMargin="0" topMargin="0" bottomMargin="0" uuid="d15f8e32-e1d8-465d-a9bc-b7f7012b230b">
    <parameter name="SUBREPORT_DIR" class="java.lang.String">
        <defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
    </parameter>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="distNo" class="java.lang.String"/>
    <field name="distAutoPayDetail" class="java.lang.Object"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <detail>
        <band height="26" splitType="Stretch">
            <textField>
                <reportElement x="50" y="0" width="60" height="20" uuid="4a1cc2ce-0867-47e3-bc00-c37c7d6bae52"/>
                <textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="0" y="0" width="50" height="20" uuid="0baad128-49cc-4830-8941-8c36bd44fe50"/>
                <text><![CDATA[DIST.NO.]]></text>
            </staticText>
            <subreport>
                <reportElement x="120" y="0" width="460" height="20" uuid="048e0c16-fdfa-40f2-8308-a1aa120582a4"/>
                <dataSourceExpression>
                    <![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distAutoPayDetail")]]></dataSourceExpression>
                <subreportExpression>
                    <![CDATA[$P{SUBREPORT_DIR} + "distAutoPays/DistAutoPay700_childTwo.jasper"]]></subreportExpression>
            </subreport>
        </band>
    </detail>
</jasperReport>
