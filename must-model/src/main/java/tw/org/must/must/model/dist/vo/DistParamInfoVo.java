package tw.org.must.must.model.dist.vo;

import tw.org.must.must.model.dist.DistParamInfo;
import tw.org.must.must.model.dist.DistUpaParam;

/**
 * @description:
 * @author: handa
 * @time: 2019/12/6 14:41
 */
public class DistParamInfoVo {


    /**
     * @description: 对用dist_param_number表distDescribe：分配描述
     */
    private String distDescribe;

    private String distYear;

    /**
     * @description: 对应dist_param_info表数据
     */
    private DistParamInfo distParamInfo;

    /**
     * @description: 对应dist_upa_param表数据
     */
    private DistUpaParam distUpaParam;

    public String getDistYear() {
        return distYear;
    }

    public void setDistYear(String distYear) {
        this.distYear = distYear;
    }

    public DistParamInfo getDistParamInfo() {
        return distParamInfo;
    }

    public void setDistParamInfo(DistParamInfo distParamInfo) {
        this.distParamInfo = distParamInfo;
    }

    public String getDistDescribe() {
        return distDescribe;
    }

    public void setDistDescribe(String distDescribe) {
        this.distDescribe = distDescribe;
    }

    public DistUpaParam getDistUpaParam() {
        return distUpaParam;
    }

    public void setDistUpaParam(DistUpaParam distUpaParam) {
        this.distUpaParam = distUpaParam;
    }
}
