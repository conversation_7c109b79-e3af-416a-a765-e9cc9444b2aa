package tw.org.must.must.model.wrk;

import tw.org.must.must.model.agr.AgrContentVO;
import tw.org.must.must.model.wrk.ipshare.WrkWorkIpShareVO;

import javax.persistence.Column;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;

public class WrkWorkIpShareTree {

    private Long id;

    private Long workId;

    private Integer workSocietyCode;

    private String ipNameNo;

    private String ipBaseNo;

    private String rightType;

    private BigDecimal ipShare;

    private String workIpRole;

    private String sd;

    private String groupIndicator;

    private String refIndicator;

    private Long sipLinkId;

    private String dummyNameRoman;

    private String dummyName;

    private Long oipLinkId;

    private String ipType;

    private BigDecimal orgWriterShare;

    private String agrNo;

    private String workUniqueKey;

    private String name;

    private String ipSocietyCode;

    // 合约开始时间
    private Date validFr;

    // 合约结束时间
    private Date validTo;

    private String autoExtensionInd;

    private String shareType;

    private Integer Distributable;

    private String chineseName;

    private BigDecimal preIpShare;

    private List<AgrContentVO> agrContentVOList;

    // 0：死循环异常；1：overlap异常
    private Integer exceptionType;

    List<WrkWorkIpShareTree> workIpShareList;

    // 存储初始时 数据库中所有ipshare的数据
    List<WrkWorkIpShare> initWrkWorkIpShareList;

    // 存储workRight的key : rightType value:wkrWrokRight
    Map<String,WrkWorkRight> wrkWorkRightMap;

    // 存储该合约的权利比例  合约上记录的比例为assignor的
    Map<String,BigDecimal> assignorRightTypeShare;

    // 存储该assignee的权利比例
    Map<String,BigDecimal> assigneeRightTypeShare;

    //会员有多个协会时，计算时保存比例分配。计算完以后，按比例拆分
    Map<Integer,BigDecimal> membershipShareMap;

    // 存储合约信息
    private AgrContentVO  agrContentVO;

    // 是否是重复的
    private boolean isRepeat;

    // 用于存储当前计算年份
    private String year;

    // 用于判断是否存在子节点
    private boolean hasBody;

    // 用於存放回收后的比例
    private BigDecimal orignalIpShare;

    // 用於存放 IP share
    private WrkWorkIpShareVO wrkWorkIpSHareVO;

    // 用於存放  是否是來自於wrokRight的SD信息
    private boolean isAgrSd;

    // 用於存放原始的workIpRole，方便處理TR SR AD 等類型的數據
    private String orignialWorkIpRole;

    // 用于记录回归后的比例即oipLinkId为0的比例
    private BigDecimal orignalWrkIpShare;

    // 用于记录 有合约且workIpRole为E的数据
    private boolean writerOp;

    // 用于记录 SE的数据方便前端取数据
    private boolean subPublisher;

    // 用于存放作品的genre   在后面合约判断的时候需要用到。
    private String genre;

    // 用于存放 ipNameNo，在合约exclude的时候要根据作品下 oipLinkId = 0的ipNameNo来过滤   FIXME 2021-01-27 调整为根据当前统一gp+rightType下的来过滤
//    private List<String> ipNameNoList;
    private HashMap<String,List<String>> ipNameNoGpRightTypeMap;

    // 用于存放 workIpRole 在合约exclude的时候要根据作品下oipLinkId = 0的workIpRole来过滤 FIXME 2021-01-27 调整为根据当前统一gp+rightType下的来过滤
//    private Set<String> workIpRoleSet;
    private HashMap<String,Set<String>> workIpRoleGpRightTypeMap;

    // 用于判断是否是overLeap的合约，如果这个人即是assignor又是assignee那么认为其为有冲突合约
    private List<WrkWorkIpShareTree> assignorList ;

    private BigDecimal ipsharem ;

    private Boolean isDist ;

    private String associatedAgrNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public BigDecimal getIpShare() {
        return ipShare;
    }

    public void setIpShare(BigDecimal ipShare) {
        this.ipShare = ipShare;
    }

    public String getWorkIpRole() {
        return workIpRole;
    }

    public void setWorkIpRole(String workIpRole) {
        this.workIpRole = workIpRole;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getGroupIndicator() {
        return groupIndicator;
    }

    public void setGroupIndicator(String groupIndicator) {
        this.groupIndicator = groupIndicator;
    }

    public Long getSipLinkId() {
        return sipLinkId;
    }

    public void setSipLinkId(Long sipLinkId) {
        this.sipLinkId = sipLinkId;
    }

    public String getDummyNameRoman() {
        return dummyNameRoman;
    }

    public void setDummyNameRoman(String dummyNameRoman) {
        this.dummyNameRoman = dummyNameRoman;
    }

    public String getDummyName() {
        return dummyName;
    }

    public void setDummyName(String dummyName) {
        this.dummyName = dummyName;
    }

    public Long getOipLinkId() {
        return oipLinkId;
    }

    public void setOipLinkId(Long oipLinkId) {
        this.oipLinkId = oipLinkId;
    }

    public String getIpType() {
        return ipType;
    }

    public void setIpType(String ipType) {
        this.ipType = ipType;
    }

    public BigDecimal getOrgWriterShare() {
        return orgWriterShare;
    }

    public void setOrgWriterShare(BigDecimal orgWriterShare) {
        this.orgWriterShare = orgWriterShare;
    }

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIpSocietyCode() {
        return ipSocietyCode;
    }

    public void setIpSocietyCode(String ipSocietyCode) {
        this.ipSocietyCode = ipSocietyCode;
    }

    public Date getValidFr() {
        return validFr;
    }

    public void setValidFr(Date validFr) {
        this.validFr = validFr;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public String getAutoExtensionInd() {
        return autoExtensionInd;
    }

    public void setAutoExtensionInd(String autoExtensionInd) {
        this.autoExtensionInd = autoExtensionInd;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public Integer getDistributable() {
        return Distributable;
    }

    public void setDistributable(Integer distributable) {
        Distributable = distributable;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public BigDecimal getPreIpShare() {
        return preIpShare;
    }

    public void setPreIpShare(BigDecimal preIpShare) {
        this.preIpShare = preIpShare;
    }

    public List<AgrContentVO> getAgrContentVOList() {
        return agrContentVOList;
    }

    public void setAgrContentVOList(List<AgrContentVO> agrContentVOList) {
        this.agrContentVOList = agrContentVOList;
    }

    public List<WrkWorkIpShareTree> getWorkIpShareList() {
        return workIpShareList;
    }

    public void setWorkIpShareList(List<WrkWorkIpShareTree> workIpShareList) {
        this.workIpShareList = workIpShareList;
    }

    public Integer getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(Integer exceptionType) {
        this.exceptionType = exceptionType;
    }

    public List<WrkWorkIpShare> getInitWrkWorkIpShareList() {
        return initWrkWorkIpShareList;
    }

    public void setInitWrkWorkIpShareList(List<WrkWorkIpShare> initWrkWorkIpShareList) {
        this.initWrkWorkIpShareList = initWrkWorkIpShareList;
    }

    public Map<String, WrkWorkRight> getWrkWorkRightMap() {
        return wrkWorkRightMap;
    }

    public void setWrkWorkRightMap(Map<String, WrkWorkRight> wrkWorkRightMap) {
        this.wrkWorkRightMap = wrkWorkRightMap;
    }

    public Map<String, BigDecimal> getAssignorRightTypeShare() {
        return assignorRightTypeShare;
    }

    public void setAssignorRightTypeShare(Map<String, BigDecimal> assignorRightTypeShare) {
        this.assignorRightTypeShare = assignorRightTypeShare;
    }

    public Map<String, BigDecimal> getAssigneeRightTypeShare() {
        return assigneeRightTypeShare;
    }

    public void setAssigneeRightTypeShare(Map<String, BigDecimal> assigneeRightTypeShare) {
        this.assigneeRightTypeShare = assigneeRightTypeShare;
    }

    public AgrContentVO getAgrContentVO() {
        return agrContentVO;
    }

    public void setAgrContentVO(AgrContentVO agrContentVO) {
        this.agrContentVO = agrContentVO;
    }

    public boolean isRepeat() {
        return isRepeat;
    }

    public void setRepeat(boolean repeat) {
        isRepeat = repeat;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public boolean isHasBody() {
        return hasBody;
    }

    public void setHasBody(boolean hasBody) {
        this.hasBody = hasBody;
    }

    public BigDecimal getOrignalIpShare() {
        return orignalIpShare;
    }

    public void setOrignalIpShare(BigDecimal orignalIpShare) {
        this.orignalIpShare = orignalIpShare;
    }

    public WrkWorkIpShareVO getWrkWorkIpSHareVO() {
        return wrkWorkIpSHareVO;
    }

    public void setWrkWorkIpSHareVO(WrkWorkIpShareVO wrkWorkIpSHareVO) {
        this.wrkWorkIpSHareVO = wrkWorkIpSHareVO;
    }

    public boolean isAgrSd() {
        return isAgrSd;
    }

    public void setAgrSd(boolean agrSd) {
        isAgrSd = agrSd;
    }

    public String getOrignialWorkIpRole() {
        return orignialWorkIpRole;
    }

    public void setOrignialWorkIpRole(String orignialWorkIpRole) {
        this.orignialWorkIpRole = orignialWorkIpRole;
    }

    public BigDecimal getOrignalWrkIpShare() {
        return orignalWrkIpShare;
    }

    public void setOrignalWrkIpShare(BigDecimal orignalWrkIpShare) {
        this.orignalWrkIpShare = orignalWrkIpShare;
    }

    public boolean isWriterOp() {
        return writerOp;
    }

    public void setWriterOp(boolean writerOp) {
        this.writerOp = writerOp;
    }

    public boolean isSubPublisher() {
        return subPublisher;
    }

    public void setSubPublisher(boolean subPublisher) {
        this.subPublisher = subPublisher;
    }

    public String getGenre() {
        return genre;
    }

    public void setGenre(String genre) {
        this.genre = genre;
    }

//    public List<String> getIpNameNoList() {
//        return ipNameNoList;
//    }
//
//    public void setIpNameNoList(List<String> ipNameNoList) {
//        this.ipNameNoList = ipNameNoList;
//    }
//
//    public Set<String> getWorkIpRoleSet() {
//        return workIpRoleSet;
//    }
//
//    public void setWorkIpRoleSet(Set<String> workIpRoleSet) {
//        this.workIpRoleSet = workIpRoleSet;
//    }

    public HashMap<String, List<String>> getIpNameNoGpRightTypeMap() {
        return ipNameNoGpRightTypeMap;
    }

    public void setIpNameNoGpRightTypeMap(HashMap<String, List<String>> ipNameNoGpRightTypeMap) {
        this.ipNameNoGpRightTypeMap = ipNameNoGpRightTypeMap;
    }

    public HashMap<String, Set<String>> getWorkIpRoleGpRightTypeMap() {
        return workIpRoleGpRightTypeMap;
    }

    public void setWorkIpRoleGpRightTypeMap(HashMap<String, Set<String>> workIpRoleGpRightTypeMap) {
        this.workIpRoleGpRightTypeMap = workIpRoleGpRightTypeMap;
    }

    public List<WrkWorkIpShareTree> getAssignorList() {
        return assignorList;
    }

    public void setAssignorList(List<WrkWorkIpShareTree> assignorList) {
        this.assignorList = assignorList;
    }

    public Map<Integer, BigDecimal> getMembershipShareMap() {
        return membershipShareMap;
    }

    public void setMembershipShareMap(Map<Integer, BigDecimal> membershipShareMap) {
        this.membershipShareMap = membershipShareMap;
    }

    public BigDecimal getIpsharem() {
        return ipsharem;
    }

    public void setIpsharem(BigDecimal ipsharem) {
        this.ipsharem = ipsharem;
    }

    public String getRefIndicator() {
        return refIndicator;
    }

    public void setRefIndicator(String refIndicator) {
        this.refIndicator = refIndicator;
    }

    public Boolean getDist() {
        return isDist;
    }

    public void setDist(Boolean dist) {
        isDist = dist;
    }

    public String getAssociatedAgrNo() {
        return associatedAgrNo;
    }

    public void setAssociatedAgrNo(String associatedAgrNo) {
        this.associatedAgrNo = associatedAgrNo;
    }
}
