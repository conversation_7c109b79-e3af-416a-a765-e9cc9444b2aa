package tw.org.must.must.model.iswc;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: Submission
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/5/19 10:28
 */
@Data
public class Submission {
    private String agency;
    private Integer sourcedb;
    private String workcode;
    private String category;
    private Boolean disambiguation;
    private String disambiguationReason;
    private List<DisambiguateFrom> disambiguateFrom;
    private String bvltr;
    private String derivedWorkType;
    private List<DerivedFromIswc> derivedFromIswcs;
    private List<Performer> performers;
    private List<Instrumentation> instrumentation;
    private String cisnetCreatedDate;
    private String cisnetLastModifiedDate;
    private String iswc;
    private String originalTitle;
    private List<OtherTitle> otherTitles;
    private List<InterestedParties> interestedParties;
    private String preferredIswc;
    private Boolean previewDisambiguation;
}
