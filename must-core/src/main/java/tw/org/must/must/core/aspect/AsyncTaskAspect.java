package tw.org.must.must.core.aspect;

import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.core.annotation.AsyncTask;
import tw.org.must.must.core.service.ref.RefAsyncTaskService;
import tw.org.must.must.model.ref.RefAsyncTask;
import tw.org.must.must.model.sys.SysUser;

import java.util.concurrent.CompletableFuture;

@Aspect
@Component
public class AsyncTaskAspect {


    public static final Logger log = LoggerFactory.getLogger(LogAspect.class);

    @Autowired
    private RefAsyncTaskService refAsyncTaskService;


    @Pointcut("@annotation(tw.org.must.must.core.annotation.AsyncTask)")
    public void cut() {
        // do nothing
    }


    @Around("cut() && @annotation(asyncTask)")
    public void doAround(ProceedingJoinPoint point, AsyncTask asyncTask) throws Throwable {

        // 初始化异步任务
        log.info("=====异步执行任务开始 : {}=====", asyncTask.name());
        try {
            RefAsyncTask task = new RefAsyncTask();
            task.setTaskName(asyncTask.name());
            task.setTaskType(asyncTask.type());
            task.setStatus(0);
            SysUser user = (SysUser) SecurityUtils.getSubject().getPrincipal();
            if(null != user) {
                task.setUserId(user.getId());
                task.setUser(user.getName());
            }
            task.init();
            refAsyncTaskService.add(task);
            // 异步执行任务
            CompletableFuture.runAsync(() -> {
                try {
                    task.setStatus(1);
                    task.init();
                    refAsyncTaskService.updateSelective(task);
                    Object proceed = point.proceed();
                    // 执行完成，记录日志
                    if(proceed instanceof String) {
                        String log = (String) proceed;
                        if(log.length() > 500) {
                            log = log.substring(0, 500) + "……";
                        }
                        task.setLog(log);
                        if(asyncTask.type() == 2) {
                            task.setLog("導出成功！");
                            task.setDownloadUrl(log);
                        }
                    }
                    task.setStatus(2);
                    task.init();
                    refAsyncTaskService.updateSelective(task);
                    log.info("=====异步执行任务结束 : {}=====", asyncTask.name());
                } catch (Exception e) {
                    log.error("=====异步执行任务失败， msg : {}", e);
                    task.setStatus(3);
                    task.setLog(e.getMessage());
                    task.init();
                    refAsyncTaskService.updateSelective(task);
                } catch (Throwable throwable) {
                    log.error("=====异步执行任务失败2， msg : {}", throwable);
                    task.setStatus(3);
                    task.setLog(throwable.getMessage());
                    task.init();
                    refAsyncTaskService.updateSelective(task);
                    throwable.printStackTrace();
                }
            });
        } catch (Exception e) {
            log.error("=====异步执行任务失败3， msg : {}", e);
            throw new MustException(String.format("異步執行失敗，msg:, %s", e.getMessage()));
        }
    }

}
