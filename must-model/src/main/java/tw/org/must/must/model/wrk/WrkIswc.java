package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`wrk_iswc`")
public class WrkIswc extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`iswc`")
    private String iswc;

    /**
     * 
     */
    @Column(name = "`worknum`")
    private Long worknum;

    /**
     * 
     */
    @Column(name = "`worknum_society`")
    private Integer worknumSociety;

    /**
     * 
     */
    @Column(name = "`source`")
    private String source;

    /**
     * 
     */
    @Column(name = "`duplicate_status`")
    private String duplicateStatus;

    /**
     * 
     */
    @Column(name = "`music_arr_code`")
    private String musicArrCode;

    /**
     * 
     */
    @Column(name = "`lyric_adapt_code`")
    private String lyricAdaptCode;

    /**
     * 
     */
    @Column(name = "`version_type`")
    private String versionType;

    /**
     * 
     */
    @Column(name = "`version_iswc`")
    private String versionIswc;

    /**
     * 
     */
    @Column(name = "`version_title`")
    private String versionTitle;

    /**
     * 
     */
    @Column(name = "`version_ip`")
    private String versionIp;

    /**
     * 
     */
    @Column(name = "`version_lang_code`")
    private String versionLangCode;

    /**
     * 
     */
    @Column(name = "`excerpt_type`")
    private String excerptType;

    /**
     * 
     */
    @Column(name = "`excerpt_iswc`")
    private String excerptIswc;

    /**
     * 
     */
    @Column(name = "`excerpt_title`")
    private String excerptTitle;

    /**
     * 
     */
    @Column(name = "`excerpt_ip`")
    private String excerptIp;

    /**
     * 
     */
    @Column(name = "`excerpt_lang_code`")
    private String excerptLangCode;

    /**
     * 
     */
    @Column(name = "`composite_type`")
    private String compositeType;

    /**
     * 
     */
    @Column(name = "`composite_title`")
    private String compositeTitle;

    /**
     * 
     */
    @Column(name = "`composite_iswc`")
    private String compositeIswc;

    /**
     * 
     */
    @Column(name = "`composite_ip`")
    private String compositeIp;

    /**
     * 
     */
    @Column(name = "`composite_dur`")
    private String compositeDur;

    /**
     * 
     */
    @Column(name = "`assign_date`")
    private Date assignDate;

    /**
     * 
     */
    @Column(name = "`preferred`")
    private String preferred;

    /**
     * 
     */
    @Column(name = "`lst_upload_date`")
    private Date lstUploadDate;

    /**
     * 
     */
    @Column(name = "`lst_upload_remark`")
    private String lstUploadRemark;

    /**
     * 
     */
    @Column(name = "`lst_export_date`")
    private Date lstExportDate;

    /**
     * 作品唯一key ，md5(work_society_code+work_id)
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }

    public Long getWorknum() {
        return worknum;
    }

    public void setWorknum(Long worknum) {
        this.worknum = worknum;
    }

    public Integer getWorknumSociety() {
        return worknumSociety;
    }

    public void setWorknumSociety(Integer worknumSociety) {
        this.worknumSociety = worknumSociety;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getDuplicateStatus() {
        return duplicateStatus;
    }

    public void setDuplicateStatus(String duplicateStatus) {
        this.duplicateStatus = duplicateStatus;
    }

    public String getMusicArrCode() {
        return musicArrCode;
    }

    public void setMusicArrCode(String musicArrCode) {
        this.musicArrCode = musicArrCode;
    }

    public String getLyricAdaptCode() {
        return lyricAdaptCode;
    }

    public void setLyricAdaptCode(String lyricAdaptCode) {
        this.lyricAdaptCode = lyricAdaptCode;
    }

    public String getVersionType() {
        return versionType;
    }

    public void setVersionType(String versionType) {
        this.versionType = versionType;
    }

    public String getVersionIswc() {
        return versionIswc;
    }

    public void setVersionIswc(String versionIswc) {
        this.versionIswc = versionIswc;
    }

    public String getVersionTitle() {
        return versionTitle;
    }

    public void setVersionTitle(String versionTitle) {
        this.versionTitle = versionTitle;
    }

    public String getVersionIp() {
        return versionIp;
    }

    public void setVersionIp(String versionIp) {
        this.versionIp = versionIp;
    }

    public String getVersionLangCode() {
        return versionLangCode;
    }

    public void setVersionLangCode(String versionLangCode) {
        this.versionLangCode = versionLangCode;
    }

    public String getExcerptType() {
        return excerptType;
    }

    public void setExcerptType(String excerptType) {
        this.excerptType = excerptType;
    }

    public String getExcerptIswc() {
        return excerptIswc;
    }

    public void setExcerptIswc(String excerptIswc) {
        this.excerptIswc = excerptIswc;
    }

    public String getExcerptTitle() {
        return excerptTitle;
    }

    public void setExcerptTitle(String excerptTitle) {
        this.excerptTitle = excerptTitle;
    }

    public String getExcerptIp() {
        return excerptIp;
    }

    public void setExcerptIp(String excerptIp) {
        this.excerptIp = excerptIp;
    }

    public String getExcerptLangCode() {
        return excerptLangCode;
    }

    public void setExcerptLangCode(String excerptLangCode) {
        this.excerptLangCode = excerptLangCode;
    }

    public String getCompositeType() {
        return compositeType;
    }

    public void setCompositeType(String compositeType) {
        this.compositeType = compositeType;
    }

    public String getCompositeTitle() {
        return compositeTitle;
    }

    public void setCompositeTitle(String compositeTitle) {
        this.compositeTitle = compositeTitle;
    }

    public String getCompositeIswc() {
        return compositeIswc;
    }

    public void setCompositeIswc(String compositeIswc) {
        this.compositeIswc = compositeIswc;
    }

    public String getCompositeIp() {
        return compositeIp;
    }

    public void setCompositeIp(String compositeIp) {
        this.compositeIp = compositeIp;
    }

    public String getCompositeDur() {
        return compositeDur;
    }

    public void setCompositeDur(String compositeDur) {
        this.compositeDur = compositeDur;
    }

    public Date getAssignDate() {
        return assignDate;
    }

    public void setAssignDate(Date assignDate) {
        this.assignDate = assignDate;
    }

    public String getPreferred() {
        return preferred;
    }

    public void setPreferred(String preferred) {
        this.preferred = preferred;
    }

    public Date getLstUploadDate() {
        return lstUploadDate;
    }

    public void setLstUploadDate(Date lstUploadDate) {
        this.lstUploadDate = lstUploadDate;
    }

    public String getLstUploadRemark() {
        return lstUploadRemark;
    }

    public void setLstUploadRemark(String lstUploadRemark) {
        this.lstUploadRemark = lstUploadRemark;
    }

    public Date getLstExportDate() {
        return lstExportDate;
    }

    public void setLstExportDate(Date lstExportDate) {
        this.lstExportDate = lstExportDate;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }



}