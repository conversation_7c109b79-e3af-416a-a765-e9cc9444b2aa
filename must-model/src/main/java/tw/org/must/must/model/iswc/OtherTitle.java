package tw.org.must.must.model.iswc;

import lombok.Data;

import java.util.Objects;

/**
 * @ClassName: OtherTitle
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/5/19 10:39
 */
@Data
public class OtherTitle {
    private String title;
    private String type;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OtherTitle that = (OtherTitle) o;
        return title.equals(that.title) &&
                type.equals(that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(title, type);
    }
}
