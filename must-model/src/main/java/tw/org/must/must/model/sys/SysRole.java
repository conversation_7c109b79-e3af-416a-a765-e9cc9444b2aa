package tw.org.must.must.model.sys;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Table(name = "`sys_role`")
public class SysRole extends BaseEntity {

    private static final long serialVersionUID = 3111450078856107148L;
    /**
     * 角色编码
     */
    @Column(name = "code")
    private String code;

    /**
     * 角色中文名称
     */
    @NotBlank(message = "角色名称不能为空！")
    @Column(name = "name")
    private String name;

    /**
     * 英文名称
     */
    @Column(name = "name_en")
    private String nameEn;

    /**
     * 删除状态，0：不删除，1：删除，默认0
     */
    @Column(name = "is_deleted")
    private Boolean deleted;

    @Column(name = "remark")
    private String remark;

    /*角色的菜单id列表*/
    private List<Long> sysMenuList;

    @Override
    public void init() {
        super.init();
        this.setDeleted(false);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<Long> getSysMenuList() {
        return sysMenuList;
    }

    public void setSysMenuList(List<Long> sysMenuList) {
        this.sysMenuList = sysMenuList;
    }

    @Override
    public String toString() {
        return "SysRole{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", deleted=" + deleted +
                ", remark='" + remark + '\'' +
                ", sysMenuList=" + sysMenuList +
                '}';
    }
}