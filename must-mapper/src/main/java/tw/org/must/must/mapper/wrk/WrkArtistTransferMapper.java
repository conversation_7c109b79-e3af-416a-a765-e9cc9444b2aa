package tw.org.must.must.mapper.wrk;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.wrk.WrkArtistTransfer;

import java.util.List;

@Repository
public interface WrkArtistTransferMapper extends BaseMapper<WrkArtistTransfer> {

	Long saveWrkArtistTransferReceiveId(WrkArtistTransfer wtf);

	Integer insertBatchWrkArtistTransfer(List<WrkArtistTransfer> list) ;


}