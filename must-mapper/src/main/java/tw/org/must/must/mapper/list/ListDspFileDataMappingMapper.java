package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListMatchDataDsp;

import java.util.List;
import java.util.Map;

@Repository
public interface ListDspFileDataMappingMapper extends BaseMapper<ListDspFileDataMapping> {

	List<ListDspFileDataMapping> getListDspFileDataMappingById(Long listDspFileDataMappingId, Integer index);


    List<Map<String, Long>> getCountByFileBaseIds(List<Long> list);

    int addListTemporary(@Param("tableName") String tableName, @Param("list") List<ListDspFileDataMapping> list);

    void createTemporaryTable(@Param("tableName") String tableName);

    void dropTemporaryTable(@Param("tableName") String tableName);

    void druncateTemporaryTable(@Param("tableName") String tableName);

    List<ListDspFileDataMapping> selectListTemporaryOrderClikcNumberDescPage(@Param("tableName") String tableName);

    List<ListDspFileDataMapping> selectTemporaryByIds(@Param("ids")List<Long> ids, @Param("tableName")String tableName);

    List<Long> selectTest(String dspReleaseId, String salesTransactionId);

    int updateTest(@Param("ids") List<Long> ids);

    List<ListDspFileDataMapping> getDataByDataUniqueKey(@Param("list") List<String> list);

    Long getMinMappingIdByBaseId(@Param("fileBaseId") Long fileBaseId) ;

    List<Long> getPrimaryKeyByBaseId(@Param("id") Long id, @Param("fileBaseId") Long fileBaseId) ;

    int insertTestList(@Param("list") List<ListDspFileDataMapping> list);

    int insertForSplit(@Param("table_name_in")String table_name_in,
                        @Param("table_name_out")String table_name_out,
                        @Param("fileBaseId") Long fileBaseId,
                        @Param("start") Long start,
                        @Param("pagesize") int pagesize) ;

    int insertForSplitByIds(@Param("table_name_in")String table_name_in,
                       @Param("table_name_out")String table_name_out,
                       @Param("ids") List<Long> ids) ;

    Long selectCountForSplit(@Param("tableName")String tableName,
                             @Param("fileBaseId") Long fileBaseId);

    Long selectMaxIdForSplit(@Param("tableName")String tableName,
                             @Param("fileBaseId") Long fileBaseId);

    List<Long> getMappingIdByIdAndBaseId(@Param("start") Long start,@Param("end")Long end, @Param("fileBaseId") Long fileBaseId, @Param("pageSize") Integer pageSize) ;

    @Select("${sql}")
    Boolean createTable(@Param("sql") String sql);

    @Select("show create table ${tableName}")
    Map<String,String> getCreateTableDesc(@Param("tableName") String tableName);

    @Select("RENAME TABLE ${old_table_name} TO ${new_table_name}")
    Boolean renameTable(@Param("old_table_name") String old_table_name,@Param("new_table_name") String new_table_name);

    @Select("CREATE TABLE IF NOT EXISTS dsp_history.${tableName} like ${logicTableName}")
    void createTableLike(@Param("tableName") String tableName,@Param("logicTableName") String logicTableName);

    @Select("select id,file_mapping_id as file_base_id from list_dsp_file_data_mapping_1870_bak2 where id > #{id} order by id limit 100000")
    List<ListDspFileDataMapping> test2(@Param("id") Long id);

    List<Long> getIds(@Param(value = "tableName") String tableName, @Param(value = "fileBaseId") Long fileBaseId,
                      @Param(value = "id") Long id,@Param(value = "pagesize") int pagesize);

    @Select("SELECT MIN(id) FROM ${tableName} where file_base_id = #{fileBaseId}")
    Long getMinId(@Param(value = "tableName")String tableName, @Param("fileBaseId")Long fileBaseId);

    @Select("SELECT MIN(id) as min,MAX(id) as max FROM ${tableName} where file_base_id = #{fileBaseId}")
    Map<String,Long> getMinAndMax(@Param(value = "tableName")String tableName, @Param("fileBaseId")Long fileBaseId);

    Integer insertSlave(List<ListDspFileDataMapping> list);

}