package tw.org.must.must.mapper.list;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListCategory;
import tw.org.must.must.model.ref.RefPoolRight;

import java.util.List;

@Repository
public interface ListCategoryMapper extends BaseMapper<ListCategory> {

    /**
     * @description: 获取所有RefPoolRight数据
     * @return
     */
    List<RefPoolRight> getAllOfRefPoolRight();

    List<ListCategory> getCategoryByCode(String categoryCode) ;
}
