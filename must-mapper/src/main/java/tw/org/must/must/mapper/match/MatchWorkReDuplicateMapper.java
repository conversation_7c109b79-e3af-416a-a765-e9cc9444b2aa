package tw.org.must.must.mapper.match;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWorkReDuplicate;

import java.util.List;

@Repository
public interface MatchWorkReDuplicateMapper extends BaseMapper<MatchWorkReDuplicate> {
    List<MatchWorkReDuplicate> listSubFromParentWorkNo(@Param("uploadWorkId") String parentWorkNo);


}