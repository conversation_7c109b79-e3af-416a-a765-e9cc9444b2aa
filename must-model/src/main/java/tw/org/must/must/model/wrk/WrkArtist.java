package tw.org.must.must.model.wrk;

import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

@Table(name = "`wrk_artist`")
public class WrkArtist extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 姓
     */
    @Column(name = "first_name")
    private String firstName;

    /**
     * 名
     */
    @Column(name = "last_name")
    private String lastName;
    
    @Column(name = "name")
    private String name;

    /**
     * 中文名
     */
    @Column(name = "chinese_name")
    private String chineseName;

    /**
     * 罗马拼音
     */
    @Column(name = "roman_name")
    private String romanName;

    /**
     * 
     */
    @Column(name = "ip_name_no")
    private String ipNameNo;

    /**
     * 
     */
    @Column(name = "ip_base_no")
    private String ipBaseNo;

    /**
     * 来自哪个演员
     */
    @Column(name = "ref_artist_id")
    private Long refArtistId;

    /**
     * 
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 是否是乐队
     */
    @Column(name = "is_band")
    private String isBand;

    /**
     * 国家编码
     */
    @Column(name = "TIS_n")
    private Integer tisN;

    /**
     * 乐队名称
     */
    @Column(name = "band_name")
    private String bandName;

    /**
     * 国家名称
     */
    @Column(name = "country")
    private String country;

    @Column(name = "orcal_artist_id")
    private Long orcalArtistId;

    @Column(name = "unique_key_md5")
    private String uniqueKeyMd5;

    @Column(name="ref_artist_id_md5")
    private String refArtistIdMd5;
    
    @Transient
    private String firstPub;
    
    @Transient
	private String oriAritist;
    
    @Transient
    private Integer inputSoc;
    
    @Transient
    private String artistName;


    public String getUniqueKeyMd5() {
        return uniqueKeyMd5;
    }

    public void setUniqueKeyMd5(String uniqueKeyMd5) {
        this.uniqueKeyMd5 = uniqueKeyMd5;
    }

    public Long getOrcalArtistId() {
        return orcalArtistId;
    }

    public void setOrcalArtistId(Long orcalArtistId) {
        this.orcalArtistId = orcalArtistId;
    }

    public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getInputSoc() {
		return inputSoc;
	}

	public void setInputSoc(Integer inputSoc) {
		this.inputSoc = inputSoc;
	}

	public String getFirstPub() {
		return firstPub;
	}

	public void setFirstPub(String firstPub) {
		this.firstPub = firstPub;
	}

	public String getOriAritist() {
		return oriAritist;
	}

	public void setOriAritist(String oriAritist) {
		this.oriAritist = oriAritist;
	}


	public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getRomanName() {
        return romanName;
    }

    public void setRomanName(String romanName) {
        this.romanName = romanName;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public Long getRefArtistId() {
        return refArtistId;
    }

    public void setRefArtistId(Long refArtistId) {
        this.refArtistId = refArtistId;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getIsBand() {
        return isBand;
    }

    public void setIsBand(String isBand) {
        this.isBand = isBand;
    }

    public Integer getTisN() {
        return tisN;
    }

    public void setTisN(Integer tisN) {
        this.tisN = tisN;
    }

    public String getBandName() {
        return bandName;
    }

    public void setBandName(String bandName) {
        this.bandName = bandName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

	public String getArtistName() {
		artistName = "";
		if(StringUtils.isNotBlank(firstName)) {
			artistName +=this.firstName+" ";
		}
		if(StringUtils.isNotBlank(lastName)) {
			artistName +=this.lastName;
		}
		return artistName;
	}

	public void setArtistName(String artistName) {
		this.artistName = artistName;
	}

    public String getRefArtistIdMd5() {
        return refArtistIdMd5;
    }

    public void setRefArtistIdMd5(String refArtistIdMd5) {
        this.refArtistIdMd5 = refArtistIdMd5;
    }
}