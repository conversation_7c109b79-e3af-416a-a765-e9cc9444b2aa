package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class Mw0101 extends BaseTsvEntity {


	
	private String BlockId;
	
	private String DspWorkId;

	private String ISWC;
	
	private String Title;
	
	private String SubTitle;

	private String ComposerAuthor;
	
	private String ComposerAuthorPartyId;
	
	private String Arranger;

	private String ArrangerPartyId;
	
	private String MusicPublisher;
	
	private String MusicPublisherPartyId;

	private String WorkContributor;
	
	private String WorkContributorPartyId;
	
	private String DataProvider;
	
	private String ProprietaryWorkId;



	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getDspWorkId() {
		return DspWorkId;
	}

	public void setDspWorkId(String dspWorkId) {
		DspWorkId = dspWorkId;
	}

	public String getISWC() {
		return ISWC;
	}

	public void setISWC(String iSWC) {
		ISWC = iSWC;
	}

	public String getTitle() {
		return Title;
	}

	public void setTitle(String title) {
		Title = title;
	}

	public String getSubTitle() {
		return SubTitle;
	}

	public void setSubTitle(String subTitle) {
		SubTitle = subTitle;
	}

	public String getComposerAuthor() {
		return ComposerAuthor;
	}

	public void setComposerAuthor(String composerAuthor) {
		ComposerAuthor = composerAuthor;
	}

	public String getComposerAuthorPartyId() {
		return ComposerAuthorPartyId;
	}

	public void setComposerAuthorPartyId(String composerAuthorPartyId) {
		ComposerAuthorPartyId = composerAuthorPartyId;
	}

	public String getArranger() {
		return Arranger;
	}

	public void setArranger(String arranger) {
		Arranger = arranger;
	}

	public String getArrangerPartyId() {
		return ArrangerPartyId;
	}

	public void setArrangerPartyId(String arrangerPartyId) {
		ArrangerPartyId = arrangerPartyId;
	}

	public String getMusicPublisher() {
		return MusicPublisher;
	}

	public void setMusicPublisher(String musicPublisher) {
		MusicPublisher = musicPublisher;
	}

	public String getMusicPublisherPartyId() {
		return MusicPublisherPartyId;
	}

	public void setMusicPublisherPartyId(String musicPublisherPartyId) {
		MusicPublisherPartyId = musicPublisherPartyId;
	}

	public String getWorkContributor() {
		return WorkContributor;
	}

	public void setWorkContributor(String workContributor) {
		WorkContributor = workContributor;
	}

	public String getWorkContributorPartyId() {
		return WorkContributorPartyId;
	}

	public void setWorkContributorPartyId(String workContributorPartyId) {
		WorkContributorPartyId = workContributorPartyId;
	}

	public String getDataProvider() {
		return DataProvider;
	}

	public void setDataProvider(String dataProvider) {
		DataProvider = dataProvider;
	}

	public String getProprietaryWorkId() {
		return ProprietaryWorkId;
	}

	public void setProprietaryWorkId(String proprietaryWorkId) {
		ProprietaryWorkId = proprietaryWorkId;
	}


}
