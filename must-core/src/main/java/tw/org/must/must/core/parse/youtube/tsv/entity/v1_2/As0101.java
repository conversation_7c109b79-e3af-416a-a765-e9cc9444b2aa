package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class As0101  extends BaseTsvEntity{


	
	private String BlockId;
	
	private String ResourceReference;
	
	private String DspResourceId;
	
	private String ISRC;
	
	private String Title;
	
	private String SubTitle;
	
	private String DisplayArtistName;
	
	private String DisplayArtistPartyId;
	
	private String Duration;
	
	private String durationM;
	
	private String durationS;
	
	private String ResourceType;
	
	private String IsMasterRecording;



	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getResourceReference() {
		return ResourceReference;
	}

	public void setResourceReference(String resourceReference) {
		ResourceReference = resourceReference;
	}

	public String getDspResourceId() {
		return DspResourceId;
	}

	public void setDspResourceId(String dspResourceId) {
		DspResourceId = dspResourceId;
	}

	public String getISRC() {
		return ISRC;
	}

	public void setISRC(String iSRC) {
		ISRC = iSRC;
	}

	public String getTitle() {
		return Title;
	}

	public void setTitle(String title) {
		Title = title;
	}

	public String getSubTitle() {
		return SubTitle;
	}

	public void setSubTitle(String subTitle) {
		SubTitle = subTitle;
	}

	public String getDisplayArtistName() {
		return DisplayArtistName;
	}

	public void setDisplayArtistName(String displayArtistName) {
		DisplayArtistName = displayArtistName;
	}

	public String getDisplayArtistPartyId() {
		return DisplayArtistPartyId;
	}

	public void setDisplayArtistPartyId(String displayArtistPartyId) {
		DisplayArtistPartyId = displayArtistPartyId;
	}

	public String getDuration() {
		return Duration;
	}

	public void setDuration(String duration) {
		Duration = duration;
	}

	public String getDurationM() {
		return durationM;
	}

	public void setDurationM(String durationM) {
		this.durationM = durationM;
	}

	public String getDurationS() {
		return durationS;
	}

	public void setDurationS(String durationS) {
		this.durationS = durationS;
	}

	public String getResourceType() {
		return ResourceType;
	}

	public void setResourceType(String resourceType) {
		ResourceType = resourceType;
	}

	public String getIsMasterRecording() {
		return IsMasterRecording;
	}

	public void setIsMasterRecording(String isMasterRecording) {
		IsMasterRecording = isMasterRecording;
	}
	
}
