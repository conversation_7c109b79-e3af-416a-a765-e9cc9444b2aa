package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistMoneySetInfo;

import java.util.List;

@Repository
public interface DistMoneySetInfoMapper extends BaseMapper<DistMoneySetInfo> {
	
	public List<DistMoneySetInfo> findMoneyInfoByTypeId(@Param("typeId")Long typeId,
			@Param("startTime")String startTime,@Param("endTime")String endTime,
			@Param("listName")String listName);

}