<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SRSociety_sub" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="780adfea-e91a-4aec-9b32-a499a5ea9f87">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="title" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="dist" class="java.lang.String"/>
	<field name="detail" class="java.lang.String"/>
	<field name="workNo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="110" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="180" height="30" uuid="1b75a06d-938c-4fd6-ad9a-3fd6c3989f85"/>
				<textFieldExpression><![CDATA[$F{title}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="180" y="0" width="120" height="30" uuid="fe198781-9f08-4ed4-9074-fa1908019e38"/>
				<textFieldExpression><![CDATA[$F{workNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="300" y="0" width="100" height="30" uuid="879176e0-2ef1-4aa3-8b11-b061f708fc85"/>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="0" width="100" height="30" uuid="87d2dbae-8f79-4aa6-a4b7-2eebf6b49cea"/>
				<textFieldExpression><![CDATA[$F{dist}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="50" width="595" height="60" uuid="fa72e0ae-d073-4474-a3fa-76b185b7279e"/>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA["SRSociety_child.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
