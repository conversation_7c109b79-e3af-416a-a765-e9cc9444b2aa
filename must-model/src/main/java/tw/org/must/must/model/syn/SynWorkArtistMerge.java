package tw.org.must.must.model.syn;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`syn_work_artist_merge`")
public class SynWorkArtistMerge extends BaseEntity {

    /**
     * 作品编号
     */
    @Column(name = "work_id")
    private Long workId;

    /**
     * 作品所属协会
     */
    @Column(name = "work_society_code")
    private Integer workSocietyCode;

    /**
     * 表演者id
     */
    @Column(name = "aritist_id")
    private Long aritistId;

    /**
     * 填写协会
     */
    @Column(name = "input_soc")
    private Integer inputSoc;

    /**
     * Original Artist of Work,用来定位版本
     */
    @Column(name = "ori_aritist")
    private String oriAritist;

    /**
     * Artist of a first publish Work
     */
    @Column(name = "first_pub")
    private String firstPub;

    /**
     * 0 初始，1同步
     */
    @Column(name = "state")
    private Integer state;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getAritistId() {
        return aritistId;
    }

    public void setAritistId(Long aritistId) {
        this.aritistId = aritistId;
    }

    public Integer getInputSoc() {
        return inputSoc;
    }

    public void setInputSoc(Integer inputSoc) {
        this.inputSoc = inputSoc;
    }

    public String getOriAritist() {
        return oriAritist;
    }

    public void setOriAritist(String oriAritist) {
        this.oriAritist = oriAritist;
    }

    public String getFirstPub() {
        return firstPub;
    }

    public void setFirstPub(String firstPub) {
        this.firstPub = firstPub;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }



}