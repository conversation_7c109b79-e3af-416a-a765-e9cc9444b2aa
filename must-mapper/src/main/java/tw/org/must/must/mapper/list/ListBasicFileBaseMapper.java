package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListBasicFileBase;

@Repository
public interface ListBasicFileBaseMapper extends BaseMapper<ListBasicFileBase> {


    /**
     *  根据list_file_queue的主键id删除关联数据
     *
     * @param fileQueueId
     * @return
     */
    Integer deleteByFileQueueId(@Param("fileQueueId") Long fileQueueId);
}