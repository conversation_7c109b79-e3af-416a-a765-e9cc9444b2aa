package tw.org.must.must.mapper.report;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.report.DistReportCategoryRoyalites;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface DistReportCategoryRoyalitesMapper extends BaseMapper<DistReportCategoryRoyalites> {

     List<DistReportCategoryRoyalites> getRetRoyAmtSum(String distNo);

}