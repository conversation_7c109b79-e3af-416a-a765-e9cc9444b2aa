package tw.org.must.must.core.parse;

import java.math.BigDecimal;
import java.util.*;

import com.firstbrave.api.cwr.vo.*;

import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.enums.GenreEnum;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.redis.NoGener;
import tw.org.must.must.model.cwr.*;
import tw.org.must.must.model.mbr.MbrIpAgreementTerritory;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.wrk.WrkArtist;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;

/**
 * 解析实体转换
 *
 * <AUTHOR>
 */
public class BeanToBeanCwr {

    public static CwrWork transform(TransmissionHeader hdr, Work work, RecordingDetail rec) {
        CwrWork w = new CwrWork();
        w.setCatalogueNo(work.getCatalogueNumber());
        w.setCategory(work.getMusicalWorkDistributionCategory());
        w.setCompositeComponentCount(work.getCompositeComponentCount());
        w.setCompositeType(work.getCompositeType());
        w.setContactId(work.getContactId());
        w.setContactName(work.getContactName());
        w.setCopyrightDate(DateParse.parseDate(work.getCopyrightDate(), "yyyyMMdd"));
        w.setCopyrightNo(work.getCopyrightNumber());
        w.setCwrWorkType(work.getWorkType());
        w.setDuration(work.getDuration());
        w.setEan(rec.getEan());
        w.setExceptionalClause(work.getExceptionalClause());
        w.setExcerptType(work.getExcerptType());
        w.setFirstAlbumLabel(rec.getFirstAlbumLabel());
        w.setFirstAlbumTitle(rec.getFirstAlbumTitle());
        w.setFirstReleaseCatalogNo(rec.getFirstReleaseCatalogNumber());
        w.setFirstReleaseDate(DateParse.parseDate(rec.getFirstReleaseDate(), "yyyyMMdd"));
        w.setFirstReleaseDuration(rec.getFirstReleaseDuration());
        w.setFormat(rec.getRecordingFormat());
        w.setGrandRights(work.getGrandRights());
        w.setIsrc(rec.getIsrc());
        w.setIswc(work.getIswc());
        w.setLanguage(work.getLanguageCode());
        w.setLyricAdaptation(work.getLyricAdaptation());
        w.setMediaType(rec.getMediaType());
        w.setMusicArrangement(work.getMusicArrangement());
        w.setOpusNo(work.getOpusNumber());
        w.setPriority(work.getPriorityFlag());
        w.setPublicationDate(DateParse.parseDate(work.getDatePublicationPrintedEdition(), "yyyyMMdd"));
        w.setRecorded(work.getRecordedIndicator());
        w.setSocCode(hdr.getSenderId().substring(6));
        w.setTechnique(rec.getRecordingTechnique());
        w.setTextMusicRelationship(work.getTextMusicRelationship());
        w.setVersionType(work.getVersionType());
        w.setWorkNo(work.getSubmitterWorkNumber());

        w.setWorkTitle(work.getWorkTitle());
        w.setStatus(0);
        w.init();
        return w;
    }

    public static CwrWorkTitle transform(TransmissionHeader hdr, Work work, AlternateTitle alt) {
        CwrWorkTitle wt = new CwrWorkTitle();
        wt.setLanguage(alt.getLanguageCode());
        wt.setSocCode(hdr.getSenderId().substring(6));
        wt.setTitle(alt.getAlternateTitle());
        wt.setType(alt.getTitleType());
        wt.setWorkNo(work.getSubmitterWorkNumber());
        wt.setStatus(0);
        wt.init();
        return wt;
    }

    public static CwrArtist transform(TransmissionHeader hdr, Work work, PerformingArtist per) {
        CwrArtist a = new CwrArtist();
        a.setFirstName(per.getPerformingArtistFirstName());
        a.setIpBaseNo(per.getPerformingArtistIpiBaseNo());
        a.setIpNameNo(per.getPerformingArtistIpiNameNo());
        a.setLastName(per.getPerformingArtistLastName());
        a.setSocCode(hdr.getSenderId().substring(6));
        a.setWorkNo(work.getSubmitterWorkNumber());
        a.setStatus(0);
        a.init();
        return a;
    }

    public static CwrPublisher transform(TransmissionHeader hdr, Work work, Publisher spu) {
        CwrPublisher p = new CwrPublisher();
        p.setFirstRecordingRefusal(spu.getFirstRecordingRefusal());
        p.setInterestedPartyNo(spu.getInterestedPartyNo());
        p.setIpBaseNo(spu.getPublisherIpiBaseNo());
        p.setIpNameNo(spu.getPublisherIpiNameNo());
        p.setIsac(spu.getInternationalStandardAgreementCode());
        p.setMrShare(spu.getMrOwnershipShare());
        p.setMrSoc(spu.getMrSociety());
        p.setPrShare(spu.getPrOwnershipShare());
        p.setPrSoc(spu.getPrAffiliationSociety());
        p.setPublisherName(spu.getPublisherName());
        p.setPublisherNo(spu.getPublisherSequenceNo());
        p.setPublisherType(spu.getPublisherType());
        p.setSocAgreementNo(spu.getSocietyAssignedAgreementNo());
        p.setSocCode(hdr.getSenderId().substring(6));
        p.setSpecialAgreements(spu.getSpecialAgreementsIndicator());
        p.setSrShare(spu.getSrOwnershipShare());
        p.setSrSoc(spu.getSrSociety());
        p.setSubmitterAgreementNo(spu.getSubmitterAgreementNo());
        p.setTaxId(spu.getTaxIdNo());
        p.setUnknown(spu.getPublisherUnknownIndicator());
        p.setWorkNo(work.getSubmitterWorkNumber());
        p.setStatus(0);
        p.init();
        return p;
    }

    public static CwrPublisherTerritory transform(TransmissionHeader hdr, Work work, PublisherTerritoryOfControl spt) {
        CwrPublisherTerritory pt = new CwrPublisherTerritory();
        pt.setInOrEx(spt.getInclusionExclusionIndicator());
        pt.setInterestedPartyNo(spt.getInterestedPartyNo());
        pt.setMrShare(spt.getMrCollectionShare());
        pt.setPrShare(spt.getPrCollectionShare());
        pt.setSequenceNo(spt.getSequenceNo());
        pt.setSharesChange(spt.getSharesChange());
        pt.setSocCode(hdr.getSenderId().substring(6));
        pt.setSrShare(spt.getSrCollectionShare());
        pt.setTisCode(spt.getTisNumericCode());
        pt.setWorkNo(work.getSubmitterWorkNumber());
        pt.setStatus(0);
        pt.init();
        return pt;
    }

    public static CwrWriter transform(TransmissionHeader hdr, Work work, Writer swr) {
        CwrWriter w = new CwrWriter();
        w.setFirstRecordingRefusal(swr.getFirstRecordingRefusal());
        w.setHire(swr.getWorkForHireIndicator());
        w.setInterestedPartyNo(swr.getInterestedPartyNo());
        w.setIpBaseNo(swr.getWriterIpiBaseNo());
        w.setIpNameNo(swr.getWriterIpiNameNo());
        w.setMrShare(swr.getMrOwnershipShare());
        w.setMrSoc(swr.getMrSociety());
        w.setPersonalNo(swr.getPersonalNo());
        w.setPrShare(swr.getPrOwnershipShare());
        w.setPrSoc(swr.getPrSociety());
        w.setReversionary(swr.getReversionaryIndicator());
        w.setSocCode(hdr.getSenderId().substring(6));
        w.setSrShare(swr.getSrOwnershipShare());
        w.setSrSoc(swr.getSrSociety());
        w.setTaxId(swr.getTaxIdNo());
        w.setUnknown(swr.getWriterUnknownIndicator());
        w.setUsaLicense(swr.getUsaLicense());
        w.setWorkNo(work.getSubmitterWorkNumber());
        w.setWriterCode(swr.getWriterDesignationCode());
        w.setWriterFirstName(swr.getWriterFirstName());
        w.setWriterLastName(swr.getWriterLastName());
        w.setStatus(0);
        w.init();
        return w;
    }

    public static CwrWriterTerritory transform(TransmissionHeader hdr, Work work, WriterTerritoryOfControl swt) {
        CwrWriterTerritory wt = new CwrWriterTerritory();
        wt.setInOrEx(swt.getInclusionExclusionIndicator());
        wt.setInterestedPartyNo(swt.getInterestedPartyNo());
        wt.setMrShare(swt.getMrCollectionShare());
        wt.setPrShare(swt.getPrCollectionShare());
        wt.setSequenceNo(swt.getSequenceNo());
        wt.setSharesChange(swt.getSharesChange());
        wt.setSocCode(hdr.getSenderId().substring(6));
        wt.setSrShare(swt.getSrCollectionShare());
        wt.setTisCode(swt.getTisNumericCode());
        wt.setWorkNo(work.getSubmitterWorkNumber());
        wt.setStatus(0);
        wt.init();
        return wt;
    }

    public static CwrPublisherWriter transform(TransmissionHeader hdr, Work work, PublisherForWriter pwr) {
        CwrPublisherWriter p = new CwrPublisherWriter();
        p.setWorkNo(work.getSubmitterWorkNumber());
        p.setSocCode(hdr.getSenderId().substring(6));
        p.setWriterIpNo(pwr.getWriterIpNo());
        p.setPublisherNo(pwr.getPublisherIpNo());
        p.setPublisherName(pwr.getPublisherName());
        p.setSubmitterAgreementNo(pwr.getSubmitterAgreementNo());
        p.setSocietyAssignedAgreementNo(pwr.getSocietyAssignedAgreementNo());
        p.init();
        return p;
    }

    public static TransmissionHeader transformToHdr() {
        TransmissionHeader hdr = new TransmissionHeader();
        Date date = new Date();
        hdr.setCharacterSet(null);
        hdr.setCreationDate(DateParse.format(date, "yyyyMMdd"));
        hdr.setCreationTime(DateParse.format(date, "hhmmss"));
        hdr.setRecordType("HDR");
        hdr.setSenderId("000000161");
        hdr.setSenderName("MUST society");
        hdr.setSenderType("SO");
        hdr.setStandardVersionNumber("01.10");
        hdr.setTransmissionDate(DateParse.format(date, "yyyyMMdd"));
        hdr.setCharacterSet("utf-8");
        return hdr;
    }

    public static GroupHeader transformToGrh(int groupId) {
        GroupHeader grh = new GroupHeader();
        long batchNo = NoGener.getIncrement("CWR_BATCH_REQUEST_NO");
        grh.setBatchRequest((int) batchNo);
        grh.setGroupId(groupId);
        grh.setRecordType("GRH");
        grh.setSubmissionType(null);
        grh.setTransactionType("REV");
        grh.setVersionNumber("02.10");
        return grh;
    }

    public static GroupTrailer transformToGrt(int groupId, int recordCount, int transactionCount) {
        GroupTrailer grt = new GroupTrailer();
        grt.setCurrencyIndicator(null);
        grt.setGroupId(groupId);
        grt.setRecordCount(recordCount);
        grt.setRecordType("GRT");
        grt.setTotalMonetaryValue(null);
        grt.setTransactionCount(transactionCount);
        return grt;
    }

    public static TransmissionTrailer transformToTrl(int groupCount, int recordCount, int transactionCount) {
        TransmissionTrailer trl = new TransmissionTrailer();
        trl.setGroupCount(groupCount);
        trl.setRecordCount(recordCount);
        trl.setRecordType("TRL");
        trl.setTransactionCount(transactionCount);
        return trl;
    }

    public static Work transformToRev(WrkWork work, WrkWorkTitle title, int transactionSequenceNo,
                                      int recordSequenceNo) {
        Work rev = new Work();
        rev.setCatalogueNumber(null);
        rev.setCompositeComponentCount(null);
        rev.setCompositeType(null);
        rev.setContactId(null);
        rev.setContactName(null);
        rev.setCopyrightDate(null);
        rev.setCopyrightNumber(null);
        rev.setDatePublicationPrintedEdition(null);
        rev.setDuration(null);
        rev.setExceptionalClause(null);
        rev.setExcerptType(null);
        rev.setGrandRights(null);
        rev.setIswc(null);
        rev.setLanguageCode(null);
        rev.setLyricAdaptation(null);
        //Genre 曲风对应关系
        if (StringUtils.isNotBlank(title.getGenreCode())) {
            try {
                String category = Enum.valueOf(GenreEnum.class, title.getGenreCode()).getGenre();
                rev.setMusicalWorkDistributionCategory(category);
            } catch (IllegalArgumentException e) {
            }
        }
        rev.setMusicArrangement(null);
        rev.setOpusNumber(null);
        rev.setPriorityFlag(null);
        rev.setRecordedIndicator("Y");
        rev.setRecordSequenceNo(recordSequenceNo);
        rev.setRecordType("REV");
        rev.setSubmitterWorkNumber(String.valueOf(work.getWorkId()));
        rev.setTextMusicRelationship(null);
        rev.setTransactionSequenceNo(transactionSequenceNo);
        rev.setVersionType("ORI");
        rev.setWorkTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
        rev.setWorkType(null);
        return rev;
    }

    public static Publisher transformToSpu(List<WrkWorkIpShare> shares, MbrIpName ipName, int transactionSequenceNo,
                                           int recordSequenceNo) {
        Publisher spu = new Publisher();
        spu.setAgreementType(null);
        spu.setFiller(null);
        spu.setFirstRecordingRefusal(null);
        spu.setInterestedPartyNo(String.valueOf(ipName.getId()));
        spu.setInternationalStandardAgreementCode(null);
        spu.setPublisherIpiBaseNo(shares.get(0).getIpBaseNo());
        spu.setPublisherIpiNameNo(shares.get(0).getIpNameNo());
        spu.setPublisherName(ipName.getName());
        spu.setPublisherSequenceNo(Integer.valueOf(shares.get(0).getGroupIndicator()));
        spu.setPublisherType(shares.get(0).getWorkIpRole());
        spu.setPublisherUnknownIndicator(null);
        spu.setRecordSequenceNo(recordSequenceNo);
        spu.setRecordType("SPU");
        spu.setSocietyAssignedAgreementNo(null);
        spu.setSpecialAgreementsIndicator(null);
        spu.setSubmitterAgreementNo(null);
        spu.setTaxIdNo(null);
        spu.setTransactionSequenceNo(transactionSequenceNo);
        spu.setUsaLicense(null);
        shares.forEach(share -> {
            if ("PER".equals(share.getRightType())) {
                spu.setPrAffiliationSociety(String.valueOf(share.getWorkSocietyCode()));
                spu.setPrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("MEC".equals(share.getRightType())) {
                spu.setMrSociety(String.valueOf(share.getWorkSocietyCode()));
                spu.setMrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("ZYN".equals(share.getRightType())) {
                spu.setSrSociety(String.valueOf(share.getWorkSocietyCode()));
                spu.setSrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
            }
        });
        return spu;
    }

    public static PublisherTerritoryOfControl transformToSpt(List<WrkWorkIpShare> shares, MbrIpName ipName,
                                                             MbrIpAgreementTerritory at, int sptSequenceNo, int transactionSequenceNo, int recordSequenceNo) {
        PublisherTerritoryOfControl spt = new PublisherTerritoryOfControl();
        spt.setConstant(null);
        spt.setInclusionExclusionIndicator("+".equals(at.getIndicator()) ? "I" : "E");
        spt.setInterestedPartyNo(String.valueOf(ipName.getId()));
        spt.setRecordSequenceNo(recordSequenceNo);
        spt.setRecordType("SPT");
        spt.setSequenceNo(sptSequenceNo);
        spt.setSharesChange(null);
        spt.setTisNumericCode(String.valueOf(at.getTisN()));
        spt.setTransactionSequenceNo(transactionSequenceNo);
        shares.forEach(share -> {
            if ("PER".equals(share.getRightType())) {
                spt.setPrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("MEC".equals(share.getRightType())) {
                spt.setMrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("ZYN".equals(share.getRightType())) {
                spt.setSrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            }
        });
        return spt;
    }

    public static Writer transformToSwr(WrkWorkIpShare share, MbrIpName ipName, int transactionSequenceNo,
                                        int recordSequenceNo) {
        Writer swr = new Writer();
        swr.setFiller(null);
        swr.setFirstRecordingRefusal(null);
        swr.setInterestedPartyNo(String.valueOf(ipName.getId()));
        swr.setPersonalNo(null);
        swr.setRecordSequenceNo(recordSequenceNo);
        swr.setRecordType("SWR");
        swr.setReversionaryIndicator(null);
        swr.setTaxIdNo(null);
        swr.setTransactionSequenceNo(transactionSequenceNo);
        swr.setUsaLicense(null);
        swr.setWorkForHireIndicator(null);
        swr.setWriterDesignationCode(share.getWorkIpRole());
        swr.setWriterFirstName(ipName.getFirstName());
        swr.setWriterIpiBaseNo(share.getIpBaseNo());
        swr.setWriterIpiNameNo(share.getIpNameNo());
        swr.setWriterLastName(ipName.getLastName());
        swr.setWriterUnknownIndicator("");

        if ("PER".equals(share.getRightType())) {
            swr.setPrSociety(String.valueOf(share.getWorkSocietyCode()));
            swr.setPrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
        } else if ("MEC".equals(share.getRightType())) {
            swr.setMrSociety(String.valueOf(share.getWorkSocietyCode()));
            swr.setMrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
        } else if ("ZYN".equals(share.getRightType())) {
            swr.setSrSociety(String.valueOf(share.getWorkSocietyCode()));
            swr.setSrOwnershipShare((int) (share.getIpShare().doubleValue() * 100));
        }

        return swr;
    }

    public static WriterTerritoryOfControl transformToSwt(List<WrkWorkIpShare> shares, MbrIpName ipName,
                                                          MbrIpAgreementTerritory at, int transactionSequenceNo, int recordSequenceNo) {
        WriterTerritoryOfControl swt = new WriterTerritoryOfControl();
        if (Objects.nonNull(at)) {
            swt.setInclusionExclusionIndicator("+".equals(at.getIndicator()) ? "I" : "E");
            swt.setTisNumericCode(String.valueOf(at.getTisN()));
        }
        swt.setInterestedPartyNo(String.valueOf(ipName.getId()));
        swt.setRecordSequenceNo(recordSequenceNo);
        swt.setRecordType("SWT");
//		swt.setSequenceNo(swtSequenceNo);
        swt.setSharesChange(null);
        swt.setTransactionSequenceNo(transactionSequenceNo);
        shares.forEach(share -> {
            if ("PER".equals(share.getRightType())) {
                swt.setPrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("MEC".equals(share.getRightType())) {
                swt.setMrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            } else if ("ZYN".equals(share.getRightType())) {
                swt.setSrCollectionShare((int) (share.getIpShare().doubleValue() * 100));
            }
        });
        return swt;
    }

    public static PerformingArtist transformToPer(WrkArtist artist, int transactionSequenceNo, int recordSequenceNo) {
        PerformingArtist per = new PerformingArtist();
        per.setPerformingArtistFirstName(artist.getFirstName());
        per.setPerformingArtistIpiBaseNo(artist.getIpBaseNo());
        per.setPerformingArtistIpiNameNo(artist.getIpNameNo());
        per.setPerformingArtistLastName(artist.getLastName());
        per.setRecordSequenceNo(recordSequenceNo);
        per.setRecordType("PER");
        per.setTransactionSequenceNo(transactionSequenceNo);
        return per;
    }

    public static AlternateTitle transformToAlt(WrkWorkTitle title, int transactionSequenceNo, int recordSequenceNo) {
        AlternateTitle alt = new AlternateTitle();
        alt.setAlternateTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
        alt.setLanguageCode(title.getLanguageCode());
        alt.setRecordSequenceNo(recordSequenceNo);
        alt.setRecordType("ALT");
        alt.setTitleType(title.getTitleType());
        alt.setTransactionSequenceNo(transactionSequenceNo);
        return alt;
    }

    /***
     * @Description:
     * @Param: [wrkWork]
     * @return: com.firstbrave.api.cwr.vo.Work
     * @Author: hanDa
     * @Date: 2020/9/27 17:02
     */
    public static Work transformToWork(WrkWork wrkWork, int transactionSequenceNo,
                                       int recordSequenceNo) {
        Work work = new Work();
        //作品类别：一般，改词，改曲 目前写死为 NWR（解析中对应work的类型有NWR，REV，ISW，EXC FIXME
        work.setRecordType("NWR");

        if (StringUtils.equalsAnyIgnoreCase(wrkWork.getWorkType(), "ARR", "ADP")) {
            work.setVersionType("MOD");
        } else {
            work.setVersionType("ORI");
        }
        work.setTransactionSequenceNo(transactionSequenceNo);
        work.setRecordSequenceNo(recordSequenceNo);
        work.setWorkType(wrkWork.getWorkType());
        work.setIswc(wrkWork.getISWC());
        work.setLanguageCode(wrkWork.getPerformLanguage());
        work.setSubmitterWorkNumber(String.valueOf(wrkWork.getWorkId()));
        work.setWorkTitle(wrkWork.getTitle());
        work.setMusicalWorkDistributionCategory(wrkWork.getGenre());
        //4.	第136字元Recorded Indicator Recorded(這首歌是否被錄製過?不確定預設值一律填Y) Indicator must be equal to “Y”, “N”, or “U”. (FR - default to “U”)
        work.setRecordedIndicator("Y");

       // work.setCwrLanguageCode(wrkWork.getPerformLanguage());

        //TODO  类别有个对应关系
        //work.setCatalogueNumber(wrkWork.getCatalogueNo());
		/*
		work.setMusicalWorkDistributionCategory(wrkWork.getCategory());
		work.setCompositeComponentCount(wrkWork.getCompositeComponentCount());
		work.setCompositeType(wrkWork.getCompositeType());
		work.setContactId(wrkWork.getContactId());
		work.setContactName(wrkWork.getContactName());
		work.setCopyrightDate(DateParse.format(wrkWork.getCopyrightDate(), "yyyyMMdd"));
		work.setCopyrightNumber(wrkWork.getCopyrightNo());*/
		/*work.setDuration(MatchUtil.getDuration(wrkWork.getDurationM(),wrkWork.getDurationS()));
		work.setExceptionalClause(wrkWork.getExceptionalClause());
		work.setExcerptType(wrkWork.getExcerptType());
		work.setGrandRights(wrkWork.getGrandRights());
		*/
		/*work.setLyricAdaptation(wrkWork.getLyricAdaptation());
		work.setMusicArrangement(wrkWork.getMusicArrangement());
		work.setOpusNumber(wrkWork.getOpusNo());
		work.setPriorityFlag(wrkWork.getPriority());
		work.setDatePublicationPrintedEdition(DateParse.format(wrkWork.getPublicationDate(), "yyyyMMdd"));
		work.setRecordedIndicator(wrkWork.getRecorded());
		work.setTextMusicRelationship(wrkWork.getTextMusicRelationship());
		work.setVersionType(wrkWork.getVersionType());*/
        return work;
    }


    /***
     * @Description:
     * @Param: [wrkWork]
     * @return: com.firstbrave.api.cwr.vo.RecordingDetail
     * @Author: hanDa
     * @Date: 2020/9/27 17:09
     */
    public static RecordingDetail transformToRecordingDetail(WrkWork wrkWork, int transactionSequenceNo,
                                                             int recordSequenceNo) {
        RecordingDetail recordingDetail = new RecordingDetail();
        recordingDetail.setRecordType("REC");
        recordingDetail.setTransactionSequenceNo(transactionSequenceNo);
        recordingDetail.setRecordSequenceNo(recordSequenceNo);
        recordingDetail.setFirstReleaseDate(DateParse.format(wrkWork.getFirstCreateDate(), "yyyyMMdd"));

		/*recordingDetail.setEan(wrkWork.getEan());
		recordingDetail.setFirstAlbumLabel(wrkWork.getFirstAlbumLabel());
		recordingDetail.setFirstAlbumTitle(wrkWork.getFirstAlbumTitle());
		recordingDetail.setFirstReleaseCatalogNo(wrkWork.getFirstReleaseCatalogNumber());
		recordingDetail.setFirstReleaseDuration(wrkWork.getFirstReleaseDuration());
		recordingDetail.setRecordingFormat(wrkWork.getFormat());
		recordingDetail.setIsrc(wrkWork.getIsrc());
		recordingDetail.setMediaType(wrkWork.getMediaType());
		recordingDetail.setRecordingTechnique(wrkWork.getTechnique());*/

        return recordingDetail;
    }

    /***
     * @Description:
     * @Param: [wrkWork, title, transactionSequenceNo, recordSequenceNo]
     * @return: com.firstbrave.api.cwr.vo.AlternateTitle
     * @Author: hanDa
     * @Date: 2020/9/27 17:23
     */
    public static AlternateTitle transformToAlternateTitle(WrkWorkTitle title, int transactionSequenceNo,
                                                           int recordSequenceNo) {
        AlternateTitle alternateTitle = new AlternateTitle();
        alternateTitle.setRecordType("ALT");
        alternateTitle.setTransactionSequenceNo(transactionSequenceNo);
        alternateTitle.setRecordSequenceNo(recordSequenceNo);

        alternateTitle.setLanguageCode(title.getLanguageCode());
        String titleStr = title.getTitleEn();
        alternateTitle.setAlternateTitle(titleStr);
        alternateTitle.setTitleType(title.getTitleType());

        return alternateTitle;

    }

    /***
     * @Description:
     * @Param: [wrkWork, title, transactionSequenceNo, recordSequenceNo]
     * @return: com.firstbrave.api.cwr.vo.AlternateTitle
     * @Author: hanDa
     * @Date: 2020/9/27 17:23
     */
    public static NonRomanAlphabetTitle transformToNonRomanAlphabetTitle(WrkWorkTitle title, int transactionSequenceNo,
                                                                         int recordSequenceNo) {
        NonRomanAlphabetTitle alternateTitle = new NonRomanAlphabetTitle();
        alternateTitle.setRecordType("NAT");
        alternateTitle.setTransactionSequenceNo(transactionSequenceNo);
        alternateTitle.setRecordSequenceNo(recordSequenceNo);

        alternateTitle.setLanguageCode(title.getLanguageCode());
//        String titleStr = StringUtils.isNotEmpty(title.getTitle()) ? title.getTitle() : title.getTitleEn();
        alternateTitle.setTitle(title.getTitle());
        alternateTitle.setTitleType(title.getTitleType());

        return alternateTitle;

    }

    /***
     * @Description:
     * @Param: [wrkWork, wrkArtist, transactionSequenceNo, recordSequenceNo]
     * @return: com.firstbrave.api.cwr.vo.PerformingArtist
     * @Author: hanDa
     * @Date: 2020/9/27 18:14
     */
    public static PerformingArtist transformToPerformingArtist(WrkArtist wrkArtist, int transactionSequenceNo,
                                                               int recordSequenceNo) {
        PerformingArtist performingArtist = new PerformingArtist();
        performingArtist.setRecordType("PER");
        performingArtist.setTransactionSequenceNo(transactionSequenceNo);
        performingArtist.setRecordSequenceNo(recordSequenceNo);

        String name = wrkArtist.getName();



        String firstName = wrkArtist.getFirstName();
        performingArtist.setPerformingArtistFirstName(firstName);
        performingArtist.setPerformingArtistIpiBaseNo(wrkArtist.getIpBaseNo());
        performingArtist.setPerformingArtistIpiNameNo(wrkArtist.getIpNameNo());

        String lastName = wrkArtist.getLastName();
        if (StringUtils.isEmpty(lastName)) {
            if (StringUtils.isNotEmpty(name)) {
                if (StringUtils.isEmpty(firstName) || name.length() <= firstName.length()) {
                    lastName = name;
                } else {
                    lastName = name.substring(firstName.length());
                }
            } else {
                lastName = wrkArtist.getChineseName();
            }
        }

        performingArtist.setPerformingArtistLastName(lastName);
        return performingArtist;
    }


    public static NonRomanAlphabetPerformanceData transformToNonRomanAlphabetPerformanceData(WrkArtist wrkArtist, int transactionSequenceNo,
                                                               int recordSequenceNo) {
        NonRomanAlphabetPerformanceData performingArtist = new NonRomanAlphabetPerformanceData();
        performingArtist.setRecordType("NPR");
        performingArtist.setTransactionSequenceNo(transactionSequenceNo);
        performingArtist.setRecordSequenceNo(recordSequenceNo);

        String name = wrkArtist.getName();



        String firstName = wrkArtist.getFirstName();
        performingArtist.setPerformingArtistFirstName(firstName);
        performingArtist.setPerformingArtistIpiBaseNo(wrkArtist.getIpBaseNo());
        performingArtist.setPerformingArtistIpiNameNo(wrkArtist.getIpNameNo());

        String lastName = wrkArtist.getLastName();
        if (StringUtils.isEmpty(lastName)) {
            if (StringUtils.isNotEmpty(name)) {
                if (StringUtils.isEmpty(firstName) || name.length() <= firstName.length()) {
                    lastName = name;
                } else {
                    lastName = name.substring(firstName.length());
                }
            } else {
                lastName = wrkArtist.getChineseName();
            }
        }

        performingArtist.setPerformingArtistName(lastName);
        return performingArtist;
    }


    /****
     * @Description:
     * @Param: [mbrIpName, wrkWorkIpShare, publisherIpshares, transactionSequenceNo, recordSequenceNo]
     * @return: com.firstbrave.api.cwr.vo.Publisher
     * @Author: hanDa
     * @Date: 2020/10/10 16:45
     */
    public static Publisher transformToPublisher(MbrIpName mbrIpName, WrkWorkIpShare wrkWorkIpShare, List<WrkWorkIpShare> publisherIpshares, int transactionSequenceNo,
                                                 int recordSequenceNo, boolean isAscii) {
        Optional<WrkWorkIpShare> wrkWorkIpShareMEC = publisherIpshares.stream().filter(publisherIpshare -> "MEC".equals(publisherIpshare.getRightType())).findFirst().or(() -> Optional.of(new WrkWorkIpShare()));
        Optional<WrkWorkIpShare> wrkWorkIpSharePER = publisherIpshares.stream().filter(publisherIpshare -> "PER".equals(publisherIpshare.getRightType())).findFirst();
        Publisher publisher = new Publisher();
        // 写死为其中一个
        if(!isAscii){
            publisher.setRecordType("NPN");
        }else{
            publisher.setRecordType("SPU");
        }
        publisher.setTransactionSequenceNo(transactionSequenceNo);
        publisher.setRecordSequenceNo(recordSequenceNo);
        //ip share表中的ip base no
        publisher.setPublisherIpiBaseNo(wrkWorkIpShareMEC.get().getIpBaseNo());
        //ip share 表中 ip name no
        publisher.setPublisherIpiNameNo(wrkWorkIpShareMEC.get().getIpNameNo());
        Integer ipShare = wrkWorkIpShareMEC.get().getIpShare() == null ? 0 : wrkWorkIpShareMEC.get().getIpShare().intValue();
        //ip share表中，right type=MEC的 ip share
        publisher.setMrOwnershipShare(ipShare);
        // ip share表中，right type=MEC的 ip soc，数据库中未存储，周兵有相应方法可以查的到
        if (wrkWorkIpShare != null && wrkWorkIpShare.getWorkSocietyCode() != null) {
            publisher.setMrSociety(StringUtils.leftPad(wrkWorkIpShare.getWorkSocietyCode().toString(), 3, "0"));
        }
//		publisher.setPublisherType(shares.get(0).getWorkIpRole());
        //ip share表中，right type=PER的 ip share
        publisher.setPrOwnershipShare(ipShare);
        //ip share表中，right type=PER的 ip soc，数据库中未存储，周兵有相应方法可以查的到
        if (wrkWorkIpSharePER.isPresent()) {
            publisher.setPrAffiliationSociety(StringUtils.leftPad(wrkWorkIpSharePER.get().getWorkSocietyCode().toString(), 3, "0"));
        }

        if (mbrIpName == null) {
            //根据 name no到 mbr_ip_name表中查询，如果name no为空获取ip share表中的 dummy_name值
            publisher.setPublisherName(wrkWorkIpShareMEC.get().getDummyName());
        } else {
            //根据 name no到 mbr_ip_name表中查询，如果name no为空获取ip share表中的 dummy_name值
            publisher.setPublisherName(mbrIpName.getName());
        }
        //ip share表中，right type=ZYN的 ip share
        publisher.setSrOwnershipShare(ipShare * 100);
        publisher.setPublisherType(wrkWorkIpShare.getWorkIpRole());
        /// ip share表中，right type=ZYN的 ip soc，数据库中未存储，周兵有相应方法可以查的到
/*		if (wrkWorkIpShare != null){
			publisher.setMrSociety(wrkWorkIpShare.getWorkSocietyCode()+"");
		}*/

        //TODO  对应ip share中为E的数据（需要调周兵接口获取最新ip share，ip share中会包含SE数据，确认下原始CWR档案中是否有SE数据）
        // 原始文档中有SE类型数据
		/*publisher.setFirstRecordingRefusal(publisherIpshare.getFirstRecordingRefusal());
		publisher.setPublisherNo(publisherIpshare.getPublisherSequenceNo()); //
		publisher.setInterestedPartyNo(publisherIpshare.getInterestedPartyNo());
		publisher.setIsac(spu.getInternationalStandardAgreementCode());

		publisher.setPublisherType(publisherIpshare.getPublisherType());  //
		publisher.setSocAgreementNo(publisherIpshare.getSocietyAssignedAgreementNo());
		publisher.setSocCode(publisherIpshare.getSenderId().substring(6));    //ip soc
		publisher.setSpecialAgreements(publisherIpshare.getSpecialAgreementsIndicator());
				publisher.setSubmitterAgreementNo(publisherIpshare.getSubmitterAgreementNo());
		publisher.setTaxId(publisherIpshare.getTaxIdNo());
		publisher.setUnknown(publisherIpshare.getPublisherUnknownIndicator());*/

        return publisher;

    }

    /***
     * @Description: 字段对应
     * @Param: [wrkWork, publisherIpshare, transactionSequenceNo, recordSequenceNo]
     * @return: com.firstbrave.api.cwr.vo.Publisher
     * @Author: hanDa
     * @Date: 2020/9/27 20:05
     */
    public static Writer transformToWriter(MbrIpName mbrIpName, WrkWorkIpShare wrkWorkIpShare1, List<WrkWorkIpShare> wrkWorkIpShares, int transactionSequenceNo,
                                           int recordSequenceNo) {
        Optional<WrkWorkIpShare> wrkWorkIpShareMEC = wrkWorkIpShares.stream()/*.filter(publisherIpshare->"MEC".equals(publisherIpshare.getRightType()))*/.findFirst();
        WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareMEC.get();
        Integer ipShare = wrkWorkIpShareMEC.get().getIpShare() == null ? 0 : wrkWorkIpShareMEC.get().getIpShare().intValue();
        Writer writer = new Writer();
        // 写死为其中一个
        writer.setRecordType("SWR");
        writer.setInterestedPartyNo(mbrIpName.getIpNameNo().substring(1));
        writer.setTransactionSequenceNo(transactionSequenceNo);
        writer.setRecordSequenceNo(recordSequenceNo);
        writer.setWriterIpiBaseNo(wrkWorkIpShare.getIpBaseNo());
        writer.setWriterIpiNameNo(wrkWorkIpShare.getIpNameNo());
        writer.setPrOwnershipShare((wrkWorkIpShare.getIpShare() == null ? BigDecimal.ZERO : wrkWorkIpShare.getIpShare()).multiply(new BigDecimal(100)).intValue());
        writer.setWriterDesignationCode(wrkWorkIpShare.getWorkIpRole());
        writer.setWriterLastName(mbrIpName.getName());
        writer.setWriterFirstName(mbrIpName.getFirstName());
        //ip share表中，right type=ZYNs的 ip share
        writer.setSrOwnershipShare(ipShare * 100);
        /// ip share表中，right type=ZYN的 ip soc，数据库中未存储，周兵有相应方法可以查的到
        if (wrkWorkIpShare != null) {
            writer.setMrSociety(wrkWorkIpShare.getWorkSocietyCode() + "");
        }
        //ip share表中，right type=MEC的 ip share
        writer.setMrOwnershipShare(ipShare);

        //字段对应同publisher ，role 为非E、SE数据
		/*writer.setFirstRecordingRefusal(publisherIpshare.getFirstRecordingRefusal());
		writer.setWorkForHireIndicator(publisherIpshare.getHire());
		writer.setInterestedPartyNo(publisherIpshare.getInterestedPartyNo());
		writer.setMrOwnershipShare(publisherIpshare.getMrShare());
		writer.setMrSociety(publisherIpshare.getMrSoc());
		writer.setPersonalNo(publisherIpshare.getPersonalNo());
		writer.setPrSociety(publisherIpshare.getPrSoc());
		writer.setReversionaryIndicator(publisherIpshare.getReversionary());
		writer.setSrOwnershipShare(publisherIpshare.getSrShare());
		writer.setTaxIdNo(publisherIpshare.getTaxId());
		writer.setWriterUnknownIndicator(publisherIpshare.getUnknown());
		writer.setUsaLicense(publisherIpshare.getUsaLicense());*/

		/*String dummyName = publisherIpshare.getDummyName();
		if (StringUtils.isNotBlank(dummyName)){
			writer.setWriterFirstName(publisherIpshare.getDummyName().substring());
			writer.setWriterLastName(publisherIpshare.getDummyName().substring());
		}*/
        return writer;
    }
}
