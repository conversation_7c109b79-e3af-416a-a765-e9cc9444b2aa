package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistCalcRetain;

import java.util.List;

@Repository
public interface DistCalcRetainMapper extends BaseMapper<DistCalcRetain> {

    List<String> listDistinctDistNoFromDistCalcRetain();

    List<String> getIpBaseNoByDistNo(@Param("distNo") String distNo);

    List<Integer> getIpSocietyCodeByDistNo(@Param("distNo") String distNo);

    List<DistCalcRetain> getRetainByDistNoAndType(@Param("distNo") String distNo,@Param("type") String type);

}