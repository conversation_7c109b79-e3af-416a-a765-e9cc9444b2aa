package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalWrkWorkTransfer;

@Repository
public interface OrcalWrkWorkTransferMapper {

	List<OrcalWrkWorkTransfer> getWrkWorkTransfer(String lastAmendTime);

	List<OrcalWrkWorkTransfer> getWrkWorkTransferBySourceNoAndSoc(Integer workNum, Integer workNumSociety);

    List<OrcalWrkWorkTransfer> getWrkWorkTransferByTransferTime(String lastProcessDate);

	List<OrcalWrkWorkTransfer> getWrkWorkTransferByProcessOrTransferTime(String lastAmendTime);
}
