package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimFilterOtherWork;
import tw.org.must.must.model.claim.ClaimFilterOthersoc;

import java.util.List;

@Repository
public interface ClaimFilterOtherWorkMapper extends BaseMapper<ClaimFilterOtherWork> {


    @Select("delete from claim_filter_other_work where company = #{company}")
    void deleteByCompany(@Param("company") String company);

    @Select("select id,work_unique_key from ${tableName} where company = #{company} and id > #{startId} order by id asc limit 100000")
    List<ClaimFilterOtherWork> getWorkUniqueKeyByCompany(@Param("tableName") String tableName, @Param("company") String company, @Param("startId") Long startId);
}