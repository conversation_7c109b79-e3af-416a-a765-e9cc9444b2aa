package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_mrd_sc_work`")
public class DistMrdScWork extends BaseEntity {

    /**
     * dist_mrd_sc表id
     */
    @Column(name = "`sc_id`")
    private Long scId;

    /**
     * 暂不知道用途，先不维护
     */
    @Column(name = "`side`")
    private String side;

    /**
     * 序号
     */
    @Column(name = "`seq_no`")
    private Integer seqNo;

    /**
     * 作品id
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 作品协会
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 作品标题id
     */
    @Column(name = "`title_id`")
    private Long titleId;

    /**
     * 作品唯一标识
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    /**
     * 作品时长，分
     */
    @Column(name = "`duration_m`")
    private Integer durationM;

    /**
     * 作品时长，秒
     */
    @Column(name = "`duration_s`")
    private Integer durationS;

    /**
     * isrc编码
     */
    @Column(name = "`isrc`")
    private String isrc;

    /**
     * claim标记，Y or N
     */
    @Column(name = "`sc_claim_flag`")
    private String scClaimFlag;

    /**
     * claim日期
     */
    @Column(name = "`sc_claim_date`")
    private Date scClaimDate;

    /**
     * 汇款代码
     */
    @Column(name = "`remit_code`")
    private String remitCode;

    /**
     * 创建用户
     */
    @Column(name = "`create_user`")
    private String createUser;

    /**
     * 修改用户
     */
    @Column(name = "`amend_user`")
    private String amendUser;

    @Column(name = "work_title")
    private String workTitle;

    /***
    * ADD,UPDATE,DEL
    */
    @Transient
    private String action;

    @Transient
    private String genreCode;

    /***
    * RED,WHITE,GREEN
    */
    @Column(name = "`check`")
    private String check;

    public String getGenreCode() {
        return genreCode;
    }

    public void setGenreCode(String genreCode) {
        this.genreCode = genreCode;
    }

    public String getCheck() {
        return check;
    }

    public void setCheck(String check) {
        this.check = check;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getScId() {
        return scId;
    }

    public void setScId(Long scId) {
        this.scId = scId;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Integer getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Integer seqNo) {
        this.seqNo = seqNo;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getTitleId() {
        return titleId;
    }

    public void setTitleId(Long titleId) {
        this.titleId = titleId;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public String getIsrc() {
        return isrc;
    }

    public void setIsrc(String isrc) {
        this.isrc = isrc;
    }

    public String getScClaimFlag() {
        return scClaimFlag;
    }

    public void setScClaimFlag(String scClaimFlag) {
        this.scClaimFlag = scClaimFlag;
    }

    public Date getScClaimDate() {
        return scClaimDate;
    }

    public void setScClaimDate(Date scClaimDate) {
        this.scClaimDate = scClaimDate;
    }

    public String getRemitCode() {
        return remitCode;
    }

    public void setRemitCode(String remitCode) {
        this.remitCode = remitCode;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getAmendUser() {
        return amendUser;
    }

    public void setAmendUser(String amendUser) {
        this.amendUser = amendUser;
    }



}