package tw.org.must.must.mapper.match;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWrkMainInfoI;
import tw.org.must.must.model.match.vo.MatchWrkMainInfoIVo;

import java.util.List;

@Repository
public interface MatchWrkMainInfoIMapper extends BaseMapper<MatchWrkMainInfoI> {

	Integer insertOrUpdateByList(List<MatchWrkMainInfoI> matchWrkDetailInfoList);

	List<MatchWrkMainInfoI> getMatchWrkDetaiInfoByStatusAndLimit(int count, int index);

	Long countByStatus(List<Integer> statusList);

	List<MatchWrkMainInfoIVo> getMatchWrkMainInfoIList(MatchWrkMainInfoIVo matchWrkMainInfoIVo);


}