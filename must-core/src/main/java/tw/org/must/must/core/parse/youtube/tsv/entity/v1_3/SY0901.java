package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;

@Data
public class SY0901 extends BaseTsvEntity {
    private String SummaryRecordId;
    private String CommercialModel;
    private String UseType;
    private String Territory;
    private String ServiceDescription;
    private String SubscriberType;
    private String RightsController;
    private String RightsControllerPartyId;
    private String RightsType;
    private String TotalUsages;
    private String AllocatedUsages;
    private String NetRevenue;
    private String IndirectValue;
    private String RightsControllerMarketShare;
    private String CurrencyOfReporting;
    private String CurrencyOfTransaction;
    private String ExchangeRate;
    private String RightsTypePercentage;
    private String SubPeriodStartDate;
    private String SubPeriodEndDate;
    private String ParentSummaryRecordId;
}
