package tw.org.must.must.core.parse.youtube.tsv.handle;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.enums.DeletedEnum;
import tw.org.must.must.common.enums.IsShowEnum;
import tw.org.must.must.common.util.ConvertUtils;
import tw.org.must.must.core.parse.DspDataTemp;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.*;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.dsp.DspListUniqueKeyHandler;
import tw.org.must.must.core.service.list.ListDspFileBaseExtendsService;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileBaseExtends;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListFileQueue;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_1000;

public class ParseYoutubeTsvV2Service extends DspListUniqueKeyHandler {
    Logger logger = LoggerFactory.getLogger(ParseYoutubeTsvService.class);

    List<Sy0401> Sy0401List = new ArrayList<>();
    List<SY0202> Sy0202List = new ArrayList<>();
    List<Sy0502> Sy0502List = new ArrayList<>();
    List<Sy09> Sy09List = new ArrayList<>();
    Header header;
    Map<String, Long> connectListDspFileBaseMap = new HashMap<>();
    Map<String, Boolean> isAvodMap = new HashMap<>();
    Map<String, String> dspFileBaseIdMap = new HashMap<>();
    boolean isParse = false;
    String nowBlockId = "";
    BigDecimal totalSy0202Usage = BigDecimal.ZERO;
    BigDecimal totalRu0101Usage = BigDecimal.ZERO;
    List<TsvCommonObject> tsvCommonObjectList = new ArrayList<>();
    Footer footer;

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ListDspFileBaseExtendsService listDspFileBaseExtendsService;

    @Autowired
    private ListDspFileDataMappingService listDspFileDataMappingService;


    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;
    private ThreadLocal<TsvCommonObject> tsvCommonObjectThreadLocal = new ThreadLocal<>();
    private ThreadLocal<Su0302> nowSu0302ThreadLocal = new ThreadLocal<>();
    private BigDecimal totalClickNumber = new BigDecimal("0");
    private Map<Long, BigDecimal> fileBaseIdTotalClickNumMap = new HashMap<>();
    private ListFileQueue listFileQueue;

    private static void dealWithString(String line, Map<String, String> map) {
        String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
        split[0] = split[0].replace("#", "");
        map.put(split[0], line);
    }

    @Transactional
    public void parse(ListFileQueue lfq) throws Exception {
        init();
        listFileQueue = lfq;
        parse(lfq.getFilePath());
        //解析完， 同步base表中mapping的数量
        List<ListDspFileBase> listDspFileBases = listDspFileBaseService.getListDspFileBaseByQueueId(lfq.getId());
        if (!listDspFileBases.isEmpty()) {
            listDspFileDataMappingService.updateFileBaseCountByDspFileBaseInfo(listDspFileBases, fileBaseIdTotalClickNumMap);
        }
    }

    /**
     * Youtube  解析tsv类型文件
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常
     */
    public void parse(String filepath) {
        LineIterator lineIterator = null;
        Map<String, String> headerMap = new HashMap<>();
        List<String> lines = new ArrayList<>();
        String result = "";
        boolean isBreak = false;
        //标志位，用于标识每一个AS01.01
        Integer flag = 0;
        int i = 0;
        try {
            Map<Long, String> fileBaseIdMap = new HashMap<>(); // <fileBaseId, tableName>;
            Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap = new HashMap<>(); // <fileBaseId, <mapping.id, mapping.clickNumber>>
            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            while (lineIterator.hasNext()) {
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                logger.info("========处理第{}行, line: {}", i, line);
                flag = this.dealLines(line, headerMap, flag, lines);

                // 每1000行入庫
                if (tsvCommonObjectList.size() >= 1000) {
                    result = saveDspFileBase(filepath);
                    if (StringUtils.isNotBlank(result)) {
                        logger.info("解析异常~" + result);
                        listFileQueue.setStatus(3);
                        listFileQueue.setDescription(result);
                        tsvCommonObjectList.clear();
                        isBreak = true;
                        break;
                    }
                    saveDspFileDataMapping(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                    tsvCommonObjectList.clear();
                }
            }
            // 最後剩餘數據入庫
            if (tsvCommonObjectList.size() > 0) {
                saveDspFileDataMapping(tsvCommonObjectList, fileBaseIdMap, fileBaseIdPlayCountMap);
                tsvCommonObjectList.clear();
            }

            // 数据排序后同步到正式表，删除临时表
            if (fileBaseIdMap.size() > 0) {
                CountDownLatch countDownLatch = new CountDownLatch(fileBaseIdMap.size());
                ReentrantLock lock = new ReentrantLock(true); // 公平锁，写锁（排他锁），用来顺序（唯一）插入mapping数据

                long startTime = System.currentTimeMillis();
                for (Long fileBaseId : fileBaseIdMap.keySet()) {
                    List<DspDataTemp> dspDataTemps = fileBaseIdPlayCountMap.get(fileBaseId);
                    if (CollectionUtils.isEmpty(dspDataTemps)) {
                        countDownLatch.countDown();
                        continue;
                    }
                    String tableName = fileBaseIdMap.get(fileBaseId);
                    // 异步方法里不共用事务
                    CompletableFuture.runAsync(() -> { // 该方法默认线程池大小是当前电脑核数 - 1
                        // TODO: 2021-08-02 huyong: 如果该任务卡主，可能是该方法异步线程出错不会报错的，偶发性问题，需要使用自定义线程池，同时中断其他线程并向上抛出异常，可参考 syncOfficialTable 方法
                        long listSize = 0;
                        long a = System.currentTimeMillis();
                        List<Long> collect = dspDataTemps.stream().sorted(Comparator.comparing(DspDataTemp::getWorkPrice, Comparator.reverseOrder()).thenComparing(DspDataTemp::getClickNumber, Comparator.reverseOrder())).map(DspDataTemp::getId).collect(Collectors.toList());
                        long b = System.currentTimeMillis() - a;
                        logger.info("临时表【{}】，workPrice, clickNumber倒叙排序耗时：【{}】", tableName, b > 1000 ? (b / 1000) + " 秒" : b + " 毫秒");
                        List<List<Long>> partition = Lists.partition(collect, BATCH_SIZE_1000);
                        for (List<Long> ids : partition) {
                            logger.info("临时表【{}】，查询id个数：【{}】", tableName, ids.size());
                            List<ListDspFileDataMapping> listDspFileDataMappings = listDspFileDataMappingService.selectTemporaryByIds(ids, tableName);
                            try {
                                lock.lock();
                                logger.info("开始插入正式表，fid:【{}】, size:【{}】", fileBaseId, listDspFileDataMappings.size());
                                listDspFileDataMappingService.addList(listDspFileDataMappings);
                                listSize += listDspFileDataMappings.size();
                            } catch (Exception e) {
                                logger.error("tzk001,msg: ", e);
                            } finally {
                                lock.unlock();
                            }
                        }
                        // 跟新base的关于mapping的个数
                        ListDspFileBase base = new ListDspFileBase();
                        base.setId(fileBaseId);
                        base.setListFileTotal(listSize);
                        base.init();
                        logger.info("更新base: {}", JSON.toJSONString(base));
                        listDspFileBaseService.updateSelective(base);
                        logger.info("更新base完成: {}", JSON.toJSONString(base));
                        countDownLatch.countDown();
                        logger.info("临时表【{}】end，countDownLatch个数：【{}】", tableName, countDownLatch.getCount());
                    });
                }
                countDownLatch.await();
                logger.info("countDownLatch end");
                while (lock.isLocked()) {
                    //自旋，等待最后一个公平锁释放
                }
                for (String tableName : fileBaseIdMap.values()) {
                    // 需要等待异步线程执行完后，才能删除表，否则会出现查询的时候表没了，导致程序卡死
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】", tableName);
                    listDspFileDataMappingService.dropTemporaryTable(tableName);
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】成功！", tableName);
                }
                logger.info("lock end");
                long consuming = System.currentTimeMillis() - startTime;
                logger.info("fid:【{}】, 临时表->正式表完成, time consuming：【{}】", fileBaseIdMap.keySet(), consuming > 1000 ? (consuming / 1000) + " 秒" : consuming + " 毫秒");
            }
            if (!isBreak) {
                listFileQueue.setStatus(2);
                listFileQueue.setDescription("解析完成~~~~");
                logger.info("totalSy0202Usage is " + totalSy0202Usage);
                logger.info("totalRu0101Usage is " + totalRu0101Usage);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            logger.error("================== {}", message);
            if (null != message && message.length() > 5000) {
                message = message.substring(0, 4900);
            }
            listFileQueue.setStatus(3);
            if (StringUtils.isNotBlank(result)) {
                listFileQueue.setDescription(String.format("解析出错：%s", result));
            } else {
                listFileQueue.setDescription(String.format("解析出错：%s", message));
            }
            e.printStackTrace();
        } finally {
            LineIterator.closeQuietly(lineIterator);
        }
    }




    /**
     * @param line      当前解析的内容（标题头+数据）
     * @param headerMap 标题头
     * @param flag      标志位，用于标识每一个AS01.01, 通过异或来判断一条数据的开闭
     * @param lines     之前未解析的内容（数据）
     * @return
     */
    protected int dealLines(String line, Map<String, String> headerMap, Integer flag, List<String> lines) {
        if (line.startsWith("#")) {
            //解析标题，全部解析完再去解析内容
            dealWithString(line, headerMap);
        } else {
            lines.add(line);
            if (headerMap.isEmpty() || headerMap.size() < 3) {
                //如果格式不变的话，head实在#head前面，先解析#head, #SY09...标题
                //不处理
            } else if (lines.size() > 0) {
                //逐行解析内容
                for (String it : lines) {
                    flag = this.dealLine(it, headerMap, flag);
                }
                lines.clear();
            }
        }
        return flag;
    }

    /**
     * @param line      当前解析的内容（数据）
     * @param headerMap 标题头
     * @param flag      标志位，用于标识每一个AS01.01, 通过异或来判断一条数据的开闭
     * @return
     */
    protected int dealLine(String line, Map<String, String> headerMap, Integer flag) {
        line = line.replace("\\\\\t", "/").replace("\\\t", "\\").replace("\\", "/");
        String[] lineSplit = StringUtils.splitPreserveAllTokens(line, "\t");
        String header = headerMap.get(lineSplit[0]);
        String[] headerSplit = StringUtils.splitPreserveAllTokens(header, "\t");
        if (lineSplit.length != headerSplit.length) {
            //数据与标题个数不一致，则忽略该行后面的，提示下
            logger.info("=================数据与标题匹配不上======================");
            logger.info("head: " + header);
            logger.info("line: " + line);
        }
        try {
            Map<String, String> map = this.convert2Map(headerSplit, lineSplit);
            return this.parseData(map, flag);
        } catch (Exception e) {
            logger.error("convert error,the error is " + e.getStackTrace());
        }
        return flag;
    }

    private Map<String, String> convert2Map(String[] headerSplit, String[] lineSplit) {
        Map<String, String> map = new HashMap<>();
        headerSplit[0] = headerSplit[0].replace("#", "");
        for (int i = 0; i < headerSplit.length; i++) {
            String value = null;
            // 当标题与数据的长度不一致，忽略缺失数据（多余数据）
            if (i < lineSplit.length) {
                value = lineSplit[i];
            }
            map.put(headerSplit[i], value);
        }
        return map;
    }

    /**
     * 解析数据
     *
     * @param map
     * @param flag
     * @return
     */
    private Integer parseData(Map<String, String> map, Integer flag) {

        // 处理文件头部		header  一个文件只有一个头
        if (map.containsKey("HEAD")) {
            header = JSON.parseObject(JSON.toJSONString(map), Header.class);
            header.setName("HEAD");
            header.setMessageCreatedDateTime(header.getMessageCreatedDateTime().replace("T", " ").replace("Z", ""));
        }

        // AVOD的时候处理 SY0202节点  汇总信息
        if (map.containsKey("SY02.02")) {
            SY0202 sy0202 = JSON.parseObject(JSON.toJSONString(map), SY0202.class);
            sy0202.setName("SY02.02");
            Sy0202List.add(sy0202);
        }

        // 处理SY04.01节点  汇总信息
        if (map.containsKey("SY04.01")) {
            Sy0401 sy0401 = JSON.parseObject(JSON.toJSONString(map), Sy0401.class);
            sy0401.setName("SY04.01");
            sy0401.setNet(map.get("NetRevenue"));
            Sy0401List.add(sy0401);
        }

        // 处理SY09节点		汇总信息   //TODO 只做了解析，其他地方暂时没用到???
        if (map.containsKey("SY09")) {
            Sy09 sy09 = JSON.parseObject(JSON.toJSONString(map), Sy09.class);
            sy09.setName("SY09");
        }

        // 处理SY05.02		汇总信息
        if (map.containsKey("SY05.02")) {
            Sy0502 sy0502 = JSON.parseObject(JSON.toJSONString(map), Sy0502.class);
            sy0502.setName("SY05.02");
            Sy0502List.add(sy0502);
        }

        // 处理SY09
        if (map.containsKey("SY09")) {
            Sy09 sy09 = JSON.parseObject(JSON.toJSONString(map), Sy09.class);
            sy09.setName("SY09");
            Sy09List.add(sy09);
        }

        // 处理块节点  	AS01.01		BlockId   每个AS0101就是一个tsvCommonObject
        if (map.containsKey("AS01.01")) {
            flag ^= 1;
            if (flag == 0) {
                TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
                //if (tsvCommonObject.getMw0101() == null || tsvCommonObject.getSu0302List() == null || tsvCommonObject.getAs0101() == null || tsvCommonObject.getRu0101List() == null) {
                //    logger.info("节点空·········································");
                //}
                tsvCommonObjectList.add(tsvCommonObject);
                tsvCommonObjectThreadLocal.remove();
                nowSu0302ThreadLocal.remove();
                //logger.info("tsvCommonObjectList size is " + tsvCommonObjectList.size());
                flag = 1;
            }
            As0101 as0101 = JSON.parseObject(JSON.toJSONString(map), As0101.class);
            as0101.setName("AS01.01");
            Matcher m = Pattern.compile("PT(?<H>\\d+)H(?<M>\\d+)M(?<S>\\d+)S").matcher(map.get("Duration"));
            if (m.find()) {
                as0101.setDurationM(m.group("M"));
                as0101.setDurationS(m.group("S"));
            }
            nowBlockId = as0101.getBlockId();
            //logger.info("now blockId is :" + nowBlockId);
            TsvCommonObject tsvCommonObject = new TsvCommonObject();
            tsvCommonObject.setAs0101(as0101);
            tsvCommonObjectThreadLocal.set(tsvCommonObject);
        }

        //处理块 节点   RU01.01	BlockId  汇总
        if (map.containsKey("RU01.01")) {
            Ru0101 ru0101 = JSON.parseObject(JSON.toJSONString(map), Ru0101.class);
            ru0101.setName("RU01.01");
            TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
            List<Ru0101> ru0101List = tsvCommonObject.getRu0101List();
            if (null == ru0101List || ru0101List.isEmpty()) {
                ru0101List = new ArrayList<>();
            }
            ru0101List.add(ru0101);
            tsvCommonObject.setRu0101List(ru0101List);
        }

        // 处理块节点 	SU03.02	BlockId
        if (map.containsKey("SU03.02")) {
            Su0302 su0302 = JSON.parseObject(JSON.toJSONString(map), Su0302.class);
            su0302.setName("SU03.02");
            TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
            if (!nowBlockId.equals(su0302.getBlockId())) {
                //验证下数据是否正确，其实可以删除
                throw new RuntimeException("当前SU03.02与AS01.01的blockId不一致，数据错误！ AS01.01: " + JSON.toJSONString(tsvCommonObject.getAs0101()) + "; SU03.02: " + JSON.toJSONString(su0302));
            }
            List<Su0302> su0302List = tsvCommonObject.getSu0302List();
            if (null == su0302List || su0302List.isEmpty()) {
                su0302List = new ArrayList<>();
            }
            su0302List.add(su0302);
            tsvCommonObject.setSu0302List(su0302List);
            nowSu0302ThreadLocal.set(su0302);

        }

        // 处理块节点	LI01.02	BlockId
        if (map.containsKey("LI01.02")) {
            LI0102 li0102 = JSON.parseObject(JSON.toJSONString(map), LI0102.class);
            li0102.setName("LI01.02");
            li0102.setRightShare(map.get("RightShare%"));
            Su0302 su0302 = nowSu0302ThreadLocal.get();
            if (su0302 != null) {
                String validityPeriodStart = su0302.getValidityPeriodStart();
                String validityPeriodEnd = su0302.getValidityPeriodEnd();
                String uniqueKey = validityPeriodStart + validityPeriodEnd;
                li0102.setUniqueKey(uniqueKey);
                List<LI0102> li0102List = su0302.getLi0102List();
                if (null == li0102List) {
                    li0102List = new ArrayList<>();
                }
                li0102List.add(li0102);
                su0302.setLi0102List(li0102List);
            }
        }

        // 处理块节点	MW01.01	BlockId	当MW01.01 出现在RU01.01之前 表示为主标题
        if (map.containsKey("MW01.01")) {
            Mw0101 mw0101 = JSON.parseObject(JSON.toJSONString(map), Mw0101.class);
            mw0101.setName("Mw01.01");
            Su0302 su0302 = nowSu0302ThreadLocal.get();
            TsvCommonObject tsvCommonObject = this.tsvCommonObjectThreadLocal.get();
            if (su0302 != null) {
                String validityPeriodStart = su0302.getValidityPeriodStart();
                String validityPeriodEnd = su0302.getValidityPeriodEnd();
                String uniqueKey = validityPeriodStart + validityPeriodEnd;
                mw0101.setUniqueKey(uniqueKey);
                List<Mw0101> mw0101List = su0302.getMw0101List();
                if (null == mw0101List) {
                    mw0101List = new ArrayList<>();
                }
                mw0101List.add(mw0101);
                su0302.setMw0101List(mw0101List);
            } else {
                tsvCommonObject.setMw0101(mw0101);
            }
        }

        //  处理文件尾部信息	FOOT	一个文件只有一个尾
        if (map.containsKey("FOOT")) {
            footer = JSON.parseObject(JSON.toJSONString(map), Footer.class);
            footer.setName("FOOT");
            // 最后一个AS01.01需要处理
            TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
            if (tsvCommonObject != null) {
                tsvCommonObjectList.add(tsvCommonObject);
            }
            tsvCommonObjectThreadLocal.remove();
            nowSu0302ThreadLocal.remove();
            logger.info("tsvCommonObjectList size is " + tsvCommonObjectList.size());
        }

        return flag;

    }

    private String saveDspFileBase(String filepath) throws ParseException {
        String result = "";
        if (isParse) {
            return result;
        }
        String extJson = listFileQueue.getExtJson();
        JSONObject object = JSONObject.fromObject(extJson);
        Long claimSetInfoId = object.getLong("claimSetInfoId");
        if (null == claimSetInfoId) {
            result = "队列表中缺少claimSetid!联系工作人员~";
            return result;
        }
        if (null != Sy0202List && Sy0202List.size() > 0) {

            // 此处可能会出现异常，查询出来多个结果！
            ClaimMinimaInfo avod = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId("AVOD", claimSetInfoId);
            if (null == avod) {
                result = "productShorName为AVOD,claimSetInfoId为" + claimSetInfoId + ",没有找到claimMinimaInfo!请先配置~~~";
                return result;
            }

            // 此处可修改成Map 按照contentCategory分组

            Map<String, List<SY0202>> resultMap = Sy0202List.stream().filter(it -> "all".equals(it.getRightsController())).collect(Collectors.groupingBy(it -> it.getContentCategory()));
            if (null != resultMap && resultMap.size() > 0) {
                Set<String> mapKeys = resultMap.keySet();
                for (String mapKey : mapKeys) {
                    List<SY0202> resultList = resultMap.get(mapKey);
                    try {
                        createListDspFileBaseForAVOD(avod, filepath, mapKey, resultList);
                    } catch (ParseException e) {
                        e.printStackTrace();
                        return "生成AVODFileBaseError ErrorMessage : " + e.getMessage();
                    }
                }
            }
            isParse = true;
        } else if (null != Sy0502List && !Sy0502List.isEmpty()) {
            //SVOD
            result = createListDspFileBaseForSVOD(claimSetInfoId, filepath, Sy0401List, Sy0502List, Sy09List);
            isParse = true;
        }
        return result;
    }

    private String createListDspFileBaseForSVOD(Long claimSetInfoId, String filepath, List<Sy0401> sy0401List, List<Sy0502> sy0502List, List<Sy09> sy09List) {
        // 此处可能会出现异常，查询出来多个结果！
        ClaimMinimaInfo svod = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId("SVOD", claimSetInfoId);
        if (null == svod) {
            String result = "productShorName为SVOD,claimSetInfoId为" + claimSetInfoId + ",没有找到claimMinimaInfo!请先配置~~~";
            return result;
        }

        List<Sy0401> sy0401s = sy0401List.stream().filter(x -> "all".equalsIgnoreCase(x.getSubscriberType())).collect(Collectors.toList());
        for (Sy0401 sy0401 : sy0401s) {
            ListDspFileBase base = createListDspFileBaseForSVOD(filepath, sy0401, svod.getId());

            List<Sy0401> sy0401s1 = sy0401List.stream().filter(x -> x.getSummaryRecordId().startsWith(sy0401.getSummaryRecordId()) && !"all".equalsIgnoreCase(x.getSubscriberType())).collect(Collectors.toList());
            List<Sy09> sy09s = sy09List.stream().filter(x -> x.getSummaryRecordId().startsWith(sy0401.getSummaryRecordId())).collect(Collectors.toList());
            List<Sy0502> sy0502s = sy0502List.stream().filter(x -> x.getSummaryRecordId().startsWith(sy0401.getSummaryRecordId())).collect(Collectors.toList());
            createListDspFileBaseExtends(base.getId(), sy0401s1, sy09s, sy0502s);
        }
        return "";
    }

    private void createListDspFileBaseExtends(Long baseId, List<Sy0401> sy0401s, List<Sy09> sy09s, List<Sy0502> sy0502s) {
        List<ListDspFileBaseExtends> baseExtendsList = new ArrayList<>();
        String userName = LoginUtil.getUserName();
        for (Sy0401 sy0401 : sy0401s) {
            ListDspFileBaseExtends baseExtends = new ListDspFileBaseExtends();
            baseExtends.setFileBaseId(baseId);
            baseExtends.setNodeName(sy0401.getName());
            baseExtends.setSummaryRecordId(sy0401.getSummaryRecordId());
            baseExtends.setCommercialModel(sy0401.getCommercialModel());
            baseExtends.setUserType(sy0401.getUseType());
            baseExtends.setTerritory(sy0401.getTerritory());
            baseExtends.setServiceDescription(sy0401.getServiceDescription());
            baseExtends.setSubscribers(StringUtils.isNotBlank(sy0401.getSubscribers()) ? new BigDecimal(sy0401.getSubscribers()) : BigDecimal.ZERO);
            baseExtends.setSubscriberType(sy0401.getSubscriberType());
//            baseExtends.setRightsController(); 权利所属，Residual|Conflict|实际的公司名
//            baseExtends.setRightType(sy0401.getty); 权利类别MechanicalRight|PerformingRight
//            baseExtends.setTotalUsages(sy0401.get);
//            baseExtends.setAllocatedUsages(sy0401.getUs);
//            baseExtends.setAllocatedNetRevenue(sy0401.getNet());
            baseExtends.setCurrency(sy0401.getCurrency());

            String usageStartDate = sy0401.getSubPeriodStartDate();
            String usageEndDate = sy0401.getSubPeriodEndDate();
            try {
                baseExtends.setSubperiodStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
                baseExtends.setSubperiodEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
            } catch (ParseException e) {
                //直接抛出异常，并不在执行下去
                throw new RuntimeException("格式不正确");
            }
//            baseExtends.setContentCategory();
            baseExtends.setNetRevenue(StringUtils.isNotBlank(sy0401.getNet()) ? new BigDecimal(sy0401.getNet()) : BigDecimal.ZERO); // TODO ???
            baseExtends.setCreateBy(userName);
            baseExtends.setExtJson(JSON.toJSONString(sy0401));
            baseExtends.init();
            baseExtendsList.add(baseExtends);
        }
        for (Sy09 sy09 : sy09s) {
            ListDspFileBaseExtends baseExtends = new ListDspFileBaseExtends();
            baseExtends.setFileBaseId(baseId);
            baseExtends.setNodeName(sy09.getName());
            baseExtends.setSummaryRecordId(sy09.getSummaryRecordId());
            baseExtends.setCommercialModel(sy09.getCommercialModel());
            baseExtends.setUserType(sy09.getUseType());
            baseExtends.setTerritory(sy09.getTerritory());
            baseExtends.setServiceDescription(sy09.getServiceDescription());
//            baseExtends.setSubscribers(StringUtils.isNotBlank(sy09.getSubscribers()) ? new BigDecimal(sy09.getSubscribers()) : BigDecimal.ZERO);
            baseExtends.setSubscriberType(sy09.getSubscriberType());
            baseExtends.setRightsController(sy09.getRightsController()); // 权利所属，Residual|Conflict|实际的公司名
            baseExtends.setRightType(sy09.getRightsType()); // 权利类别MechanicalRight|PerformingRight
            baseExtends.setTotalUsages(StringUtils.isNotBlank(sy09.getTotalUsages()) ? new BigDecimal(sy09.getTotalUsages()) : BigDecimal.ZERO);
            baseExtends.setAllocatedUsages(StringUtils.isNotBlank(sy09.getAllocatedUsages()) ? new BigDecimal(sy09.getAllocatedUsages()) : BigDecimal.ZERO);
//            baseExtends.setAllocatedNetRevenue(StringUtils.isNotBlank(sy09.getNetRevenue()) ? new BigDecimal(sy09.getNetRevenue()) : BigDecimal.ZERO);
            baseExtends.setCurrency(sy09.getCurrency());
            String usageStartDate = sy09.getSubPeriodStartDate();
            String usageEndDate = sy09.getSubPeriodEndDate();
            try {
                baseExtends.setSubperiodStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
                baseExtends.setSubperiodEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
            } catch (ParseException e) {
                //直接抛出异常，并不在执行下去
                throw new RuntimeException("格式不正确");
            }
//            baseExtends.setContentCategory();
            baseExtends.setNetRevenue(StringUtils.isNotBlank(sy09.getNetRevenue()) ? new BigDecimal(sy09.getNetRevenue()) : BigDecimal.ZERO);
            baseExtends.setCreateBy(userName);
            baseExtends.setExtJson(JSON.toJSONString(sy09));
            baseExtends.init();
            baseExtendsList.add(baseExtends);
        }
        for (Sy0502 sy0502 : sy0502s) {
            ListDspFileBaseExtends baseExtends = new ListDspFileBaseExtends();
            baseExtends.setFileBaseId(baseId);
            baseExtends.setNodeName(sy0502.getName());
            baseExtends.setSummaryRecordId(sy0502.getSummaryRecordId());
            baseExtends.setCommercialModel(sy0502.getCommercialModel());
            baseExtends.setUserType(sy0502.getUseType());
            baseExtends.setTerritory(sy0502.getTerritory());
            baseExtends.setServiceDescription(sy0502.getServiceDescription());
//            baseExtends.setSubscribers(StringUtils.isNotBlank(sy0502.getSubscribers()) ? new BigDecimal(sy0502.getSubscribers()) : BigDecimal.ZERO);
            baseExtends.setSubscriberType(sy0502.getSubscriberType());
            baseExtends.setRightsController(sy0502.getRightsController()); // 权利所属，Residual|Conflict|实际的公司名
            baseExtends.setRightType(sy0502.getRightsType()); // 权利类别MechanicalRight|PerformingRight
            baseExtends.setTotalUsages(StringUtils.isNotBlank(sy0502.getTotalUsages()) ? new BigDecimal(sy0502.getTotalUsages()) : BigDecimal.ZERO);
            baseExtends.setAllocatedUsages(StringUtils.isNotBlank(sy0502.getAllocatedUsages()) ? new BigDecimal(sy0502.getAllocatedUsages()) : BigDecimal.ZERO);
            baseExtends.setAllocatedNetRevenue(StringUtils.isNotBlank(sy0502.getAllocatedNetRevenue()) ? new BigDecimal(sy0502.getAllocatedNetRevenue()) : BigDecimal.ZERO);
            baseExtends.setCurrency(sy0502.getCurrency());
            String usageStartDate = sy0502.getSubPeriodStartDate();
            String usageEndDate = sy0502.getSubPeriodEndDate();
            try {
                baseExtends.setSubperiodStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
                baseExtends.setSubperiodEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
            } catch (ParseException e) {
                //直接抛出异常，并不在执行下去
                throw new RuntimeException("格式不正确");
            }
            baseExtends.setContentCategory(sy0502.getContentCategory());
//            baseExtends.setNetRevenue(StringUtils.isNotBlank(sy0502.getAllocatedNetRevenue()) ? new BigDecimal(sy0502.getAllocatedNetRevenue()) : BigDecimal.ZERO);
            baseExtends.setCreateBy(userName);
            baseExtends.setExtJson(JSON.toJSONString(sy0502));
            baseExtends.init();
            baseExtendsList.add(baseExtends);
        }
        listDspFileBaseExtendsService.addList(baseExtendsList);
    }

    /**
     * @param filepath
     * @param sy0401
     */
    private ListDspFileBase createListDspFileBaseForSVOD(String filepath, Sy0401 sy0401, Long claimMinimaInfoId) {
        ListDspFileBase listDspFileBase = new ListDspFileBase();
        Map<String, Object> extJson = new HashMap<>();
        listDspFileBase.setCurrency(sy0401.getCurrency());
        listDspFileBase.setCommercialModel(sy0401.getServiceDescription());
        listDspFileBase.setUseType(sy0401.getUseType());
        listDspFileBase.setFileExt(null != listFileQueue ? listFileQueue.getFileExt() : "");
        dspFileBaseIdMap.put("CommercialModel", sy0401.getServiceDescription());
        dspFileBaseIdMap.put("Currency", sy0401.getCurrency());
        // 对应的产品需要固定为某一个，默认为数据的
        String usageStartDate = sy0401.getSubPeriodStartDate();
        String usageEndDate = sy0401.getSubPeriodEndDate();
        try {
            listDspFileBase.setListFileStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
            listDspFileBase.setListFileEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
        } catch (ParseException e) {
            //直接抛出异常，并不在执行下去
            throw new RuntimeException("格式不正确");
        }
        listDspFileBase.setDspCompany(header.getSenderName());
        listDspFileBase.setDist(0);
        listDspFileBase.setDistStatus(0);
        listDspFileBase.setFilePath(filepath);
        String fileName = filepath.substring(filepath.lastIndexOf(File.separator) + 1);
        listDspFileBase.setFileName(fileName);
        listDspFileBase.setFileStatus(1);
        String summaryRecordId = sy0401.getSummaryRecordId();
        String productName = "";
        String subscriberType = sy0401.getSubscriberType();
        if ("all".equalsIgnoreCase(subscriberType)) {
            String subPeriodStartDate = sy0401.getSubPeriodStartDate();
            productName = subscriberType + "-" + subPeriodStartDate;
            listDspFileBase.setIsShow(IsShowEnum.show.code());
        } else {
            productName = subscriberType;
            listDspFileBase.setIsShow(IsShowEnum.hide.code());
        }

        listDspFileBase.setProductName(productName);
        listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
        listDspFileBase.setListSubscribeCount(StringUtils.isNotBlank(sy0401.getSubscribers()) ? new BigDecimal(sy0401.getSubscribers()) : BigDecimal.ZERO);
        listDspFileBase.setListTotalRoy(StringUtils.isNotBlank(sy0401.getNet()) ? new BigDecimal(sy0401.getNet()) : BigDecimal.ZERO); //NetRevenue
        extJson.put("MusicUsagePercentage", sy0401.getMusicUsagePercentage());
        listDspFileBase.setFileQuenceId(listFileQueue.getId());
        String usages = sy0401.getUsagesInSubPeriod();
        if (StringUtils.isNotBlank(usages)) {
            listDspFileBase.setListTotalClickCount(new BigDecimal(usages));
        }
        String numberOfFiles = header.getNumberofFiles();
        if (StringUtils.isNotBlank(numberOfFiles)) {
            listDspFileBase.setListFileTotal(Long.valueOf(numberOfFiles));
        }
        listDspFileBase.setClaimMinimaInfoId(claimMinimaInfoId);
        listDspFileBase.setExtJson(JSON.toJSONString(extJson));

        List<ListDspFileBase> bases = listDspFileBaseService.list(listDspFileBase);
        if (Objects.nonNull(bases) && !bases.isEmpty()) {
            //直接抛出异常，并不在执行下去
            throw new RuntimeException("重复执行了，该base数据库已存在！ " + JSON.toJSONString(bases));
        }
        listDspFileBase.init();
        listDspFileBase.setDeleted(DeletedEnum.normal.code());
        listDspFileBaseService.add(listDspFileBase);

        if ("all".equalsIgnoreCase(subscriberType)) {
            connectListDspFileBaseMap.put(sy0401.getSummaryRecordId(), listDspFileBase.getId());
        }
        return listDspFileBase;
    }


    private void createListDspFileBaseForAVOD(ClaimMinimaInfo avod, String filepath, String type, List<SY0202> sy0202List) throws ParseException {
        if (null != sy0202List && sy0202List.size() > 0) {
            SY0202 sy0202 = sy0202List.get(0);
            Map<String, Object> extJson = new HashMap<>();
            ListDspFileBase listDspFileBase = new ListDspFileBase();
            listDspFileBase.init();
            listDspFileBase.setCurrency(sy0202.getCurrency());
            listDspFileBase.setCommercialModel(sy0202.getServiceDescription());
            extJson.put("UseType", sy0202.getUseType());
            listDspFileBase.setExtJson(JSON.toJSONString(extJson));
            listDspFileBase.setUseType(sy0202.getUseType());
            // 对应的产品需要固定为某一个，默认为数据的
            String usageStartDate = header.getUsageStartDate();
            listDspFileBase.setListFileStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
            String usageEndDate = header.getUsageEndDate();
            listDspFileBase.setListFileEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
//			String fileNumber = header.getFileNumber();
            listDspFileBase.setDspCompany(header.getSenderName());
            listDspFileBase.setDist(0);
            listDspFileBase.setDistStatus(0);
            listDspFileBase.setFilePath(filepath);
            String fileName = listFileQueue.getFileName();
//            String fileName = "DSR_MUST-CS_YouTube_Usage-AdSupport-Music-All_2019-Q4_TW_1of1_20200122T132333.tsv";
            listDspFileBase.setFileName(fileName + "_" + type);
            listDspFileBase.setFileStatus(1);
            listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
            listDspFileBase.setFileQuenceId(listFileQueue.getId());
            listDspFileBase.setDeleted(DeletedEnum.normal.code());


            listDspFileBase.setListTotalRoy(ConvertUtils.tryStrToDecimal(sy0202.getNetRevenue()));
            String summaryRecordId = sy0202.getSummaryRecordId();

//            listDspFileBase.setFileQuenceId(400l);
            String usages = sy0202.getUsages();
            if (StringUtils.isNotBlank(usages)) {
                totalSy0202Usage = totalSy0202Usage.add(new BigDecimal(usages));
                listDspFileBase.setListTotalClickCount(new BigDecimal(usages));
            }

            String numberOfFiles = header.getNumberofFiles();
            if (StringUtils.isNotBlank(numberOfFiles)) {
                listDspFileBase.setListFileTotal(Long.valueOf(numberOfFiles));
            }
            listDspFileBase.setClaimMinimaInfoId(avod.getId());
            listDspFileBase.setIsShow(IsShowEnum.show.code());

//            String netRevenue = sy0202.getNetRevenue();
//            BigDecimal netRevenueValue = BigDecimal.ZERO;
//            if(StringUtils.isNotBlank(netRevenue)){
//                netRevenueValue = new BigDecimal(netRevenue);
//            }
//            listDspFileBase.setListTotalRoy(netRevenueValue);
            String productFullName = avod.getProductFullName() ==null?"AVOD":avod.getProductFullName();

            listDspFileBase.setProductName(productFullName+ "_" + sy0202.getContentCategory());
            listDspFileBaseService.add(listDspFileBase);
            connectListDspFileBaseMap.put(summaryRecordId, listDspFileBase.getId());
            isAvodMap.put(summaryRecordId, true);
            dspFileBaseIdMap.put(listDspFileBase.getId() + "", sy0202.getUseType());
            dspFileBaseIdMap.put("CommercialModel", sy0202.getServiceDescription());
            dspFileBaseIdMap.put("Currency", sy0202.getCurrency());
        }
    }

    /**
     * 校验排序字段项，防止排序报空指针异常
     *
     * @param listDspFileDataMapping
     */
    protected void checkSortedField(ListDspFileDataMapping listDspFileDataMapping) {
        if (Objects.isNull(listDspFileDataMapping.getClickNumber())) {
            listDspFileDataMapping.setClickNumber(BigDecimal.ZERO);
        }
        if (Objects.isNull(listDspFileDataMapping.getWorkPrice())) {
            listDspFileDataMapping.setWorkPrice(BigDecimal.ZERO);
        }
    }

    private void saveDspFileDataMapping(List<TsvCommonObject> tsvCommonObjectList, Map<Long, String> fileBaseIdMap, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) {
        List<ListDspFileDataMapping> ldfdmList = new ArrayList<>();
        for (TsvCommonObject tsvCommonObject : tsvCommonObjectList) {
            // 一个block下的title和subTitle都需要去重后拼接在一起
            tsvCommonObjectChangeToDspFileDataMapping(ldfdmList, tsvCommonObject);
        }
        Map<Long, List<ListDspFileDataMapping>> listMap = ldfdmList.stream().collect(Collectors.groupingBy(ListDspFileDataMapping::getFileBaseId));
        for (Long fileBaseId : listMap.keySet()) {
            List<ListDspFileDataMapping> mappings = listMap.get(fileBaseId);
            if (fileBaseIdMap.containsKey(fileBaseId)) {
                int i = listDspFileDataMappingService.addListTemporary(fileBaseIdMap.get(fileBaseId), mappings);
            } else {
                String temporaryTable = listDspFileDataMappingService.createTemporaryTable(fileBaseId);
                fileBaseIdMap.put(fileBaseId, temporaryTable);
                int i = listDspFileDataMappingService.addListTemporary(temporaryTable, mappings);
            }
            List<DspDataTemp> transfer = transfer(mappings);
            List<DspDataTemp> orDefault = fileBaseIdPlayCountMap.getOrDefault(fileBaseId, new ArrayList<>());
            orDefault.addAll(transfer);
            fileBaseIdPlayCountMap.put(fileBaseId, orDefault);
        }
    }

    private void tsvCommonObjectChangeToDspFileDataMapping(List<ListDspFileDataMapping> ldfdmList, TsvCommonObject tsvCommonObject) {
        List<Su0302> su0302List = tsvCommonObject.getSu0302List();
        if (null != su0302List && su0302List.size() > 0) {
            for (Su0302 su0302 : su0302List) {
                ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
                listDspFileDataMapping.init();
                JSONObject object = new JSONObject();
                createListDspFileDataMappingByAs0101(object, listDspFileDataMapping, tsvCommonObject.getAs0101());
                createListDspFileDataMappingByRu0101(object, tsvCommonObject.getRu0101List());
                createListDspFileDataMappingByMw0101(null, object, listDspFileDataMapping, tsvCommonObject.getMw0101());
                createListDspFileDataMappingBySu0302(object, listDspFileDataMapping, su0302);

                // 生成dataUniqueKey  和 dataUniqueKeyStr
                setUniqueKey(listDspFileDataMapping);
                List<LI0102> li0102List = su0302.getLi0102List();
                if (null != li0102List && li0102List.size() > 0) {
                    List<Map<String, String>> extJsonList = new ArrayList<>();
                    for (LI0102 li0102 :
                            li0102List) {
                        dealLi0102(li0102, extJsonList, listDspFileDataMapping);
                    }
                    object.put("li0102List", extJsonList);
                }

                List<Mw0101> mw0101List = su0302.getMw0101List();
                if (null != mw0101List && mw0101List.size() > 0) {
                    List<Map<String, String>> extJsonList = new ArrayList<>();
                    for (Mw0101 mw0101 : mw0101List
                    ) {
                        createListDspFileDataMappingByMw0101(extJsonList, object, listDspFileDataMapping, mw0101);
                    }
                    object.put("mw0101List", extJsonList);
                }
                if(listFileQueue.getMatchMark().equals("N")){
                    listDspFileDataMapping.setStatus(5);
                }else {
                    listDspFileDataMapping.setStatus(0);
                }
                listDspFileDataMapping.setExtJson(object.toString());
                checkSortedField(listDspFileDataMapping);
                ldfdmList.add(listDspFileDataMapping);
            }
        }
    }

    private void dealLi0102(LI0102 li0102, List<Map<String, String>> extJsonList, ListDspFileDataMapping listDspFileDataMapping) {

        Map<String, String> extMap = new HashMap<>();
        if (li0102 == null) {
            logger.info(listDspFileDataMapping.getGroupCode() + "块未找到li0102节点");
            return;
        }

        String allocatedAmount = li0102.getAllocatedAmount();
        if (StringUtils.isNotBlank(allocatedAmount)) {
            extMap.put("AllocatedAmount", allocatedAmount + "");
        }
        String allocatedNetRevenue = li0102.getAllocatedNetRevenue();
        if (StringUtils.isNotBlank(allocatedNetRevenue)) {
            extMap.put("AllocatedNetRevenue", allocatedNetRevenue + "");
        }
        String allocatedUsages = li0102.getAllocatedUsages();
        if (StringUtils.isNotBlank(allocatedUsages)) {
            extMap.put("AllocatedUsages", allocatedUsages + "");
        }

        String li0102Name = li0102.getName();
        if (StringUtils.isNotBlank(li0102Name)) {
            extMap.put("name", "LI01.02");
        }

        String rightsController = li0102.getRightsController();
        if (StringUtils.isNotBlank(rightsController)) {
            extMap.put("RightsController", rightsController);
        }

        String rightsControllerPartyId = li0102.getRightsControllerPartyId();
        if (StringUtils.isNotBlank(rightsControllerPartyId)) {
            extMap.put("RightsControllerPartyId", rightsControllerPartyId);
        }

        String rightsControllerWorkID = li0102.getRightsControllerWorkID();
        if (StringUtils.isNotBlank(rightsControllerWorkID)) {
            extMap.put("RightsControllerWorkID", rightsControllerWorkID);
        }
        String rightShare = li0102.getRightShare();
        String rightType = li0102.getRightType();
        if (StringUtils.isNotBlank(rightType)) {
            extMap.put("RightType", rightType);
            extMap.put("RightShare", rightShare);
            if ("PerformingRight".equals(rightType)) {
                if (StringUtils.isNotBlank(rightShare)) {
                    BigDecimal perRightShare = new BigDecimal(rightShare);
                    BigDecimal perShare = listDspFileDataMapping.getPerShare();
                    if (null != perShare) {
                        perRightShare = perRightShare.add(perShare);
                    }
                    listDspFileDataMapping.setPerShare(perRightShare);
                }
            }
            if ("MechanicalRight".equals(rightType)) {
                if (StringUtils.isNotBlank(rightShare)) {
                    BigDecimal mecRightShare = new BigDecimal(rightShare);
                    BigDecimal mecShare = listDspFileDataMapping.getMecShare();
                    if (null != mecShare) {
                        mecRightShare = mecRightShare.add(mecShare);
                    }
                    listDspFileDataMapping.setMecShare(mecRightShare);
                }
            }
        }
        String summaryRecordId = li0102.getSummaryRecordId();
        if (StringUtils.isNotBlank(summaryRecordId)) {
            extMap.put("SummaryRecordId", summaryRecordId);
        }
        extJsonList.add(extMap);
    }

    private void createListDspFileDataMappingBySu0302(JSONObject object, ListDspFileDataMapping listDspFileDataMapping, Su0302 su0302) {

        Map<String, String> extMap = new HashMap<>();
        if (su0302 == null) {
            logger.debug(listDspFileDataMapping.getGroupCode() + "块未找到su0302节点");
            return;
        }
        String blockId = su0302.getBlockId();
        if (StringUtils.isNotBlank(blockId)) {
            String groupCode = listDspFileDataMapping.getGroupCode();
            if (!StringUtils.isNotBlank(groupCode)) {
                listDspFileDataMapping.setGroupCode(blockId);
            }
        }

        String summaryRecordId = su0302.getSummaryRecordId();
        if (StringUtils.isNotBlank(summaryRecordId)) {
            extMap.put("SummaryRecordId", summaryRecordId);
        }

        Boolean aBoolean = isAvodMap.get(summaryRecordId);
        Long listDspFileBaseId = null;
        if (null == aBoolean || !aBoolean) {  // SVOD
            summaryRecordId = summaryRecordId.substring(0, summaryRecordId.length() - 1);
            listDspFileBaseId = connectListDspFileBaseMap.get(summaryRecordId);
        } else {  // AVOD
            listDspFileBaseId = connectListDspFileBaseMap.get(summaryRecordId);
        }

        listDspFileDataMapping.setFileBaseId(listDspFileBaseId);
        String useType = dspFileBaseIdMap.get(listDspFileBaseId + "");
        String commercialModel = dspFileBaseIdMap.get("CommercialModel");
        listDspFileDataMapping.setCommercialModel(commercialModel);
        listDspFileDataMapping.setUsage(useType); // usage用于存储useType
        String contentCategory = su0302.getContentCategory();
        listDspFileDataMapping.setLabel(contentCategory);

        String dspReleaseId = su0302.getDspReleaseId();
        if (StringUtils.isNotBlank(dspReleaseId)) {
            listDspFileDataMapping.setReleaseId(dspReleaseId);
            listDspFileDataMapping.setBusinessId(dspReleaseId);
        }

        String netRevenue = su0302.getNetRevenue();
        if (StringUtils.isNotBlank(netRevenue)) {
            listDspFileDataMapping.setWorkPrice(new BigDecimal(netRevenue));
        }

        String usages = su0302.getUsages();


        if (StringUtils.isNotBlank(usages)) {
            BigDecimal bigDecimal = fileBaseIdTotalClickNumMap.get(listDspFileBaseId);
            if (bigDecimal == null) {
                bigDecimal = BigDecimal.ZERO;
            }

            //这块如果在多线程下调用会有问题 ，如果优化成多线程处理，这块需要重构
            listDspFileDataMapping.setClickNumber(new BigDecimal(usages));
            totalClickNumber = totalClickNumber.add(listDspFileDataMapping.getClickNumber());
            bigDecimal = bigDecimal.add(listDspFileDataMapping.getClickNumber());
            fileBaseIdTotalClickNumMap.put(listDspFileBaseId, bigDecimal);
        }

        String validityPeriodEnd = su0302.getValidityPeriodEnd();
        String validityPeriodStart = su0302.getValidityPeriodStart();
        if (StringUtils.isNotBlank(validityPeriodEnd)) {
            Date endDate = null;
            try {
                endDate = new SimpleDateFormat("yyyy-MM-dd").parse(validityPeriodEnd);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            listDspFileDataMapping.setListFileEndTime(endDate);
        }
        if (StringUtils.isNotBlank(validityPeriodStart)) {
            Date startDate = null;
            try {
                startDate = new SimpleDateFormat("yyyy-MM-dd").parse(validityPeriodStart);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            listDspFileDataMapping.setListFileStartTime(startDate);
        }

        String isRoyaltyBearing = su0302.getIsRoyaltyBearing();
        if (StringUtils.isNotBlank(isRoyaltyBearing)) {
            extMap.put("IsRoyaltyBearing", isRoyaltyBearing);
        }

        String currency = dspFileBaseIdMap.get("Currency");
        extMap.put("Currency", currency);

        String su0302Name = su0302.getName();
        if (StringUtils.isNotBlank(su0302Name)) {
            extMap.put("name", su0302Name);
        }

        String salesTransactionId = su0302.getSalesTransactionId();
        if (StringUtils.isNotBlank(salesTransactionId)) {
            extMap.put("SalesTransactionId", salesTransactionId);
        }

        object.put("su02", extMap);
    }

    private void createListDspFileDataMappingByMw0101(List<Map<String, String>> extJsonList, JSONObject object, ListDspFileDataMapping listDspFileDataMapping, Mw0101 mw0101) {

        Map<String, String> extMap = new HashMap<>();
        if (mw0101 == null) {
            logger.debug(listDspFileDataMapping.getGroupCode() + "块未找到mw0101节点");
            return;
        }

        String composerAuthor = mw0101.getComposerAuthor();
        if (StringUtils.isNotBlank(composerAuthor) && !"-".equals(composerAuthor)) {
            String composer = listDspFileDataMapping.getComposer();
            if (StringUtils.isNotBlank(composer) && !composer.contains(composerAuthor)) {
                composerAuthor = composer + ";" + composerAuthor;
            }
            listDspFileDataMapping.setComposer(composerAuthor);
            String author = listDspFileDataMapping.getAuthor();
            if (StringUtils.isNotBlank(author) && !author.contains(composerAuthor)) {
                composerAuthor = author + ";" + composerAuthor;
            }
            listDspFileDataMapping.setAuthor(composerAuthor);
        }

        String iswc = mw0101.getISWC();
        if (StringUtils.isNotBlank(iswc) && !"-".equals(iswc)) {
            String iswcExisted = listDspFileDataMapping.getIswc();
            if (StringUtils.isNotBlank(iswcExisted) && !iswc.contains(iswcExisted)) {
                iswc = iswcExisted + ";" + iswc;
            }
            listDspFileDataMapping.setIswc(iswc);
        }

        String musicPublisher = mw0101.getMusicPublisher();
        if (StringUtils.isNotBlank(musicPublisher)) {
            listDspFileDataMapping.setPublisher(musicPublisher);
        }

        String mw0101SubTitle = mw0101.getSubTitle();
        if (StringUtils.isNotBlank(mw0101SubTitle) && !"-".equals(mw0101SubTitle)) {
            String albumTitle = listDspFileDataMapping.getAlbumTitle();
            if (StringUtils.isNotBlank(albumTitle) && !albumTitle.contains(mw0101SubTitle)) {
                mw0101SubTitle = albumTitle + ";" + mw0101SubTitle;
            }
            listDspFileDataMapping.setAlbumTitle(mw0101SubTitle);
        }
        String mw0101Title = mw0101.getTitle();
        if (StringUtils.isNotBlank(mw0101Title) && !"-".equals(mw0101Title)) {
            String title = listDspFileDataMapping.getMwTitle();
            if (StringUtils.isNotBlank(title) && !title.contains(mw0101Title)) {
                mw0101Title = title + ";" + mw0101Title;
            }
            listDspFileDataMapping.setMwTitle(mw0101Title);
        }

        if (object.get("mw0101") == null) {
            String arranger = mw0101.getArranger();
            if (StringUtils.isNotBlank(arranger)) {
                extMap.put("Arranger", arranger);
            }

            String arrangerPartyId = mw0101.getArrangerPartyId();
            if (StringUtils.isNotBlank(arrangerPartyId)) {
                extMap.put("ArrangerPartyId", arrangerPartyId);
            }

            String composerAuthorPartyId = mw0101.getComposerAuthorPartyId();
            if (StringUtils.isNotBlank(composerAuthorPartyId)) {
                extMap.put("ComposerAuthorPartyId", composerAuthorPartyId);
            }

            String dataProvider = mw0101.getDataProvider();
            if (StringUtils.isNotBlank(dataProvider)) {
                extMap.put("DataProvider", dataProvider);
            }

            String musicPublisherPartyId = mw0101.getMusicPublisherPartyId();
            if (StringUtils.isNotBlank(musicPublisherPartyId)) {
                extMap.put("MusicPublisherPartyId", musicPublisherPartyId);
            }

            String mw0101Name = mw0101.getName();
            if (StringUtils.isNotBlank(mw0101Name)) {
                extMap.put("name", mw0101Name);
            }

            String proprietaryWorkId = mw0101.getProprietaryWorkId();
            if (StringUtils.isNotBlank(proprietaryWorkId)) {
                extMap.put("ProprietaryWorkId", proprietaryWorkId);
            }

            String workContributor = mw0101.getWorkContributor();
            if (StringUtils.isNotBlank(workContributor)) {
                extMap.put("WorkContributor", workContributor);
            }
            String workContributorPartyId = mw0101.getWorkContributorPartyId();
            if (StringUtils.isNotBlank(workContributorPartyId)) {
                extMap.put("WorkContributorPartyId", workContributorPartyId);
            }

            String dspWorkId = mw0101.getDspWorkId();
            if (StringUtils.isNotBlank(dspWorkId) && !"-".equals(dspWorkId)) {
                extMap.put("WorkContributorPartyId", dspWorkId);
            }
            if (null == extJsonList) {
                object.put("mw0101", extMap);
            } else {
                extJsonList.add(extMap);
            }
        }
    }

    private void createListDspFileDataMappingByRu0101(JSONObject object, List<Ru0101> ru0101List) {
        if (null != ru0101List && ru0101List.size() > 0) {
            List<Map<String, String>> extJsonList = new ArrayList<>();
            if (null == object.get("ru0101List")) {
                for (Ru0101 ru0101 : ru0101List) {
                    Map<String, String> extMap = new HashMap<>();
                    String summaryRecordId = ru0101.getSummaryRecordId();
                    extMap.put("SummaryRecordId", summaryRecordId);
                    String usages = ru0101.getUsages();
                    if (StringUtils.isNotBlank(usages)) {
                        BigDecimal totalUsages = BigDecimal.ZERO;
                        String[] split = usages.split("\\|");
                        if (split.length > 1) {
                            for (String key : split
                            ) {
                                totalUsages.add(new BigDecimal(key));
                            }
                        } else {
                            totalUsages = new BigDecimal(split[0]);
                        }
                        extMap.put("FormatUsages", totalUsages.toString());
                        totalRu0101Usage = totalRu0101Usage.add(totalUsages);
                    }
                    extMap.put("Usages", usages);

                    String contentCategory = ru0101.getContentCategory();
                    if ("Music".equals(contentCategory)) {
                        extMap.put("Music", contentCategory);
                    }
                    if ("GEMusic".equals(contentCategory)) {
                        extMap.put("GEMusic", contentCategory);
                    }
                    if ("GE".equals(contentCategory)) {
                        extMap.put("GE", contentCategory);
                    }
                    extJsonList.add(extMap);
                }
            }
            object.put("ru0101List", extJsonList);
        }
    }

    private void createListDspFileDataMappingByAs0101(JSONObject object, ListDspFileDataMapping listDspFileDataMapping, As0101 as0101) {
        listDspFileDataMapping.setGroupCode(as0101.getBlockId());// 用于分组
        listDspFileDataMapping.setIsrc(as0101.getISRC());
        listDspFileDataMapping.setResourceId(as0101.getDspResourceId());
        listDspFileDataMapping.setWorkArtist(as0101.getDisplayArtistName());
        listDspFileDataMapping.setDurationStr(as0101.getDuration());
        listDspFileDataMapping.setDurationM(StringUtils.isNotBlank(as0101.getDurationM()) ? Integer.valueOf(as0101.getDurationM()) : null);
        listDspFileDataMapping.setDurationS(StringUtils.isNotBlank(as0101.getDurationS()) ? Integer.valueOf(as0101.getDurationS()) : null);
        listDspFileDataMapping.setReleaseType(as0101.getResourceType());
        String as0101Title = as0101.getTitle();
        if (StringUtils.isNotBlank(as0101Title)) {
            listDspFileDataMapping.setTitle(as0101Title);
        }
        String as0101SubTitle = as0101.getSubTitle();
        if (StringUtils.isNotBlank(as0101SubTitle)) {
            listDspFileDataMapping.setAlbumTitle(as0101SubTitle);
        }
        if (null == object.get("as0101")) {
            Map<String, String> extMap = new HashMap<>();
            extMap.put("name", "AS0101");

            String resourceReference = as0101.getResourceReference();
            extMap.put("ResourceReference", resourceReference);

            String displayArtistPartyId = as0101.getDisplayArtistPartyId();
            extMap.put("DisplayArtistPartyId", displayArtistPartyId);

            String isMasterRecording = as0101.getIsMasterRecording();
            extMap.put("IsMasterRecording", isMasterRecording);
            object.put("as0101", extMap);
        }
    }

    protected void init() {
        //每次执行前初始化下公用类，防止程序出错，遗留脏数据（由于定时任务执行是设置串行执行的，暂时没有多线程安全问题；后期定时任务执行设置成了并行执行，需要重构）
        Sy0401List.clear();
        Sy0202List.clear();
        Sy0502List.clear();
        Sy09List.clear();
        header = new Header();
        connectListDspFileBaseMap.clear();
        dspFileBaseIdMap.clear();
        isParse = false;
        nowBlockId = "";
        totalSy0202Usage = BigDecimal.ZERO;
        totalRu0101Usage = BigDecimal.ZERO;
        tsvCommonObjectList.clear();
        footer = new Footer();
    }

    public ListFileQueue getListFileQueue() {
        return listFileQueue;
    }

    public void setListFileQueue(ListFileQueue listFileQueue) {
        this.listFileQueue = listFileQueue;
    }

    @Override
    public String getCompanyIdentification() {
        return "youtube";
    }
}
