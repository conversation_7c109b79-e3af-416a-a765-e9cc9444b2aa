package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistMoneyDetailInfo;

import java.util.List;

@Repository
public interface DistMoneyDetailInfoMapper extends BaseMapper<DistMoneyDetailInfo> {

	public List<DistMoneyDetailInfo> findDetailInfoByTypeId(@Param("typeId")Long typeId,
			@Param("startTime")String startTime,@Param("endTime")String endTime,
			@Param("listName")String listName);
	
	public List<DistMoneyDetailInfo> findDetailInfoByInfoId(@Param("typeId")Long typeId,
			@Param("fileInfoId")Long fileInfoId);
}