package tw.org.must.must.core.parse;

import com.alibaba.fastjson.JSONObject;
import com.firstbrave.api.ipi.vo.*;
import tw.org.must.must.common.enums.*;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.service.mbr.MbrIpService;
import tw.org.must.must.model.mbr.*;

import java.util.Date;

/**
 * 解析实体转换
 * 
 * <AUTHOR>
 *
 */
public class BeanToBeanIpi {
	/**
	 * MCN
	 * 
	 * @param transaction
	 * @param nameMultiIpConnection
	 * @return
	 */
	public static MbrIpName getMbrIpName(Transaction transaction, NameMultiIpConnection nameMultiIpConnection) {
		MbrIpName mbrIpName = new MbrIpName();
		mbrIpName.init();
		mbrIpName.setIpiAction(MbrIpiActionEnum.NEW.getCode());
//		mbrIpName.setFirstName(nameMultiIpConnection.getName());
		mbrIpName.setLastName(nameMultiIpConnection.getName());
		
		
		mbrIpName.setNameType(nameMultiIpConnection.getNameType());

		mbrIpName.setIpNameNoRef(transaction.getIpNameNumberRef() + "");
		mbrIpName.setIpNameNo(addZeroForNum(nameMultiIpConnection.getIpNameNumber() + "", 11));
		return mbrIpName;
	}

	/**
	 * NCN
	 * 
	 * @param transaction
	 * @param nameSingleIpConnection
	 * @return
	 */
	public static MbrIpName getMbrIpName(Transaction transaction, NameSingleIpConnection nameSingleIpConnection) {
		MbrIpName mbrIpName = new MbrIpName();
		mbrIpName.init();
		mbrIpName.setIpiAction(MbrIpiActionEnum.NEW.getCode());
		
		mbrIpName.setFirstName(nameSingleIpConnection.getFirstName());
		
		mbrIpName.setLastName(nameSingleIpConnection.getName());
		
		mbrIpName.setIpNameNoRef(transaction.getIpNameNumberRef() + "");
		mbrIpName.setNameType(nameSingleIpConnection.getNameType());

		mbrIpName.setIpNameNo(addZeroForNum(nameSingleIpConnection.getIpNameNumber() + "", 11));
		Date date = new Date();
		mbrIpName.setAmendTime(date);
		mbrIpName.setCreateTime(date);
		return mbrIpName;
	}

	/**
	 * ONN
	 * 
	 * @param transaction
	 * @param otherNameConnection
	 * @return
	 */
	public static MbrIpName getMbrIpName(Transaction transaction, OtherNameConnection otherNameConnection) {
		MbrIpName mbrIpName = new MbrIpName();
		mbrIpName.init();
		mbrIpName.setIpiAction(MbrIpiActionEnum.NEW.getCode());
		
		mbrIpName.setFirstName(otherNameConnection.getFirstName());
		mbrIpName.setLastName(otherNameConnection.getName());
		
		mbrIpName.setIpNameNoRef(transaction.getIpNameNumberRef() + "");
		mbrIpName.setNameType(otherNameConnection.getNameType());

		mbrIpName.setIpNameNo(addZeroForNum(otherNameConnection.getIpNameNumber() + "", 11));
		Date date = new Date();
		mbrIpName.setAmendTime(date);
		mbrIpName.setCreateTime(date);
		return mbrIpName;
	}

	/**
	 * MUN
	 *
	 * @param transaction
	 * @param nameMultiIpUsage
	 * @return
	 */
	public static MbrIpNameUsage getMbrIpNameUsage(Transaction transaction, NameMultiIpUsage nameMultiIpUsage) {
		MbrIpNameUsage mbrIpNameUsage = new MbrIpNameUsage();
		mbrIpNameUsage.setCreationClassCode(nameMultiIpUsage.getCreationClassCode());
		mbrIpNameUsage.setRoleCode(nameMultiIpUsage.getRoleCode());
		mbrIpNameUsage.setIpBaseNo(transaction.getIpBaseNumber());

		mbrIpNameUsage.setIpNameNo(addZeroForNum(nameMultiIpUsage.getIpNameNumber() + "", 11));
		Date date = new Date();
		mbrIpNameUsage.setAmendTime(date);
		mbrIpNameUsage.setCreateTime(date);
		return mbrIpNameUsage;
	}

	/**
	 * NTN
	 *
	 * @param transaction
	 * @param nationality
	 * @return
	 */
	public static MbrIpNationality getMbrIpNationality(Transaction transaction, Nationality nationality) {
		MbrIpNationality mbrIpNationality = new MbrIpNationality();
		mbrIpNationality.init();
		String validFromStr = nationality.getValidFromYear() + "" + nationality.getValidFromMonth() + ""
				+ nationality.getValidFromDay();
		String validToStr = nationality.getValidToYear() + "" + nationality.getValidToMonth() + ""
				+ nationality.getValidToDay();
		Date validFrom = DateParse.parseDateTime(validFromStr);
		Date validTo = DateParse.parseDateTime(validToStr);
		mbrIpNationality.setValidFrom(validFrom);
		mbrIpNationality.setValidTo(validTo);
		mbrIpNationality.setCountryCode(nationality.getTisn() + "");
		mbrIpNationality.setTisN(nationality.getTisn());
		mbrIpNationality.setCountry(nationality.getTisan());
		mbrIpNationality.setIpBaseNo(transaction.getIpBaseNumber());
		Date date = new Date();
		mbrIpNationality.setAmendTime(date);
		mbrIpNationality.setCreateTime(date);
		return mbrIpNationality;

	}

	/**
	 * NUN
	 * 
	 * @param transaction
	 * @param nameSingleIpUsage
	 * @return
	 */
	public static MbrIpNameUsage getMbrIpNameUsage(Transaction transaction, NameSingleIpUsage nameSingleIpUsage) {
		MbrIpNameUsage mbrIpNameUsage = new MbrIpNameUsage();
		mbrIpNameUsage.setCreationClassCode(nameSingleIpUsage.getCreationClassCode());
		mbrIpNameUsage.setRoleCode(nameSingleIpUsage.getRoleCode());
		mbrIpNameUsage.setIpNameNo(addZeroForNum(nameSingleIpUsage.getIpNameNumber() + "", 11));
		mbrIpNameUsage.setIpBaseNo(transaction.getIpBaseNumber());

		Date date = new Date();
		mbrIpNameUsage.setAmendTime(date);
		mbrIpNameUsage.setCreateTime(date);
		return mbrIpNameUsage;
	}

	/**
	 * INN
	 * 
	 * @param transaction
	 * @param inheritedNameSingleIpUsage
	 * @return
	 */
	public static MbrIpNameUsage getMbrIpNameUsage(Transaction transaction,
			InheritedNameSingleIpUsage inheritedNameSingleIpUsage) {
		MbrIpNameUsage mbrIpNameUsage = new MbrIpNameUsage();
		mbrIpNameUsage.setCreationClassCode(inheritedNameSingleIpUsage.getCreationClassCode());
		mbrIpNameUsage.setRoleCode(inheritedNameSingleIpUsage.getRoleCode());
		mbrIpNameUsage.setIpNameNo(addZeroForNum(inheritedNameSingleIpUsage.getIpNameNumber() + "", 11));
		mbrIpNameUsage.setIpBaseNo(transaction.getIpBaseNumber());

		Date date = new Date();
		mbrIpNameUsage.setAmendTime(date);
		mbrIpNameUsage.setCreateTime(date);
		return mbrIpNameUsage;
	}

	/**
	 * 会员状态 STN
	 * 
	 * @param transaction
	 * @param status
	 * @return
	 */
	public static MbrIpStatus getMbrIpStatus(Transaction transaction, Status status) {
		MbrIpStatus mbrIpStatus = new MbrIpStatus();
		Date date = new Date();
		mbrIpStatus.setAmendTime(date);
		mbrIpStatus.setCreateTime(date);
		mbrIpStatus.setIpBaseNoRef(transaction.getIpBaseNumberRef());
		mbrIpStatus.setIpBaseNo(transaction.getIpBaseNumber());
		mbrIpStatus.setStatusCode(status.getStatusCode());
		String validFromStr = status.getValidFromDate() + status.getValidFromTime();
		String validToStr = status.getValidToDate() + status.getValidToTime();
		Date validFrom = DateParse.parseDate(validFromStr, DateParse.patternTime);
		Date validTo = DateParse.parseDate(validToStr, DateParse.patternTime);
		mbrIpStatus.setValidFrom(validFrom);
		mbrIpStatus.setValidTo(validTo);
		return mbrIpStatus;
	}

	/**
	 * 转成会员ip BDN
	 * 
	 * @param transaction
	 * @param baseData
	 * @return
	 */
	public static MbrIp getMbrIp(Transaction transaction, BaseData baseData) {
		MbrIp mbrIp = new MbrIp();
		mbrIp.init();
		mbrIp.setIpType(baseData.getTypeOfInterestedParty());
		mbrIp.setSex(baseData.getSex());
		mbrIp.setDummyIpiFlag(DummyIpiFlagEnum.N.getCode());
		mbrIp.setBirthYear(baseData.getYearOfBirth());
		mbrIp.setBirthMounth(baseData.getMonthOfBirth());
		mbrIp.setBirthDay(baseData.getDayOfBirth());
		mbrIp.setDeathYear(baseData.getYearOfDeath());
		mbrIp.setDeathMounth(baseData.getMonthOfDeath());
		mbrIp.setDeathDay(baseData.getDayOfDeath());
		mbrIp.setBirthPlace(baseData.getPlaceOfBirth());
		mbrIp.setBirthState(baseData.getStateOfBirth());
		mbrIp.setBirthCountryName(baseData.getTisanBirth());
		mbrIp.setBirthCountryCode(baseData.getTisnBirth());
		// 会员起始日期 空的
		Date validFrom = DateParse.parseDateTime(baseData.getTisnValidFrom());
		mbrIp.setValidFrom(validFrom);
		Date date = new Date();
		mbrIp.setAmendTime(date);
		mbrIp.setCreateTime(date);
		mbrIp.setIpBaseNoRef(transaction.getIpBaseNumberRef());
		mbrIp.setSocietyCode(transaction.getCodeOfRemittingSociety());
		mbrIp.setIpBaseNo(transaction.getIpBaseNumber());
		return mbrIp;
	}

	/**
	 * 解析MbrIpAgreement 实体
	 * 
	 * @return
	 */
	public static MbrIpAgreement getMbrIpAgreement(Transaction transaction, JSONObject maoOrMan) {
		MbrIpAgreement mbrIpAgreement = new MbrIpAgreement();
		mbrIpAgreement.setIpBaseNo(transaction.getIpBaseNumber());
		Integer societyCode = 99;
		if (maoOrMan.getString("societyCode") != null) {
			societyCode = Integer.valueOf(maoOrMan.getString("societyCode"));
		}
		String creationClassCode = maoOrMan.getString("creationClassCode");
		String roleCode = maoOrMan.getString("roleCode");
		String rightCode = maoOrMan.getString("rightCode");

		String validFromStr = maoOrMan.getString("validFromDate") + maoOrMan.getString("validFromTime");
		String validToStr = maoOrMan.getString("validToDate") + maoOrMan.getString("validToTime");
		Date validFrom = DateParse.parseDateTime(validFromStr);
		Date validTo = DateParse.parseDateTime(validToStr);
		/*if(null != validTo && new Date().after(validTo)) {
			societyCode = 99;
		}*/
		String signatureDateStr = maoOrMan.getString("dateOfSignature");
		Date signatureDate = DateParse.parseDate(signatureDateStr);

		String membershipShareStr = maoOrMan.getString("membershipShare");
		if (membershipShareStr != null && !"".equals(membershipShareStr)) {
			Double membershipShare = (Double.valueOf(membershipShareStr)) / 100.00;
			mbrIpAgreement.setMembershipShare(membershipShare);
		}
		String amendDateStr = maoOrMan.getString("amendmentDate") + maoOrMan.getString("amendmentTime");
		Date amendTime = DateParse.parseDateTime(amendDateStr);
		mbrIpAgreement.setSocietyCode(societyCode);
		mbrIpAgreement.setCreationClassCode(creationClassCode);
		mbrIpAgreement.setRightCode(rightCode);
		mbrIpAgreement.setRoleCode(roleCode);
		mbrIpAgreement.setValidFrom(validFrom);
		mbrIpAgreement.setValidTo(validTo);
		mbrIpAgreement.setSignatureDate(signatureDate);

		mbrIpAgreement.setAmendTime(amendTime);

		mbrIpAgreement.setDataSource(DataSourceEnum.Y.getCode());
		mbrIpAgreement.setCreateTime(new Date());
		mbrIpAgreement.setAmendTime(new Date());

		return mbrIpAgreement;
	}

	public static MbrIpAgreementTerritory getMbrIpAgreementTerritory(Transaction transaction, String maoOrManStr,
			String tmaStr) {
		MbrIpAgreementTerritory mbrIpAgreementTerritory = new MbrIpAgreementTerritory();
		mbrIpAgreementTerritory.setIpBaseNo(transaction.getIpBaseNumber());
		JSONObject maoOrMan = (JSONObject) JSONObject.parse(maoOrManStr);
		Integer societyCode = Integer.valueOf(maoOrMan.getString("societyCode"));
		String creationClassCode = maoOrMan.getString("creationClassCode");
		String roleCode = maoOrMan.getString("roleCode");
		String rightCode = maoOrMan.getString("rightCode");

		String validFromStr = maoOrMan.getString("validFromDate") + "" + maoOrMan.getString("validFromTime");
		String validToStr = maoOrMan.getString("validToDate") + "" + maoOrMan.getString("validToTime");
		Date validFrom = DateParse.parseDateTime(validFromStr);
		Date validTo = DateParse.parseDateTime(validToStr);

		mbrIpAgreementTerritory.setSocietyCode(societyCode);
		mbrIpAgreementTerritory.setCreationClassCode(creationClassCode);
		mbrIpAgreementTerritory.setRightCode(rightCode);
		mbrIpAgreementTerritory.setRoleCode(roleCode);
		mbrIpAgreementTerritory.setValidFrom(validFrom);

		mbrIpAgreementTerritory.setValidTo(validTo);

		mbrIpAgreementTerritory.setCreateTime(new Date());
		mbrIpAgreementTerritory.setAmendTime(new Date());

		JSONObject tma = (JSONObject) JSONObject.parse(tmaStr);

		String ieIndicator = tma.getString("ieIndicator");
		if ("I".equals(ieIndicator)) {
			mbrIpAgreementTerritory.setIndicator(IndicatorEnum.ADD.getCode());
		} else if ("E".equals(ieIndicator)) {
			mbrIpAgreementTerritory.setIndicator(IndicatorEnum.REDUCE.getCode());
		}

		Long tisN = Long.valueOf(tma.getString("tisn"));
		mbrIpAgreementTerritory.setTisN(tisN);
		return mbrIpAgreementTerritory;
	}

	public static MbrIpSocietyRemark getMbrIpSocietyRemark(Transaction transaction, Remark record) {
		MbrIpSocietyRemark mbrIpSocietyRemark = new MbrIpSocietyRemark();
		mbrIpSocietyRemark.setIpNameNo(addZeroForNum(record.getIpNameNumber() + "", 11));
		mbrIpSocietyRemark.setSequenceNo(record.getSequenceNo());
		mbrIpSocietyRemark.setSocietyCode(record.getSocietyCode());
		mbrIpSocietyRemark.setContent(record.getText());
		mbrIpSocietyRemark.setCreateTime(new Date());
		mbrIpSocietyRemark.setAmendTime(new Date());
		return mbrIpSocietyRemark;
	}

	public static String addZeroForNum(String str, int strLength) {
		str = str.replace("-", "");
		int strLen = str.length();
		StringBuffer sb = null;
		while (strLen < strLength) {
			sb = new StringBuffer();
			sb.append("0").append(str);// 左补0
			str = sb.toString();
			strLen = str.length();
		}
		return str;
	}
}
