package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimResultPreclaim;

import java.util.List;

@Repository
public interface ClaimResultPreclaimMapper extends BaseMapper<ClaimResultPreclaim> {

    Long getMinIdByHeaderId(@Param("headerId") Long headerId);

    Long getMinMappingIdByHeaderId(@Param("headerId") Long headerId);

    List<Long> selectFileMappingIdsByHeaderIdAndNotClaimFlag(@Param("headerId")Long headerId, @Param("claimFlag")String claimFlag);
}