<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="800MEN_main" pageWidth="1000" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="1000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ba8dba43-c113-483a-86e6-04dc0531e5a7">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="date" class="java.lang.String"/>
	<parameter name="reportId" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="count" class="java.lang.String"/>
	<parameter name="total" class="java.lang.String"/>
	<parameter name="taxAmount" class="java.lang.String"/>
	<parameter name="commissionAmount" class="java.lang.String"/>
	<parameter name="reciprocalAmount" class="java.lang.String"/>
	<parameter name="paymentTotal" class="java.lang.String"/>
	<parameter name="autoPayNo" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="detailList" class="java.lang.Object"/>
	<field name="summaryTotal" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="46" splitType="Stretch"/>
	</title>
	<columnHeader>
		<band height="155" splitType="Stretch">
			<staticText>
				<reportElement x="840" y="80" width="60" height="20" uuid="d0306256-43b7-413a-882f-d264bac7230c"/>
				<text><![CDATA[REPORT ID: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="220" y="44" width="560" height="20" uuid="f104f319-8fff-4560-9e61-adb812cd0c78"/>
				<textElement>
					<font fontName="华文宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[
					TAIWAN MEMBER ROYALTIES PAYMENT BY CABLE TRANSFER/DEMAND/CHEQUE LIST 台灣會員其他附款方式報表]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="14" width="380" height="20" uuid="2c165201-b1d5-4dbe-a7ff-ff54f780a8a6"/>
				<textElement>
					<font fontName="华文宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[MUSIC COPYRIGHT SOCIETY OF CHINESE TAIPEI 社團法人中華音樂著作權協會]]></text>
			</staticText>
			<staticText>
				<reportElement x="840" y="44" width="30" height="20" uuid="67dfef90-a592-42f9-abf8-f1de19997b11"/>
				<text><![CDATA[DATE:]]></text>
			</staticText>
			<staticText>
				<reportElement x="20" y="80" width="219" height="20" uuid="dc172ae4-788a-4deb-93b1-68cc6bc13525"/>
				<text><![CDATA[ROYALTIES DISTRIBUTION BY PAYMENT NO ]]></text>
			</staticText>
			<staticText>
				<reportElement x="20" y="119" width="110" height="20" uuid="ee95c9cb-b347-4ae6-8eb1-7f0c52cd1552"/>
				<text><![CDATA[SOCIETY]]></text>
			</staticText>
			<staticText>
				<reportElement x="135" y="119" width="90" height="20" uuid="9151603f-211d-4217-a3fe-48c30da4e360"/>
				<text><![CDATA[DIST NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="119" width="100" height="20" uuid="471886a0-dbd6-47d7-af01-0064d20ae831"/>
				<text><![CDATA[PAYABLE TO ]]></text>
			</staticText>
			<staticText>
				<reportElement x="335" y="119" width="90" height="20" uuid="ccaf3f6f-fb47-4982-8aac-d0e2db1400b0"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[ROYALTIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="430" y="119" width="80" height="30" uuid="23ebfc59-1d30-4086-b738-1adf30f6c2c0"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[PAYABLE
AMOUNT]]></text>
			</staticText>
			<staticText>
				<reportElement x="610" y="119" width="70" height="30" uuid="1ad56412-1b56-419f-97c5-77d23a947931"/>
				<text><![CDATA[CURRENCY
DRAWN ON]]></text>
			</staticText>
			<staticText>
				<reportElement x="690" y="119" width="100" height="20" uuid="5f5e9372-722b-4fb4-8477-237eb6a55855"/>
				<text><![CDATA[PAYMENT DETAIL]]></text>
			</staticText>
			<line>
				<reportElement x="20" y="150" width="960" height="1" forecolor="#000000" uuid="cf917b94-d264-49e6-bc38-f755743d3c61"/>
			</line>
			<staticText>
				<reportElement x="515" y="120" width="80" height="29" uuid="f45ad949-9a3d-4205-8ddf-a31104f97a36"/>
				<text><![CDATA[NET
CONVERT]]></text>
			</staticText>
			<textField>
				<reportElement x="870" y="44" width="100" height="20" uuid="3cf16da3-16e9-4a29-a12b-93c4dbda5de2"/>
				<textFieldExpression><![CDATA[$P{date}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="901" y="80" width="80" height="20" uuid="9cf3909d-0209-497a-aa99-c3ff9ff9be21"/>
				<textFieldExpression><![CDATA[$P{reportId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="80" width="70" height="20" uuid="e25701e3-a8c0-410d-96fc-07df806c3be8"/>
				<textFieldExpression><![CDATA[$P{autoPayNo}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="20" y="23" width="80" height="40" uuid="c32c57df-396c-425d-9fe3-59ab803b5a84"/>
				<imageExpression><![CDATA["ireport/logo.png"]]></imageExpression>
			</image>
		</band>
	</columnHeader>
	<detail>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement x="20" y="8" width="160" height="21" uuid="3dbeb44e-5b2a-4fb0-ad24-5afebf8676a0"/>
				<text><![CDATA[*** BY CABLE TRANSFER ***]]></text>
			</staticText>
			<subreport>
				<reportElement x="20" y="40" width="900" height="40" uuid="e080995e-ab6f-46cc-b695-ae19ef15b9db"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("detailList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPaySociety800_sub.jasper")]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band height="55" splitType="Stretch">
			<textField>
				<reportElement x="400" y="20" width="100" height="30" uuid="c8c64a09-d39e-4cd9-ab73-1c2909633600"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="500" y="20" width="100" height="30" uuid="39637c9c-1265-4245-b855-6d82a8f3093d"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</columnFooter>
	<summary>
		<band height="268" splitType="Stretch">
			<staticText>
				<reportElement x="20" y="110" width="380" height="20" uuid="b4364eb0-700d-49a8-ada3-8bdebe1b9a2b"/>
				<text><![CDATA[
					Summary of Society Royalties Payment by Cable Transfer/Demand/Cheque by Distribution Number]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="143" width="60" height="17" uuid="3ce1a26d-e380-4a1e-acd6-d832b061a1d9"/>
				<text><![CDATA[Dist No]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="143" width="50" height="17" uuid="dd1becd7-c5a0-4831-bf12-2869db6b8808"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[NT$ 台幣]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="230" width="80" height="24" uuid="cfb642df-dfdb-493d-ac72-e6290251a1ab"/>
				<text><![CDATA[Total Royalties]]></text>
			</staticText>
			<subreport>
				<reportElement x="20" y="170" width="314" height="30" uuid="19af173d-9a28-450f-a336-3dcb4ee70304"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("summaryTotal")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPaySociety800_subTwo.jasper")]]></subreportExpression>
			</subreport>
			<line>
				<reportElement x="170" y="210" width="110" height="1" uuid="6b512ce3-38f9-4542-a1be-2e17ba67f819"/>
			</line>
			<line>
				<reportElement x="175" y="260" width="105" height="1" uuid="36a51143-ab05-4787-81c8-802352585c45"/>
			</line>
			<line>
				<reportElement x="175" y="262" width="105" height="1" uuid="f51421f0-9e5d-405f-929a-45f0306ce19a"/>
			</line>
			<staticText>
				<reportElement x="20" y="20" width="126" height="20" uuid="ae27d55d-7054-49c9-8fca-bc6dd66de0b5"/>
				<text><![CDATA[TOTAL NO. OF SOCIETY:]]></text>
			</staticText>
			<textField>
				<reportElement x="146" y="20" width="44" height="20" uuid="64afc3d4-3ead-44df-adb5-f1cfb7832144"/>
				<textFieldExpression><![CDATA[$P{count}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="285" y="20" width="30" height="20" uuid="9afebf3c-b4bd-4117-acfb-5b786b441091"/>
				<text><![CDATA[Total:]]></text>
			</staticText>
			<staticText>
				<reportElement x="249" y="40" width="66" height="20" uuid="2f5fe705-2595-429d-ad1a-b9089b735832"/>
				<text><![CDATA[Withheld Tax:]]></text>
			</staticText>
			<staticText>
				<reportElement x="254" y="60" width="62" height="20" uuid="c96b1341-6be5-47d0-a4de-970e4a241dd3"/>
				<text><![CDATA[Commission:]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="80" width="75" height="20" uuid="d035d76f-3f41-4e2b-a354-0430146a3e1d"/>
				<text><![CDATA[Reciprocal Amt:]]></text>
			</staticText>
			<textField>
				<reportElement x="360" y="80" width="90" height="20" uuid="397ea354-70ac-44ca-aa40-0f5b5e23cd0c"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" + $P{reciprocalAmount} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="360" y="60" width="90" height="20" uuid="6cf5203c-96c5-4c6e-9fef-2bb39a05a664"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" +$P{commissionAmount} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="360" y="40" width="90" height="20" uuid="34c19f75-47b0-40cf-ae18-15a4e1e677b0"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" + $P{taxAmount} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="360" y="20" width="90" height="20" uuid="b281131f-7e02-4146-a3bc-6cdc7645d6f2"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{total}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="480" y="20" width="90" height="20" uuid="c0063876-07a4-4d84-9890-bf3898b8cb94"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{paymentTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="180" y="230" width="100" height="20" uuid="b281131f-7e02-4146-a3bc-6cdc7645d6f2"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $P{total}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
