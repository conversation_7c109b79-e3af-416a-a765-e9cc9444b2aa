package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_mrd_sc_label`")
public class DistMrdScLabel extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`label_name`")
    private String labelName;

    /**
     * 
     */
    @Column(name = "`producer_code`")
    private String producerCode;

    @Column(name="producer_name")
    private String producerName;

    /**
     * 
     */
    @Column(name = "`create_user`")
    private String createUser;

    /**
     * 
     */
    @Column(name = "`amend_user`")
    private String amendUser;

    public String getProducerName() {
        return producerName;
    }

    public void setProducerName(String producerName) {
        this.producerName = producerName;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public String getProducerCode() {
        return producerCode;
    }

    public void setProducerCode(String producerCode) {
        this.producerCode = producerCode;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getAmendUser() {
        return amendUser;
    }

    public void setAmendUser(String amendUser) {
        this.amendUser = amendUser;
    }



}