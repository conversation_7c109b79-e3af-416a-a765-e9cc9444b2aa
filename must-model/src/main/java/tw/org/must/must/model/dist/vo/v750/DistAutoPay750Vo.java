package tw.org.must.must.model.dist.vo.v750;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.json.AlexBigDecimalSerializer;
import tw.org.must.must.common.util.LocalCommonMethodUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: hanDa
 * @Date: 2021/5/19 15:49
 * @Version:1.0
 * @Description:
 */
@Data
public class DistAutoPay750Vo {
    private String autopayNo;
    private String paNameNo;
    private String paName;
    @JsonSerialize(using = AlexBigDecimalSerializer.class)
    private String balance;
    private String accountNumber;
    private String bankName;
    private Long bankCode;
    private Long branchNo;
    private String bankAccountName;
    private String distNo;
    private String payAmount;
    private String payMethod;
    private String payDescription;

    public String getBankCode() {
        return bankCode == null ? "" : String.format("%06d", bankCode);
    }

    public BigDecimal getBalanceA() {
        return balance == null ? BigDecimal.ZERO : new BigDecimal(balance).setScale(2, RoundingMode.HALF_UP);
    }

    public String getBalance() {
        return LocalCommonMethodUtils.formate(balance, LocalCommonMethodUtils.BIGDECIMAL_PATTERN_SCALE_2);
    }

    public String getPayMethod() {
        if (StringUtils.isBlank(payMethod)) {
            return payMethod;
        }
        //支付方式T=telegraphic transfer, A=Auto transfer, D=Draft, C=Cheque, O=Overseas Draft, X=Not provided'
        switch (payMethod) {
            case "T":
                return "telegraphic transfer";
            case "A":
                return "Auto transfer";
            case "D":
                return "Draft, C=Cheque";
            case "O":
                return "Draft, C=Cheque";
            case "X":
                return "Not provided";
        }
        return payMethod;
    }
}