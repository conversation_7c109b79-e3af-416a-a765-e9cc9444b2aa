package tw.org.must.must.model.wrk.vo;

import lombok.Data;

/**
 * @Author: hanDa
 * @Date: 2020/9/23 10:33
 * @Version:1.0
 * @Description:
 */
@Data
public class WorkSocietyVo implements Cloneable{
    private Long workId;
    private Integer workSocietyCode;
    private String title;
    private Long parentWorkId;
    private Integer parentWorkSocietyCode;

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}