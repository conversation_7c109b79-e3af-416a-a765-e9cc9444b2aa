<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="780_sub" pageWidth="800" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="800"
              leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ae8c6fbc-e991-4856-94d4-50ca6e3d4253">
    <parameter name="SUBREPORT_DIR" class="java.lang.String">
        <defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
    </parameter>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="royaltiesType" class="java.lang.String"/>
    <field name="distAutoPayReceiptDetail" class="java.lang.Object"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <detail>
        <band height="54" splitType="Stretch">
            <textField>
                <reportElement x="0" y="0" width="240" height="25" uuid="3a0ba39c-ba9b-4b03-b48d-fa7841467370"/>
                <textElement>
                    <font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{royaltiesType}]]></textFieldExpression>
            </textField>
            <subreport>
                <reportElement x="10" y="25" width="790" height="25" uuid="613245f0-7032-49d4-8d8a-66ed14d5663d"/>
                <dataSourceExpression>
                    <![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distAutoPayReceiptDetail")]]></dataSourceExpression>
                <subreportExpression>
                    <![CDATA[$P{SUBREPORT_DIR}+"distAutoPays/DistAutoPay780_child.jasper"]]></subreportExpression>
            </subreport>
        </band>
    </detail>
</jasperReport>
