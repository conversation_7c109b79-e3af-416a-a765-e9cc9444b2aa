package tw.org.must.must.core.valid;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import tw.org.must.must.common.annotation.FieldVaild;
import tw.org.must.must.core.exception.CustomizeException;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Aspect
@Component
public class FieldVaildAspect {

    @Pointcut("execution(* tw.org.must.must.core.valid.ValidService.addWithValid(..))")
    public void valid() {
    }


    @Before("valid()")
    public void before(JoinPoint point) throws Throwable {
        //System.out.println("single valid");
        Object[] args = point.getArgs();
        for (Object arg : args) {
            if (!(arg instanceof List)) {
                vaild(arg);
            }
        }
    }

    public Boolean vaild(Object object) throws CustomizeException {
        if (object == null) {
            throw new CustomizeException("实体类校验时，基础数据选择不能为空");
        } else {
            List<String> sb = new ArrayList<>();
            try {
                Field[] fields = object.getClass().getDeclaredFields();
                for (int i = 0; i < fields.length; i++) {
                    FieldVaild annotation = fields[i].getAnnotation(FieldVaild.class);
                    if (Objects.isNull(annotation)) {
                        continue;
                    }
                    fields[i].setAccessible(true);
                    Object o = fields[i].get(object);
                    if (Objects.isNull(o)) {
                        if (annotation.isNotNull()) {
                            sb.add(fields[i].getName() + "不能为空！");
                        }
                    } else {
                        if (annotation.isNotNull() && StringUtils.isBlank(o.toString())) {
                            sb.add(fields[i].getName() + "不能为空！");
                        }
                        if (o instanceof String && ((String) o).length() > annotation.stringLength()) {
                            //sb.add(fields[i].getName() + "字段超过数据库指定长度" + annotation.stringLength());
                            fields[i].set(object, ((String) o).substring(0, annotation.stringLength()));
                        }
                    }
                }
            } catch (IllegalAccessException e) {
                throw new CustomizeException("基础校验失败" + e.getMessage());
            }
            if (!sb.isEmpty()) {
                throw new CustomizeException(String.join("; ", sb));
            }
        }
        return true;
    }
}
