package tw.org.must.must.model.sys.vo;

import tw.org.must.must.model.sys.SysUser;

import java.io.Serializable;

public class UserVO implements Serializable {
    
    private static final long serialVersionUID = -8372173143599579455L;

    private String userId;

    private String name;

    private String nameEn;

    public static UserVO build(SysUser sysUser) {
        return new UserVO(String.valueOf(sysUser.getId()),
                sysUser.getName(),
                sysUser.getNameEn());
    }

    public UserVO() {
    }

    public UserVO(String userId, String name, String nameEn) {
        this.userId = userId;
        this.name = name;
        this.nameEn = nameEn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    @Override
    public String toString() {
        return "UserVO{" +
                "userId='" + userId + '\'' +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                '}';
    }
}
