package tw.org.must.must.core.parse;

import java.nio.charset.MalformedInputException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import com.firstbrave.api.avr.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.firstbrave.api.base.AvrRecord;
import com.firstbrave.api.parser.AvrFileDecoder;
import com.firstbrave.api.parser.AvrFileEncoder;

import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.exception.CustomException;
import tw.org.must.must.core.service.avr.AvrWorkService;
import tw.org.must.must.core.service.avr.AvrWorkShareService;
import tw.org.must.must.core.service.avr.AvrWorkTitleService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.wrk.WrkWorkComponentService;
import tw.org.must.must.core.service.wrk.WrkWorkIpShareService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.core.service.wrk.WrkWorkTitleService;
import tw.org.must.must.model.avr.AvrWork;
import tw.org.must.must.model.avr.AvrWorkShare;
import tw.org.must.must.model.avr.AvrWorkTitle;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkComponent;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;
import tw.org.must.must.model.wrk.vo.WorkSocietyVo;

@Service
public class ParseAvrService {

    @Autowired
    private AvrWorkService avrWorkService;

    @Autowired
    private AvrWorkShareService avrWorkShareService;

    @Autowired
    private AvrWorkTitleService avrWorkTitleService;

    @Autowired
    private WrkWorkService wrkWorkService;

    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;

    @Autowired
    private WrkWorkComponentService wrkWorkComponentService;

    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;

    @Autowired
    private MbrIpNameService mbrIpNameService;

    /**
     * 解析AVR文件
     *
     * @param file
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public String parse(Long fId, String file) {
        StringBuffer errorMsg = new StringBuffer();
        Path path = Paths.get(file);
        if (!path.toFile().exists()) {
            System.out.println("avr 文件不存在");
            errorMsg.append("avr 文件不存在");
            return errorMsg.toString();
        }

        try {
            List<String> lines = Files.readAllLines(path);
            List<AvrRecord> records = AvrFileDecoder.decode(new LinkedList<>(lines));
            if (!records.isEmpty()) {
                insertDb(fId, records);
            }
            return errorMsg.toString();
        } catch (MustException e) {
            errorMsg.append(e.getMessage());
            e.printStackTrace();
        } catch (MalformedInputException e) {
            errorMsg.append(String.format("文件不符合AVR文件规则，请确认后重新上传---%s",e.getClass().getSimpleName() + e.getMessage())+"！");
            e.printStackTrace();
        } catch (Exception e) {
            errorMsg.append(e.getMessage());
            e.printStackTrace();
        }
        return errorMsg.toString();
    }


    @Transactional(rollbackFor = Exception.class)
    public void insertDb(Long fId, List<AvrRecord> records) {
        TransmissionHeader hdr = null;
        AvWorkNotification avr = null;
        AlternateAvWorkTitle aat = null;
        CueExistingWorkUsage cue = null;
        MusicalWorkInformation mwi = null;
        InterestedPartyForMusicalWork mip = null;
        ShareForMusicalWork msr = null;
        AssociatedNamesForAvWork aan = null;
        AvResponse avn = null ;

        List<AvrWork> works = new ArrayList<>();
        List<AvrWorkTitle> workTitles = new ArrayList<>();
        List<AvrWorkShare> workShares = new ArrayList<>();
        AvrWork work = null;

        for (AvrRecord record : records) {
            if (record instanceof TransmissionHeader) {
                hdr = (TransmissionHeader) record;
            } else if (record instanceof AvWorkNotification) {
                avr = (AvWorkNotification) record;
                work = BeanToBeanAvr.transform(Objects.requireNonNull(hdr), avr);
                work.setfId(fId);
            } else if (record instanceof AlternateAvWorkTitle) {
                aat = (AlternateAvWorkTitle) record;
                AvrWorkTitle workTitle = BeanToBeanAvr.transform(Objects.requireNonNull(hdr),
                        Objects.requireNonNull(avr), aat);
                workTitle.setSocWorkNo(work.getSocWorkNo());
                workTitle.setDataId(work.getId());
                workTitles.add(workTitle);
            } else if (record instanceof CueExistingWorkUsage) {
                cue = (CueExistingWorkUsage) record;
            } else if (record instanceof MusicalWorkInformation) {
                mwi = (MusicalWorkInformation) record;
                work = BeanToBeanAvr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(avr),
                        Objects.requireNonNull(cue), mwi);
                work.setfId(fId);
            } else if (record instanceof InterestedPartyForMusicalWork) {
                mip = (InterestedPartyForMusicalWork) record;
            } else if (record instanceof ShareForMusicalWork) {
                msr = (ShareForMusicalWork) record;
                AvrWorkShare workShare = BeanToBeanAvr.transform(Objects.requireNonNull(hdr),
                        Objects.requireNonNull(avr), Objects.requireNonNull(mip), msr);
                workShare.setSocWorkNo(work.getSocWorkNo());
                workShare.setDataId(work.getId());
                workShares.add(workShare);
            } else if(record instanceof  AssociatedNamesForAvWork){
                aan = (AssociatedNamesForAvWork)record;
                if(StringUtils.equals(aan.getRole(),"RE")){


                }

            }else if(record instanceof GroupTrailer){
                avrWorkService.add(work);
                works.add(work);

            }
        }
        if (CollectionUtils.isEmpty(works)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "按解析规则无可入库作品数据！");
        }
        if (CollectionUtils.isNotEmpty(workTitles)) {
            workTitles.forEach(workTitle -> workTitle.setfId(fId));
        }
        if (CollectionUtils.isNotEmpty(workShares)) {
            workShares.forEach(workShare -> workShare.setfId(fId));
        }
        avrWorkTitleService.addList(workTitles);
        avrWorkShareService.addList(workShares);
    }

    /***
     * @Description: 生成AVR文档记录
     * @Param: [workIdWorkSocietyCodeList]
     * @return: java.util.List<java.lang.String>
     * @Author: hanDa
     * @Date: 2020/9/23 10:35
     */
    public List<String> generate(List<WorkSocietyVo> workIdWorkSocietyCodeList) throws IllegalAccessException {
        List<WorkSocietyVo> workSocietyVoList = new ArrayList<>();
        workIdWorkSocietyCodeList.forEach(workSocietyVo -> {
            workSocietyVo.setParentWorkSocietyCode(workSocietyVo.getWorkSocietyCode());
            workSocietyVo.setParentWorkSocietyCode(workSocietyVo.getWorkSocietyCode());
        });
        workSocietyVoList.addAll(workIdWorkSocietyCodeList);

        int groupId = 1;
        int transactionSequenceNo = 0;
        int recordSequenceNo = 0;
        List<AvrRecord> records = new ArrayList<>();
        TransmissionHeader hdr = BeanToBeanAvr.transformToHdr();
        records.add(hdr);
        GroupHeader grh = BeanToBeanAvr.transformToGrh(groupId);
        records.add(grh);
        for (WorkSocietyVo workIdWorkSocietyCode : workSocietyVoList) {
            Long workId = workIdWorkSocietyCode.getWorkId();
            Integer workSocietyCode = workIdWorkSocietyCode.getWorkSocietyCode();
            WrkWork work = wrkWorkService.getWrkWorkByWorkId(workId, workSocietyCode);
            if (work == null) {
                throw new CustomException("该作品不存在", HttpStatus.BAD_REQUEST);
            }
            boolean hasOtTitle = false;
            List<WrkWorkTitle> aWorkTitles = wrkWorkTitleService.getWrkWorkTitleByWrkId(workId, workSocietyCode);
            for (WrkWorkTitle title : aWorkTitles) {
                if ("OT".equals(title.getTitleType())) {
                    hasOtTitle = true;
                    AvWorkNotification avr = BeanToBeanAvr.transformToAvr(work, title, transactionSequenceNo,
                            recordSequenceNo);
                    records.add(avr);
                    recordSequenceNo++;
                    break;
                }
            }
            if (!hasOtTitle) {
                throw new CustomException("该作品原标题不存在", HttpStatus.BAD_REQUEST);
            }
            for (WrkWorkTitle title : aWorkTitles) {
                if ("OT".equals(title.getTitleType())) {
                    continue;
                }
                AlternateAvWorkTitle aat = BeanToBeanAvr.transformToAat(title, transactionSequenceNo, recordSequenceNo);
                records.add(aat);
                recordSequenceNo++;
            }
            List<WrkWorkComponent> components = wrkWorkComponentService.getWrkWorkComponentByWrkId(workId,
                    workSocietyCode);
            for (WrkWorkComponent component : components) {
                WrkWork mWork = wrkWorkService.getWrkWorkByWorkId(component.getComponentWorkId(), component.getComWorkSociety());
                if (mWork == null) {
                    throw new CustomException("该作品" + component.getWorkId() + "不存在", HttpStatus.BAD_REQUEST);
                }
                AtomicReference<WrkWorkTitle> mWorkTitle = new AtomicReference<>();
                wrkWorkTitleService.getWrkWorkTitleByWrkId(component.getComponentWorkId(), component.getComWorkSociety()).forEach(title -> {
                    if ("OT".equals(title.getTitleType())) {
                        mWorkTitle.set(title);
                    }
                });
                if (mWorkTitle.get() == null) {
                    throw new CustomException("该作品" + component.getWorkId() + "原标题不存在", HttpStatus.BAD_REQUEST);
                }
                CueExistingWorkUsage cue = BeanToBeanAvr.transformToCue(mWork, mWorkTitle.get(), component,
                        transactionSequenceNo, recordSequenceNo);
                records.add(cue);
                recordSequenceNo++;
                MusicalWorkInformation mwi = BeanToBeanAvr.transformToMwi(mWork, mWorkTitle.get(),
                        transactionSequenceNo, recordSequenceNo);
                records.add(mwi);
                recordSequenceNo++;
                List<WrkWorkIpShare> mShares = wrkWorkIpShareService.getWrkWorkIpShareByWrkId(component.getWorkId(),
                        workSocietyCode);
                for (WrkWorkIpShare share : mShares) {
                    MbrIpName name = mbrIpNameService.getMbrIpNameByIpNameNo(share.getIpNameNo());
                    if (name == null) {
                        throw new CustomException("该IP Name" + share.getIpNameNo() + "不存在", HttpStatus.BAD_REQUEST);
                    }
                    InterestedPartyForMusicalWork mip = BeanToBeanAvr.transformToMip(share, name, transactionSequenceNo,
                            recordSequenceNo);
                    records.add(mip);
                    recordSequenceNo++;
                    ShareForMusicalWork msr = BeanToBeanAvr.transformToMsr(share, transactionSequenceNo,
                            recordSequenceNo);
                    records.add(msr);
                    recordSequenceNo++;
                }
            }
            transactionSequenceNo++;
            recordSequenceNo = 0;
        }
        GroupTrailer grt = BeanToBeanAvr.transformToGrt(groupId, records.size(), transactionSequenceNo);
        records.add(grt);
        TransmissionTrailer trl = BeanToBeanAvr.transformToTrl(1, records.size() + 1, transactionSequenceNo);
        records.add(trl);
        return AvrFileEncoder.encode(records);
    }
}
