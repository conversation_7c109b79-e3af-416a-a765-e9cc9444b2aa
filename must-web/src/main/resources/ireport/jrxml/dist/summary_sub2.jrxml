<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_fiche_rpt_subreport_otherTitle" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fb506072-2bad-45c1-964e-a63c3177c298">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="name" class="java.lang.String"/>
	<field name="role" class="java.lang.String"/>
	<field name="amount" class="java.lang.String"/>
	<field name="share" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="32" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="260" y="10" width="201" height="1" forecolor="#482AB5" uuid="e389eb82-a62f-4181-882f-0e2d8467e012">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="260" y="11" width="1" height="21" forecolor="#482AB5" uuid="e96b031d-58fb-4768-b0cc-6d2f90118c1d">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="360" y="11" width="1" height="20" forecolor="#482AB5" uuid="ed0f4c84-ec25-41aa-bb8b-74c8ea24c93e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="460" y="11" width="1" height="20" forecolor="#482AB5" uuid="fc7825bd-29d9-42b3-8dde-b55ba7d5d098">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
			<textField>
				<reportElement positionType="Float" x="378" y="14" width="78" height="14" uuid="345655c8-d539-449b-9cf3-d905998df981">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{share}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="278" y="14" width="78" height="14" uuid="cdaa65b5-4fdb-43d4-8161-b59f142641d2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{amount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="10" y="14" width="78" height="14" uuid="751307e6-b0b2-4afa-8374-7caf90cc0667">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="150" y="14" width="78" height="14" uuid="4f64bbcd-c42f-4759-8215-c15e1b8577ee">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{role}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="0" width="486" height="1" isPrintInFirstWholeBand="true" forecolor="#482AB5" uuid="270538ee-f16a-45a6-b8a8-1391e684d1d9">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="31" width="486" height="1" isPrintInFirstWholeBand="true" forecolor="#482AB5" uuid="a0e0f88d-c5e6-4d54-af71-0c1e4ad82049">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="485" y="1" width="1" height="30" forecolor="#482AB5" uuid="98452948-8d3b-4543-93fa-eca75d7317f0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="1" width="1" height="30" forecolor="#482AB5" uuid="dd59a085-98b6-4615-9e44-010efea04ccd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
		</band>
	</detail>
</jasperReport>
