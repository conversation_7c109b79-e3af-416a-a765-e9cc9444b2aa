package tw.org.must.must.core.shiro;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tw.org.must.must.core.service.sys.SysMenuService;
import tw.org.must.must.core.service.sys.SysRoleService;
import tw.org.must.must.core.service.sys.SysUserService;
import tw.org.must.must.model.sys.SysMenu;
import tw.org.must.must.model.sys.SysRole;
import tw.org.must.must.model.sys.SysUser;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ShiroRealm extends AuthorizingRealm {

    private final SysUserService sysUserService;

    private final SysRoleService sysRoleService;

    private final SysMenuService sysMenuService;


    @Override
    public String getName() {
        return "shiroRealm";
    }

    @Autowired
    public ShiroRealm(SysUserService sysUserService, SysRoleService sysRoleService, SysMenuService sysMenuService) {
        this.sysUserService = sysUserService;
        this.sysRoleService = sysRoleService;
        this.sysMenuService = sysMenuService;
    }

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        SysUser user = (SysUser) principalCollection.getPrimaryPrincipal();

        List<SysRole> roleList = sysRoleService.listByUserId(user.getId());
        Set<String> roleCodeSet = roleList.stream().map(SysRole::getCode).collect(Collectors.toSet());
        info.addRoles(roleCodeSet);

        List<SysMenu> menuList = sysMenuService.listByUserId(user.getId());
        Set<String> menuCodeSet = menuList.stream().map(SysMenu::getPerms).collect(Collectors.toSet());
        info.addStringPermissions(menuCodeSet);

        return info;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authenticationToken;
        String account = token.getUsername();
        SysUser user = sysUserService.getUserByAccount(account);
        if (user == null) {
            throw new UnknownAccountException("This account does not exist !");
        }
        if (user.getDeleted()) {
            throw new DisabledAccountException("This account is locked !");
        }
        clearCache();
        return new SimpleAuthenticationInfo(user, user.getPassword(), getName());
    }

    /**
     * 清除当前用户权限缓存
     * 使用方法：在需要清除用户权限的地方注入 ShiroRealm,
     * 然后调用其 clearCache方法。
     */
    public void clearCache() {
        PrincipalCollection principals = SecurityUtils.getSubject().getPrincipals();
        super.clearCache(principals);
    }
}
