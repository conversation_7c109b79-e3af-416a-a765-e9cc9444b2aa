package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_no_info`")
public class DistNoInfo extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "dist_no")
    private String distNo;

    /**
     * 分配编号描述
     */
    @Column(name = "dist_no_description")
    private String distNoDescription;

    /**
     * 分配年份
     */
    @Column(name = "dist_year")
    private String distYear;

    /**
     * 分配类别（P、I、M、O）
     */
    @Column(name = "dist_type")
    private String distType;

    /**
     * 创建者id
     */
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    private String createUserName;

    /**
     * 修改者id
     */
    @Column(name = "amend_user_id")
    private Long amendUserId;

    /**
     * 修改者名称
     */
    @Column(name = "amend_user_name")
    private String amendUserName;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getDistNoDescription() {
        return distNoDescription;
    }

    public void setDistNoDescription(String distNoDescription) {
        this.distNoDescription = distNoDescription;
    }

    public String getDistYear() {
        return distYear;
    }

    public void setDistYear(String distYear) {
        this.distYear = distYear;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getAmendUserId() {
        return amendUserId;
    }

    public void setAmendUserId(Long amendUserId) {
        this.amendUserId = amendUserId;
    }

    public String getAmendUserName() {
        return amendUserName;
    }

    public void setAmendUserName(String amendUserName) {
        this.amendUserName = amendUserName;
    }



}