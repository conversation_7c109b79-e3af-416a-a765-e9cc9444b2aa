package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class Sy0502 extends BaseTsvEntity{


	
	private String SummaryRecordId;
	
	private String DistributionChannel;
	
	private String DistributionChannelDPID;
	
	private String CommercialModel;	
	   
	private String UseType;
	
	private String Territory;
	
	private String ServiceDescription;
	
	private String RightsController;
	
	private String RightsControllerPartyID;
	
	private String RightsType;
	
	private String TotalUsages;	
	
	private String AllocatedUsages;
	
	private String MusicUsageRatio;
	
	private String AllocatedNetRevenue;
	
	private String AllocatedRevenue;	
	   
	private String RightsControllerMarketShare;
	
	private String Currency;
	
	private String ExchangeRateBaseCurrency;
	
	private String ExchangeRate;
	
	private String SubscriberType;
	
	private String SubPeriodStartDate;
	
	private String SubPeriodEndDate;
	
	private String ContentCategory;
	
	private String RightsTypePercentage;



	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getDistributionChannel() {
		return DistributionChannel;
	}

	public void setDistributionChannel(String distributionChannel) {
		DistributionChannel = distributionChannel;
	}

	public String getDistributionChannelDPID() {
		return DistributionChannelDPID;
	}

	public void setDistributionChannelDPID(String distributionChannelDPID) {
		DistributionChannelDPID = distributionChannelDPID;
	}

	public String getCommercialModel() {
		return CommercialModel;
	}

	public void setCommercialModel(String commercialModel) {
		CommercialModel = commercialModel;
	}

	public String getUseType() {
		return UseType;
	}

	public void setUseType(String useType) {
		UseType = useType;
	}

	public String getTerritory() {
		return Territory;
	}

	public void setTerritory(String territory) {
		Territory = territory;
	}

	public String getServiceDescription() {
		return ServiceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		ServiceDescription = serviceDescription;
	}

	public String getRightsController() {
		return RightsController;
	}

	public void setRightsController(String rightsController) {
		RightsController = rightsController;
	}

	public String getRightsControllerPartyID() {
		return RightsControllerPartyID;
	}

	public void setRightsControllerPartyID(String rightsControllerPartyID) {
		RightsControllerPartyID = rightsControllerPartyID;
	}

	public String getRightsType() {
		return RightsType;
	}

	public void setRightsType(String rightsType) {
		RightsType = rightsType;
	}

	public String getTotalUsages() {
		return TotalUsages;
	}

	public void setTotalUsages(String totalUsages) {
		TotalUsages = totalUsages;
	}

	public String getAllocatedUsages() {
		return AllocatedUsages;
	}

	public void setAllocatedUsages(String allocatedUsages) {
		AllocatedUsages = allocatedUsages;
	}

	public String getMusicUsageRatio() {
		return MusicUsageRatio;
	}

	public void setMusicUsageRatio(String musicUsageRatio) {
		MusicUsageRatio = musicUsageRatio;
	}

	public String getAllocatedNetRevenue() {
		return AllocatedNetRevenue;
	}

	public void setAllocatedNetRevenue(String allocatedNetRevenue) {
		AllocatedNetRevenue = allocatedNetRevenue;
	}

	public String getAllocatedRevenue() {
		return AllocatedRevenue;
	}

	public void setAllocatedRevenue(String allocatedRevenue) {
		AllocatedRevenue = allocatedRevenue;
	}

	public String getRightsControllerMarketShare() {
		return RightsControllerMarketShare;
	}

	public void setRightsControllerMarketShare(String rightsControllerMarketShare) {
		RightsControllerMarketShare = rightsControllerMarketShare;
	}

	public String getCurrency() {
		return Currency;
	}

	public void setCurrency(String currency) {
		Currency = currency;
	}

	public String getExchangeRateBaseCurrency() {
		return ExchangeRateBaseCurrency;
	}

	public void setExchangeRateBaseCurrency(String exchangeRateBaseCurrency) {
		ExchangeRateBaseCurrency = exchangeRateBaseCurrency;
	}

	public String getExchangeRate() {
		return ExchangeRate;
	}

	public void setExchangeRate(String exchangeRate) {
		ExchangeRate = exchangeRate;
	}

	public String getSubscriberType() {
		return SubscriberType;
	}

	public void setSubscriberType(String subscriberType) {
		SubscriberType = subscriberType;
	}

	public String getSubPeriodStartDate() {
		return SubPeriodStartDate;
	}

	public void setSubPeriodStartDate(String subPeriodStartDate) {
		SubPeriodStartDate = subPeriodStartDate;
	}

	public String getSubPeriodEndDate() {
		return SubPeriodEndDate;
	}

	public void setSubPeriodEndDate(String subPeriodEndDate) {
		SubPeriodEndDate = subPeriodEndDate;
	}

	public String getContentCategory() {
		return ContentCategory;
	}

	public void setContentCategory(String contentCategory) {
		ContentCategory = contentCategory;
	}

	public String getRightsTypePercentage() {
		return RightsTypePercentage;
	}

	public void setRightsTypePercentage(String rightsTypePercentage) {
		RightsTypePercentage = rightsTypePercentage;
	}
	
}
