package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistAutopayNetPayMember;
import tw.org.must.must.model.dist.vo.v750.DistAutoPay750Vo;
import tw.org.must.must.model.dist.vo.v800.mem.DetailMem800;
import tw.org.must.must.model.dist.vo.v800.mem.DistNoDetailMem800;

import java.util.List;
import java.util.Map;

@Repository
public interface DistAutopayNetPayMemberMapper extends BaseMapper<DistAutopayNetPayMember> {

    List<Map<String, Object>> groupDistAutopayNetPayMember(Map<String, Object> param);

    List<DistAutoPay750Vo> getDistAutoPay750List(@Param("autopayNo") String autopayNo, @Param("orderWay") String orderWay, @Param("paNameNoList") List<String> paNameNoList);

    List<DistAutoPay750Vo> getDistAutoPay880List(@Param("autopayNo") String autopayNo, @Param("ipBaseNo") String ipBaseNo, @Param("paNameNo") String paNameNo, @Param("distNo") String distNo);

    List<DetailMem800> getDetailMem800(@Param("autopayNo") String autopayNo, @Param("paNameNo") String paNameNo, @Param("distNo") String distNo);

    List<DistNoDetailMem800> getDistNoDetailMem800(@Param("autopayNo") String autopayNo, @Param("paNameNo") String paNameNo, @Param("distNo") String distNo);

}