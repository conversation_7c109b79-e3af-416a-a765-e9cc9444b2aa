package tw.org.must.must.model.dist.vo.v800.mem;

import lombok.Data;
import tw.org.must.must.common.util.LocalCommonMethodUtils;

import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2021/5/31 11:50
 * @Version:1.0
 * @Description:
 */
@Data
public class DetailMem800 {
    private String payMethod;
    private String paName;
    private String paNameNo;
    private String royalties;
    private String handlingCharge;
    private String payAbleAmount;
    private String payee;
    private String paymentDesc;
    private List<DistNoDetailMem800>  distNoLists;

    public String getRoyalties() {
        return LocalCommonMethodUtils.formateNoScale(royalties);
    }

    public String getHandlingCharge() {
        return LocalCommonMethodUtils.formateNoScale(handlingCharge);
    }

    public String getPayAbleAmount() {
        return LocalCommonMethodUtils.formateNoScale(payAbleAmount);
    }
}