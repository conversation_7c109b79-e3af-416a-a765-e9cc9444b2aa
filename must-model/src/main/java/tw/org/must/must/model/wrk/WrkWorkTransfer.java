package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.util.Date;

@Table(name = "`wrk_work_transfer`")
public class WrkWorkTransfer extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 
     */
    @Column(name = "work_source_no")
    private Long workSourceNo;

    /**
     * 原始协会
     */
    @Column(name = "work_source_no_society")
    private Integer workSourceNoSociety;

    /**
     * 
     */
    @Column(name = "source_title_id")
    private Long sourceTitleId;

    /**
     * 
     */
    @Column(name = "work_dest_no")
    private Long workDestNo;

    /**
     * 
     */
    @Column(name = "work_dest_no_society")
    private Integer workDestNoSociety;

    /**
     * 
     */
    @Column(name = "dest_title_id")
    private Long destTitleId;

    /**
     * 
     */
    @Column(name = "process_date")
    private Date processDate;

    /**
     * T=transfered, W= waiting for transfer；U ：undo transfer
     */
    @Column(name = "status")
    private String status;

    /**
     * 
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 转换时间
     */
    @Column(name = "transfer_time")
    private Date transferTime;

    /**
     * 执行任务id
     */
    @Column(name = "task_id")
    private Long taskId;

    @Transient
    private String destTitle ;

    public Long getWorkSourceNo() {
        return workSourceNo;
    }

    public void setWorkSourceNo(Long workSourceNo) {
        this.workSourceNo = workSourceNo;
    }

    public Integer getWorkSourceNoSociety() {
        return workSourceNoSociety;
    }

    public void setWorkSourceNoSociety(Integer workSourceNoSociety) {
        this.workSourceNoSociety = workSourceNoSociety;
    }

    public Long getSourceTitleId() {
        return sourceTitleId;
    }

    public void setSourceTitleId(Long sourceTitleId) {
        this.sourceTitleId = sourceTitleId;
    }

    public Long getWorkDestNo() {
        return workDestNo;
    }

    public void setWorkDestNo(Long workDestNo) {
        this.workDestNo = workDestNo;
    }

    public Integer getWorkDestNoSociety() {
        return workDestNoSociety;
    }

    public void setWorkDestNoSociety(Integer workDestNoSociety) {
        this.workDestNoSociety = workDestNoSociety;
    }

    public Long getDestTitleId() {
        return destTitleId;
    }

    public void setDestTitleId(Long destTitleId) {
        this.destTitleId = destTitleId;
    }

    public Date getProcessDate() {
        return processDate;
    }

    public void setProcessDate(Date processDate) {
        this.processDate = processDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getTransferTime() {
        return transferTime;
    }

    public void setTransferTime(Date transferTime) {
        this.transferTime = transferTime;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getDestTitle() {
        return destTitle;
    }

    public void setDestTitle(String destTitle) {
        this.destTitle = destTitle;
    }

}