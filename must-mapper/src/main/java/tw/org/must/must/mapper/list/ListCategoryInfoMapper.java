package tw.org.must.must.mapper.list;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListCategoryInfo;
import tw.org.must.must.model.list.vo.ListCategoryInfoVo;

import java.util.List;

@Repository
public interface ListCategoryInfoMapper extends BaseMapper<ListCategoryInfo> {

    List<ListCategoryInfoVo> listCategoryInfoVo();
}