package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;

@Data
public class SY0503 extends BaseTsvEntity {
    private String SummaryRecordId;
    private String DistributionChannel;
    private String DistributionChannelDPID;
    private String CommercialModel;
    private String UseType;
    private String Territory;
    private String ServiceDescription;
    private String RightsController;
    private String RightsControllerPartyId;
    private String RightsType;
    private String TotalUsages;
    private String AllocatedUsages;
    private String MusicUsagePercentage;
    private String AllocatedNetRevenue;
    private String AllocatedRevenue;
    private String RightsControllerMarketShare;
    private String CurrencyOfReporting;
    private String CurrencyOfTransaction;
    private String ExchangeRate;
    private String SubscriberType;
    private String SubPeriodStartDate;
    private String SubPeriodEndDate;
    private String ContentCategory;
    private String RightsTypePercentage;
    private String ParentSummaryRecordId;
}
