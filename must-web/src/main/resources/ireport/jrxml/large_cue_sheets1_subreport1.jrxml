<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_cue_sheets1_subreport1" pageWidth="802" pageHeight="555" orientation="Landscape" whenNoDataType="BlankPage" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="70db5cb6-82a8-4f0c-bf7a-2238e76dd747">
	<property name="ireport.zoom" value="2.8531167061100073"/>
	<property name="ireport.x" value="879"/>
	<property name="ireport.y" value="0"/>
	<field name="type" class="java.lang.String"/>
	<field name="surnames" class="java.lang.String"/>
	<field name="IPNameNo" class="java.lang.String"/>
	<field name="performingSociety" class="java.lang.String"/>
	<field name="partShare1" class="java.lang.String"/>
	<field name="mechanical" class="java.lang.String"/>
	<field name="partShare2" class="java.lang.String"/>
	<detail>
		<band height="10">
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="758" y="0" width="1" height="10" uuid="524d2ec0-5940-472f-a247-ae14d7b8cd29"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="349" y="0" width="1" height="10" uuid="b991c2c7-2a28-4b69-9b92-c1c4d7cd4ea3"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="554" y="0" width="1" height="10" uuid="65f168e2-4635-4c1b-85e5-729e16008921"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="604" y="0" width="1" height="10" uuid="b81c908b-cc90-480a-878c-02f60f9bbb7e"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="656" y="0" width="1" height="10" uuid="f11c12af-6e21-4cd5-b64b-40eb358708a3"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="704" y="0" width="1" height="10" uuid="19937e05-eec2-40cd-9ce5-6e30632061ba"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="489" y="0" width="1" height="10" uuid="449aa330-1f42-403d-8a38-0e9a62cf6c11"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="316" y="0" width="1" height="10" uuid="1390b25e-1219-43d4-8728-d63f34dc0412"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="272" y="0" width="1" height="10" uuid="f2420609-32d5-4aef-8a4f-921da3f29e85"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="236" y="0" width="1" height="10" uuid="bce13533-07e4-48d9-8e34-2fd0f1ee0ecc"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line direction="BottomUp">
				<reportElement isPrintRepeatedValues="false" mode="Transparent" x="5" y="0" width="1" height="10" uuid="9f44df21-32d2-4f7a-8067-f3a0166e1583"/>
				<graphicElement>
					<pen lineWidth="0.1" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="320" y="0" width="17" height="10" uuid="d13afea3-e224-41a4-bf8d-f89045853be8"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{type}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="353" y="0" width="123" height="10" uuid="e95be8f9-4456-4769-b08a-15e4289f6161"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{surnames}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="494" y="0" width="55" height="10" uuid="b6a36742-6981-4d1a-bd47-148b7b3b8f21"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{IPNameNo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="559" y="0" width="45" height="10" uuid="4e32b60d-0ebd-40ab-a5c1-44b60fc8bb85"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{performingSociety}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="609" y="0" width="39" height="10" uuid="7e79a40e-e142-4758-9578-0cd3ab9cdea8"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partShare1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="660" y="0" width="33" height="10" uuid="b8cac9d8-d206-4aa3-ab26-489dd0cc7563"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mechanical}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="708" y="0" width="49" height="10" uuid="d8d5dbc4-f035-4c73-988b-9a15ec6acec5"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partShare2}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
