package tw.org.must.must.core.parse.spotify.tsv.entity;

import tw.org.must.must.core.parse.spotify.tsv.entity.BaseTsvEntity;

public class AS0202 extends BaseTsvEntity {


	
	private String BlockId;
	
	private String ResourceReference;
	
	private String DspResourceId;
	
	private String ISRC;
	
	private String Title;
	
	private String SubTitle;
	
	private String DisplayArtistName;
	
	private String DisplayArtistPartyId;
	
	private String Duration;

	private String durationH;
	
	private String durationM;
	
	private String durationS;
	
	private String ResourceType;
	
	private String IsMasterRecording;

	private String ISWC;

	private String ComposerAuthor;

	private String ComposerAuthorPartyId;

	private String Arranger;

	private String ArrangerPartyId;

	private String MusicPublisher;

	private String MusicPublisherPartyId;
	private String WorkContributor;

	private String WorkContributorPartyId;

	private String ProprietaryWorkId;


	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getResourceReference() {
		return ResourceReference;
	}

	public void setResourceReference(String resourceReference) {
		ResourceReference = resourceReference;
	}

	public String getDspResourceId() {
		return DspResourceId;
	}

	public void setDspResourceId(String dspResourceId) {
		DspResourceId = dspResourceId;
	}

	public String getISRC() {
		return ISRC;
	}

	public void setISRC(String iSRC) {
		ISRC = iSRC;
	}

	public String getTitle() {
		return Title;
	}

	public void setTitle(String title) {
		Title = title;
	}

	public String getSubTitle() {
		return SubTitle;
	}

	public void setSubTitle(String subTitle) {
		SubTitle = subTitle;
	}

	public String getDisplayArtistName() {
		return DisplayArtistName;
	}

	public void setDisplayArtistName(String displayArtistName) {
		DisplayArtistName = displayArtistName;
	}

	public String getDisplayArtistPartyId() {
		return DisplayArtistPartyId;
	}

	public void setDisplayArtistPartyId(String displayArtistPartyId) {
		DisplayArtistPartyId = displayArtistPartyId;
	}

	public String getDuration() {
		return Duration;
	}

	public void setDuration(String duration) {
		Duration = duration;
	}

	public String getDurationM() {
		return durationM;
	}

	public void setDurationM(String durationM) {
		this.durationM = durationM;
	}

	public String getDurationS() {
		return durationS;
	}

	public void setDurationS(String durationS) {
		this.durationS = durationS;
	}

	public String getResourceType() {
		return ResourceType;
	}

	public void setResourceType(String resourceType) {
		ResourceType = resourceType;
	}

	public String getIsMasterRecording() {
		return IsMasterRecording;
	}

	public void setIsMasterRecording(String isMasterRecording) {
		IsMasterRecording = isMasterRecording;
	}

	public String getISWC() {
		return ISWC;
	}

	public void setISWC(String ISWC) {
		this.ISWC = ISWC;
	}

	public String getComposerAuthor() {
		return ComposerAuthor;
	}

	public void setComposerAuthor(String composerAuthor) {
		ComposerAuthor = composerAuthor;
	}

	public String getComposerAuthorPartyId() {
		return ComposerAuthorPartyId;
	}

	public void setComposerAuthorPartyId(String composerAuthorPartyId) {
		ComposerAuthorPartyId = composerAuthorPartyId;
	}

	public String getArranger() {
		return Arranger;
	}

	public void setArranger(String arranger) {
		Arranger = arranger;
	}

	public String getArrangerPartyId() {
		return ArrangerPartyId;
	}

	public void setArrangerPartyId(String arrangerPartyId) {
		ArrangerPartyId = arrangerPartyId;
	}

	public String getMusicPublisher() {
		return MusicPublisher;
	}

	public void setMusicPublisher(String musicPublisher) {
		MusicPublisher = musicPublisher;
	}

	public String getMusicPublisherPartyId() {
		return MusicPublisherPartyId;
	}

	public void setMusicPublisherPartyId(String musicPublisherPartyId) {
		MusicPublisherPartyId = musicPublisherPartyId;
	}

	public String getWorkContributor() {
		return WorkContributor;
	}

	public void setWorkContributor(String workContributor) {
		WorkContributor = workContributor;
	}

	public String getWorkContributorPartyId() {
		return WorkContributorPartyId;
	}

	public void setWorkContributorPartyId(String workContributorPartyId) {
		WorkContributorPartyId = workContributorPartyId;
	}

	public String getProprietaryWorkId() {
		return ProprietaryWorkId;
	}

	public void setProprietaryWorkId(String proprietaryWorkId) {
		ProprietaryWorkId = proprietaryWorkId;
	}

	public String getDurationH() {
		return durationH;
	}

	public void setDurationH(String durationH) {
		this.durationH = durationH;
	}
}
