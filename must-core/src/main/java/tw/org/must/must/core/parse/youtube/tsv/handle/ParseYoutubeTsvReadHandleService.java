//package tw.org.must.must.core.parse.youtube.tsv.handle;
//
//import org.apache.commons.io.FileUtils;
//import org.apache.commons.io.LineIterator;
//import org.apache.commons.lang3.StringUtils;
//import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.TsvCommonObject;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class ParseYoutubeTsvReadHandleService extends ParseYoutubeTsvService {
//
//    private YoutubeTsvRowReadHandle youtubeTsvRowReadHandle;
//
//    public void setYoutubeTsvRowReadHandle(YoutubeTsvRowReadHandle youtubeTsvRowReadHandle){
//        this.youtubeTsvRowReadHandle= youtubeTsvRowReadHandle;
//    }
//
//    @Override
//    public void parse(String filepath) {
//
//        LineIterator lineIterator = null;
//        Map<String, String> headerMap = new HashMap<>();
//        List<String> lines = new ArrayList<>();
//        String result = "";
//        boolean isBreak = false;
//        //标志位，用于标识每一个AS01.01
//        Integer flag = 0;
//        int i = 0;
//        try {
//            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
//            while (lineIterator.hasNext()) {
//                i++;
//                String line = lineIterator.nextLine();
//                if (StringUtils.isBlank(line)) {
//                    continue;
//                }
//
//                logger.debug("========处理第{}行, line: {}", i, line);
//                flag = this.dealLines(line, headerMap, flag, lines);
//
//                // 每1000行入庫
//                if (tsvCommonObjectList.size() >= 1000) {
//
//                    if(StringUtils.isNotBlank(result)){
//                        logger.info("解析异常~"+result);
//
//                        tsvCommonObjectList.clear();
//                        isBreak = true;
//                        break;
//                    }
//
//
//
//                    if(youtubeTsvRowReadHandle !=null){
//                        for (TsvCommonObject tsvCommonObject : tsvCommonObjectList) {
//                            if(youtubeTsvRowReadHandle !=null){
//                                youtubeTsvRowReadHandle.handle(tsvCommonObject);
//                            }
//                        }
//
//
//                    }
//
//
//                    tsvCommonObjectList.clear();
//                }
//            }
//            // 最後剩餘數據入庫
//            if (tsvCommonObjectList.size() > 0) {
//
//                for (TsvCommonObject tsvCommonObject : tsvCommonObjectList) {
//                    if(youtubeTsvRowReadHandle !=null){
//                        youtubeTsvRowReadHandle.handle(tsvCommonObject);
//                    }
//                }
//
//                tsvCommonObjectList.clear();
//            }
//
//        } catch (Exception e) {
//            String message = e.getMessage();
//            logger.error("================== {}", message);
//            if (null != message && message.length() > 5000) {
//                message = message.substring(0, 4900);
//            }
//
//            e.printStackTrace();
//        } finally {
//            LineIterator.closeQuietly(lineIterator);
//        }
//    }
//}
