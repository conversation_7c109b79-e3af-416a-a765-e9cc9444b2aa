package tw.org.must.must.model.dist;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import tw.org.must.must.common.annotation.DynaEntity;
import tw.org.must.must.common.base.BaseEntity;

@DynaEntity
@Table(name = "`dist_param_category`")
public class DistParamCategory extends BaseEntity {

    /**
     */
    @Column(name = "`list_source_id`")
    private Long listSourceId;

    /**
     */
    @Column(name = "`list_source_name`")
    private String listSourceName;

    /**
     * 分配金额设置id
     */
    @Column(name = "`list_category_id`")
    private Long listCategoryId;

    /**
     * 分配清单名称
     */
    @Column(name = "`list_category_code`")
    private String listCategoryCode;

    /**
     * 分配数据开始时间，空则取dist_param_info中的时间
     */
    @Column(name = "`list_start_time`")
    private Date listStartTime;

    /**
     * 分配数据结束时间，开始结束时间只能全空或全不空
     */
    @Column(name = "`list_end_time`")
    private Date listEndTime;

    /**
     * A:自动拉取,M：手动拉取
     */
    @Column(name = "`type`")
    private String type;

    /**
     * 清单分配占比
     */
    @Column(name = "`radio`")
    private BigDecimal radio;



    /**
     * 总分配的钱
     */
    @Column(name = "`source_total_amount`")
    private BigDecimal sourceTotalAmount;


    /**
     * 保留款比例
     */
    @Column(name = "`retain_rate`")
    private BigDecimal retainRate;

    /**
     * 保留款金额
     */
    @Column(name = "`retain_amount`")
    private BigDecimal retainAmount;


    /**
     * 参与分配的金额
     */
    @Column(name = "`net_dist_amount`")
    private BigDecimal netDistAmount;

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    @Transient
    private Integer distStatus;


    public Long getListSourceId() {
        return listSourceId;
    }

    public void setListSourceId(Long listSourceId) {
        this.listSourceId = listSourceId;
    }

    public String getListSourceName() {
        return listSourceName;
    }

    public void setListSourceName(String listSourceName) {
        this.listSourceName = listSourceName;
    }

    public Long getListCategoryId() {
        return listCategoryId;
    }

    public void setListCategoryId(Long listCategoryId) {
        this.listCategoryId = listCategoryId;
    }

    public String getListCategoryCode() {
        return listCategoryCode;
    }

    public void setListCategoryCode(String listCategoryCode) {
        this.listCategoryCode = listCategoryCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getRadio() {
        return radio;
    }

    public void setRadio(BigDecimal radio) {
        this.radio = radio;
    }

    public BigDecimal getSourceTotalAmount() {
        return sourceTotalAmount;
    }

    public void setSourceTotalAmount(BigDecimal sourceTotalAmount) {
        this.sourceTotalAmount = sourceTotalAmount;
    }

    public BigDecimal getRetainRate() {
        return retainRate;
    }

    public void setRetainRate(BigDecimal retainRate) {
        this.retainRate = retainRate;
    }

    public BigDecimal getRetainAmount() {
        return retainAmount;
    }

    public void setRetainAmount(BigDecimal retainAmount) {
        this.retainAmount = retainAmount;
    }

    public BigDecimal getNetDistAmount() {
        return netDistAmount;
    }

    public void setNetDistAmount(BigDecimal netDistAmount) {
        this.netDistAmount = netDistAmount;
    }

    public String getDistNo() {
        return distNo;
    }

    public Date getListStartTime() {
        return listStartTime;
    }

    public void setListStartTime(Date listStartTime) {
        this.listStartTime = listStartTime;
    }

    public Date getListEndTime() {
        return listEndTime;
    }

    public void setListEndTime(Date listEndTime) {
        this.listEndTime = listEndTime;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public Integer getDistStatus() {
        return distStatus;
    }

    public void setDistStatus(Integer distStatus) {
        this.distStatus = distStatus;
    }
}