import com.firstbrave.api.parser.FileDecoder;
import com.firstbrave.api.parser.FileEncoder;
import com.firstbrave.api.ipi.vo.*;
import com.firstbrave.api.ipi.base.Record;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class TestIPIStringDecoder {

    FileEncoder encoder = new FileEncoder();
    FileDecoder decoder = new FileDecoder();

//    @Test
    public void testToTransmissionHeader() throws NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        String str = "HDRSO080      SUISA                                        01.00201906200016582019062020190619.IPI   ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);

    }


    @Test
    public void testToGroupHeader() throws IllegalAccessException, NoSuchMethodException, InstantiationException, InvocationTargetException {
        String str = "GRHMAU0000101.00                                                                                                                                                                                                  ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToTransaction() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "MAU000000000000000020190526094948001ACUM                I-002824178-1100563655723PAI-002824178-1100563655723PA   ";
        str = "IPA000000000000000020190116235717029SCD                 I-005105667-6100371782246PAI-005105667-6100371782246PA                                                                                                    ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToMembershipAgreement() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "MAO0000000000000001001DMLYBT2016052900000099991231235959201606011000020160601075044";
        //str = "MAN0000000000000003001DMLYBT2016052900000020190526235959201606011000020190526094948 ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToTerritory() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "TMA0000000000000002037610000101ISRAEL              10000101I  ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToBaseData() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "BDN0000000000000001N                                                                                                                     20190526194954                                                           ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToGroupTrailer() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "GRT000020000000100000063                                                                          ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToStatus() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "STN0000000000000002I-005105667-61201901162357179999123123595920190116235717                                                                                                                                       ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToNameMultiIpConnection() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "MCO000000000000000100697263104BLONDSTONE                                                                                PG2013010311151120130116120744I-003591694-8                                               ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToNameSingleIpConnection() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "NCN000000000000000100371766830AARON SUMMERNIGHT                                                                                                                      PP2019011620544520190116205445               ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }
    @Test
    public void testToNameSingleIpUsage() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "NUN000000000000000200371766830MWLY                                                                                                                                                                                ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToInheritedNameSingleIpUsage() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "INN000000000000000500371703765MWMC                                                                                                                                               ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToNameMultiIpUsage() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "MUN000000270000001100371710770MWMCI-005105115-9                                                                                                                                                                   ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToInheritedNameMultiIpUsage() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "IMN000000270000001100371710770MWMCI-005105115-9                                                                                                                                                                   ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }

    @Test
    public void testToNationality() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "NTN0000000000000001025010000101FRANCE              10000101        99991231                                                                                                                                       ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }
    @Test
    public void testToOtherNameConnection() throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        String str = "ONN000000000000000100371761257ABDULLA                                                                                   RAFICQ SHAIK                                 DF201901161820022019011618200200626212770    ";
        Record record = decoder.decode(str);
        System.out.println(str);
        String code = encoder.code(record);
        System.out.println(code);
    }



    //@Test
    public void finalTest() throws Exception {
        // \u000d	System.out.println("Comments in java");
        String file = "C:\\Users\\<USER>\\Documents\\Tencent Files\\757879296\\FileRecv\\IPI\\2019010246.IPI";
        file = "C:\\Users\\<USER>\\Desktop\\20190619.IPI";
        //file = "C:\\Users\\<USER>\\Desktop\\20190619.IPI";
        //file = "C:\\Users\\<USER>\\Desktop\\20190620.IPI";
        Path path = Paths.get(file);
        if(!path.toFile().exists()) return;
        List<String> list = Files.readAllLines(path);


        FileDecoder fileDecoder = new FileDecoder();
        Transmission decode = fileDecoder.decode(new LinkedList<>(list));

        List<String> ls = new ArrayList<>();
        ls.add(decode.getHeader().toString());
        decode.getGroups().stream().map(Group::toString).forEach(ls::add);
        ls.add(decode.getTrailer().toString());
        //collect.forEach(System.out::println);
        Files.write(Paths.get(file.concat(".DECODE")),ls);
    }
}
