package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.YoutubeLi0103Detail;

import java.util.List;


@Repository
public interface YoutubeLi0103DetailMapper extends BaseMapper<YoutubeLi0103Detail> {


    @Select("select * from list_dsp_file_data_mapping_yt_li0103_details_20240719 where id > #{id} order by id limit 100000 ")
    List<YoutubeLi0103Detail> getDetails(Long id);
}