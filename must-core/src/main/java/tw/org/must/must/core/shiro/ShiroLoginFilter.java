package tw.org.must.must.core.shiro;

import com.alibaba.fastjson.JSONObject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.http.HttpStatus;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

/**
 * shiro 自定义认证过滤器，认证失败，不跳转，返回JSON数据
 */
public class ShiroLoginFilter extends FormAuthenticationFilter {

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletResponse httpServletResponse = WebUtils.toHttp(response);
        httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json; charset=utf-8");
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msg","请登录！");
            httpServletResponse.getWriter().append(jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            httpServletResponse.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return false;
        }
        return false;
    }
}