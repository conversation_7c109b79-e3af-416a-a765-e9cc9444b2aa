package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistDistributionCheck;
import tw.org.must.must.model.ref.RefErrorCode;

import java.util.List;

@Repository
public interface DistDistributionCheckMapper extends BaseMapper<DistDistributionCheck> {


    List<DistDistributionCheck> selectWithDesc(String distNo, String errorCode, String errorType, String checked, Integer itemSoc);

    @Select("SELECT * FROM ref_error_code r inner join (SELECT DISTINCT error_code FROM dist_distribution_check where dist_no = #{distNo}) a ON r.err_code = a.error_code")
    List<RefErrorCode> getErrorCodeByDistNo(@Param(value = "distNo")String distNo);
}