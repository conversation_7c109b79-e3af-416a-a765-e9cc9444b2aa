package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`wrk_work_right`")
public class WrkWorkRight extends BaseEntity {

    /**
     * 作品id
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 作品协会编号
     */
    @Column(name = "`work_society`")
    private Integer workSociety;

    /**
     * 权利类型
     */
    @Column(name = "`right_type`")
    private String rightType;

    /**
     * 页面上是否勾选distribution
     */
    @Column(name = "`distributable`")
    private Integer distributable;

    /**
     * 页面上选择的是Auto还是Manual Auto:A;Manual:M
     */
    @Column(name = "`share_type`")
    private String shareType;

    /**
     * 当且仅当作品为改词改曲的时候(ARR、ADP)，如果是带出来的PER|MEC|ZYN则为SY,如果没有则为SS
     */
    @Column(name = "`syn_indicator`")
    private String synIndicator;

    /**
     * 有且仅当RightSD勾选的时候为Y
     */
    @Column(name = "`work_sd`")
    private String workSd;

    /**
     * 作品为改词改曲 标记为Y,否则N
     */
    @Column(name = "`work_dp`")
    private String workDp;

    /**
     * 作品唯一键
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSociety() {
        return workSociety;
    }

    public void setWorkSociety(Integer workSociety) {
        this.workSociety = workSociety;
    }

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public Integer getDistributable() {
        return distributable;
    }

    public void setDistributable(Integer distributable) {
        this.distributable = distributable;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getSynIndicator() {
        return synIndicator;
    }

    public void setSynIndicator(String synIndicator) {
        this.synIndicator = synIndicator;
    }

    public String getWorkSd() {
        return workSd;
    }

    public void setWorkSd(String workSd) {
        this.workSd = workSd;
    }

    public String getWorkDp() {
        return workDp;
    }

    public void setWorkDp(String workDp) {
        this.workDp = workDp;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }



}