<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DistAutoPay700_sub" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="192859b6-ace0-441f-903f-9f8c6288f0a0">
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="royaltiesType" class="java.lang.String"/>
	<field name="distAutoPayReceiptDetail" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="54" splitType="Stretch">
			<textField>
				<reportElement x="4" y="1" width="170" height="20" uuid="10a35a16-9301-489f-b9e3-5f47e417e175"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{royaltiesType}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="10" y="25" width="572" height="24" uuid="773af15f-ffab-4daf-ab20-3c02569d9651"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distAutoPayReceiptDetail")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"distAutoPays/DistAutoPay700_child.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
