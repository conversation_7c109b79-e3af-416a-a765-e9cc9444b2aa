package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`dist_media_version`")
public class DistMediaVersion extends BaseEntity {

    /**
     * db right分配版本
     */
    @Column(name = "`version_name`")
    private String versionName;

    /**
     * 
     */
    @Column(name = "`dist_type`")
    private String distType;

    /**
     * 
     */
    @Column(name = "`remark`")
    private String remark;

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }



}