package tw.org.must.must.model.dist.vo;

import lombok.Data;
import tw.org.must.must.model.dist.DistAutopayMemberRetain;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2021/3/29 14:38
 * @Version:1.0
 * @Description:
 */
@Data
public class IncomeMemberRetainVo {
    List<DistAutopayMemberRetain> distAutopayMemberRetainList;

    /**1
     * 支付金额
     */
    private BigDecimal payAmount;

    /**1
     * 本地收款金额  正数
     */
    private BigDecimal localTaxableIncome;

    /**1
     * 海外收款金额  正数
     */
    private BigDecimal overseasTaxableIncome;

    /**1
     * 废弃---所得税
     */
    private BigDecimal withheldTax;

    /**1
     * 管理费  负数
     */
    private BigDecimal commissionAmount;

    /**1
     * --废弃，扣除费用，deduction
     */
    private BigDecimal nonTaxableIncome;


    /**1
     * 企业税额
     */
    private BigDecimal salesTaxAmount;

    /**1
     * 预付款，正数
     */
    private BigDecimal advance;

    private BigDecimal royaltyAmount;

    private BigDecimal deduction;
}