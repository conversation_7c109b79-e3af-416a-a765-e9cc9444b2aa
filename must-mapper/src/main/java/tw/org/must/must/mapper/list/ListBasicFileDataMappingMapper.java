package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListBasicFileDataMapping;

import java.util.List;

@Repository
public interface ListBasicFileDataMappingMapper extends BaseMapper<ListBasicFileDataMapping> {


    List<ListBasicFileDataMapping> getRepeatDateUniqueKeyByFileBaseId(@Param("fileBaseId") Long fileBaseId);

    Long getMaxIdByFileBaseId(@Param("fileBaseId") Long fileBaseId);
}