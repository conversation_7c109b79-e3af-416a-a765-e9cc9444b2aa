package com.firstbrave.api.ccidv14.vo;

import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.base.ApiContentType;

public class InvoiceDetailRecordV14 extends CCIDRecordPrefix {

    /**
     * ORI,ADJ,COR etc.
     */
    @ApiContentType(index=2,length = 3)
	private String transactionType;

    @ApiContentType(index=3,length = 20)
    private String refId;
 
    @ApiContentType(index=4,length = 20)
    private String correctionReference;

    @ApiContentType(index=5,length = 60)
    private String salesTransactionId;

    @ApiContentType(index=6,length = 60)
    private String workId;

    @ApiContentType(index=7,length = 60)
    private String releaseId;

    @ApiContentType(index=8,length = 60)
    private String resourceId;

    @ApiContentType(index=9,length = 12)
    private String isrc;

    @ApiContentType(index=10,length = 11)
    private String iswc;

    @ApiContentType(index=11,length = 39)
    private String workcode;
    
    @ApiContentType(index=12,length = 60)
    private String workTitle;
  
    @ApiContentType(index=13,length = 10)
    private String useQuantity;
    
    @ApiContentType(index=14,length = 10)
    private String appliedTariff;
    
    @ApiContentType(index=15,length = 2)
    private String royaltyType;

    @ApiContentType(index=16,length = 30)
    private String revenueBasis;
    
    @ApiContentType(index=17,length = 30)
    private String originalReleaseRevenueBasis;

    @ApiContentType(index=18,length = 30)
    private String originalResourceRevenueBasis;

    @ApiContentType(index=19,length = 30)
    private String royality;

    @ApiContentType(index=20,length = 6)
    private String resourceShare;
    
    @ApiContentType(index=21,length = 10)
    private String restrictions;
	
    @ApiContentType(index=22,length = 6)
    private String claimLicensorCombined;

    @ApiContentType(index=23,length = 6)
    private String claimCopconCombined;

    @ApiContentType(index=24,length = 6)
    private String claimUnmatchCombined;

    @ApiContentType(index=25,length = 6)
    private String claimPdCombined;

    @ApiContentType(index=26,length = 6)
    private String claimNotCollectedCombined;

    @ApiContentType(index=27,length = 30)
    private String amountInvoicedTotal;

    @ApiContentType(index=28,length = 6)
    private String claimLicensorMech;

    @ApiContentType(index=29,length = 6)
    private String claimLicensorPerf;

    @ApiContentType(index=30,length = 30)
    private String amountLicensorMech;

    @ApiContentType(index=31,length = 30)
    private String amountLicensorPerf;
    
    @ApiContentType(index=32,length = 30)
    private String amountCopconMech;
    
    @ApiContentType(index=33,length = 30)
    private String amountCopconPerf;
    
    @ApiContentType(index=34,length = 30)
    private String amountPdMech;
    
    @ApiContentType(index=35,length = 30)
    private String amountPdPerf;
    
    @ApiContentType(index=36,length = 30)
    private String amountNotCollectedMech;
    
    @ApiContentType(index=37,length = 30)
    private String amountNotCollectedPerf;
    
    @ApiContentType(index=38,length = 30)
    private String amountUnmatchedMech;
    
    @ApiContentType(index=39,length = 30)
    private String amountUnmatchedPerf;
    
    @ApiContentType(index=40,length = 6)
    private String claimCopconMech;
    
    @ApiContentType(index=41,length = 6)
    private String claimCopconPerf;
    
    @ApiContentType(index=42,length = 6)
    private String claimPdMech;
    
    @ApiContentType(index=43,length = 6)
    private String claimPdPerf;
    
    @ApiContentType(index=44,length = 6)
    private String claimNotCollectedMech;
    
    @ApiContentType(index=45,length = 6)
    private String claimNotCollectedrPerf;
    
    @ApiContentType(index=46,length = 6)
    private String claimUnmatchedMech;
    
    @ApiContentType(index=47,length = 6)
    private String claimUnmatchedPerf;

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getCorrectionReference() {
		return correctionReference;
	}

	public void setCorrectionReference(String correctionReference) {
		this.correctionReference = correctionReference;
	}

	public String getSalesTransactionId() {
		return salesTransactionId;
	}

	public void setSalesTransactionId(String salesTransactionId) {
		this.salesTransactionId = salesTransactionId;
	}

	public String getWorkId() {
		return workId;
	}

	public void setWorkId(String workId) {
		this.workId = workId;
	}

	public String getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(String releaseId) {
		this.releaseId = releaseId;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId;
	}

	public String getIsrc() {
		return isrc;
	}

	public void setIsrc(String isrc) {
		this.isrc = isrc;
	}

	public String getIswc() {
		return iswc;
	}

	public void setIswc(String iswc) {
		this.iswc = iswc;
	}

	public String getWorkcode() {
		return workcode;
	}

	public void setWorkcode(String workcode) {
		this.workcode = workcode;
	}

	public String getWorkTitle() {
		return workTitle;
	}

	public void setWorkTitle(String workTitle) {
		this.workTitle = workTitle;
	}

	public String getUseQuantity() {
		return useQuantity;
	}

	public void setUseQuantity(String useQuantity) {
		this.useQuantity = useQuantity;
	}

	public String getAppliedTariff() {
		return appliedTariff;
	}

	public void setAppliedTariff(String appliedTariff) {
		this.appliedTariff = appliedTariff;
	}

	public String getRoyaltyType() {
		return royaltyType;
	}

	public void setRoyaltyType(String royaltyType) {
		this.royaltyType = royaltyType;
	}

	public String getRevenueBasis() {
		return revenueBasis;
	}

	public void setRevenueBasis(String revenueBasis) {
		this.revenueBasis = revenueBasis;
	}

	public String getOriginalReleaseRevenueBasis() {
		return originalReleaseRevenueBasis;
	}

	public void setOriginalReleaseRevenueBasis(String originalReleaseRevenueBasis) {
		this.originalReleaseRevenueBasis = originalReleaseRevenueBasis;
	}

	public String getOriginalResourceRevenueBasis() {
		return originalResourceRevenueBasis;
	}

	public void setOriginalResourceRevenueBasis(String originalResourceRevenueBasis) {
		this.originalResourceRevenueBasis = originalResourceRevenueBasis;
	}

	public String getRoyality() {
		return royality;
	}

	public void setRoyality(String royality) {
		this.royality = royality;
	}

	public String getResourceShare() {
		return resourceShare;
	}

	public void setResourceShare(String resourceShare) {
		this.resourceShare = resourceShare;
	}

	public String getRestrictions() {
		return restrictions;
	}

	public void setRestrictions(String restrictions) {
		this.restrictions = restrictions;
	}

	public String getClaimLicensorCombined() {
		return claimLicensorCombined;
	}

	public void setClaimLicensorCombined(String claimLicensorCombined) {
		this.claimLicensorCombined = claimLicensorCombined;
	}

	public String getClaimCopconCombined() {
		return claimCopconCombined;
	}

	public void setClaimCopconCombined(String claimCopconCombined) {
		this.claimCopconCombined = claimCopconCombined;
	}

	public String getClaimUnmatchCombined() {
		return claimUnmatchCombined;
	}

	public void setClaimUnmatchCombined(String claimUnmatchCombined) {
		this.claimUnmatchCombined = claimUnmatchCombined;
	}

	public String getClaimPdCombined() {
		return claimPdCombined;
	}

	public void setClaimPdCombined(String claimPdCombined) {
		this.claimPdCombined = claimPdCombined;
	}

	public String getClaimNotCollectedCombined() {
		return claimNotCollectedCombined;
	}

	public void setClaimNotCollectedCombined(String claimNotCollectedCombined) {
		this.claimNotCollectedCombined = claimNotCollectedCombined;
	}

	public String getAmountInvoicedTotal() {
		return amountInvoicedTotal;
	}

	public void setAmountInvoicedTotal(String amountInvoicedTotal) {
		this.amountInvoicedTotal = amountInvoicedTotal;
	}

	public String getClaimLicensorMech() {
		return claimLicensorMech;
	}

	public void setClaimLicensorMech(String claimLicensorMech) {
		this.claimLicensorMech = claimLicensorMech;
	}

	public String getClaimLicensorPerf() {
		return claimLicensorPerf;
	}

	public void setClaimLicensorPerf(String claimLicensorPerf) {
		this.claimLicensorPerf = claimLicensorPerf;
	}

	public String getAmountLicensorMech() {
		return amountLicensorMech;
	}

	public void setAmountLicensorMech(String amountLicensorMech) {
		this.amountLicensorMech = amountLicensorMech;
	}

	public String getAmountLicensorPerf() {
		return amountLicensorPerf;
	}

	public void setAmountLicensorPerf(String amountLicensorPerf) {
		this.amountLicensorPerf = amountLicensorPerf;
	}

	public String getAmountCopconMech() {
		return amountCopconMech;
	}

	public void setAmountCopconMech(String amountCopconMech) {
		this.amountCopconMech = amountCopconMech;
	}

	public String getAmountCopconPerf() {
		return amountCopconPerf;
	}

	public void setAmountCopconPerf(String amountCopconPerf) {
		this.amountCopconPerf = amountCopconPerf;
	}

	public String getAmountPdMech() {
		return amountPdMech;
	}

	public void setAmountPdMech(String amountPdMech) {
		this.amountPdMech = amountPdMech;
	}

	public String getAmountPdPerf() {
		return amountPdPerf;
	}

	public void setAmountPdPerf(String amountPdPerf) {
		this.amountPdPerf = amountPdPerf;
	}

	public String getAmountNotCollectedMech() {
		return amountNotCollectedMech;
	}

	public void setAmountNotCollectedMech(String amountNotCollectedMech) {
		this.amountNotCollectedMech = amountNotCollectedMech;
	}

	public String getAmountNotCollectedPerf() {
		return amountNotCollectedPerf;
	}

	public void setAmountNotCollectedPerf(String amountNotCollectedPerf) {
		this.amountNotCollectedPerf = amountNotCollectedPerf;
	}

	public String getAmountUnmatchedMech() {
		return amountUnmatchedMech;
	}

	public void setAmountUnmatchedMech(String amountUnmatchedMech) {
		this.amountUnmatchedMech = amountUnmatchedMech;
	}

	public String getAmountUnmatchedPerf() {
		return amountUnmatchedPerf;
	}

	public void setAmountUnmatchedPerf(String amountUnmatchedPerf) {
		this.amountUnmatchedPerf = amountUnmatchedPerf;
	}

	public String getClaimCopconMech() {
		return claimCopconMech;
	}

	public void setClaimCopconMech(String claimCopconMech) {
		this.claimCopconMech = claimCopconMech;
	}

	public String getClaimCopconPerf() {
		return claimCopconPerf;
	}

	public void setClaimCopconPerf(String claimCopconPerf) {
		this.claimCopconPerf = claimCopconPerf;
	}

	public String getClaimPdMech() {
		return claimPdMech;
	}

	public void setClaimPdMech(String claimPdMech) {
		this.claimPdMech = claimPdMech;
	}

	public String getClaimPdPerf() {
		return claimPdPerf;
	}

	public void setClaimPdPerf(String claimPdPerf) {
		this.claimPdPerf = claimPdPerf;
	}

	public String getClaimNotCollectedMech() {
		return claimNotCollectedMech;
	}

	public void setClaimNotCollectedMech(String claimNotCollectedMech) {
		this.claimNotCollectedMech = claimNotCollectedMech;
	}

	public String getClaimNotCollectedrPerf() {
		return claimNotCollectedrPerf;
	}

	public void setClaimNotCollectedrPerf(String claimNotCollectedrPerf) {
		this.claimNotCollectedrPerf = claimNotCollectedrPerf;
	}

	public String getClaimUnmatchedMech() {
		return claimUnmatchedMech;
	}

	public void setClaimUnmatchedMech(String claimUnmatchedMech) {
		this.claimUnmatchedMech = claimUnmatchedMech;
	}

	public String getClaimUnmatchedPerf() {
		return claimUnmatchedPerf;
	}

	public void setClaimUnmatchedPerf(String claimUnmatchedPerf) {
		this.claimUnmatchedPerf = claimUnmatchedPerf;
	}

	@Override
	public String toString() {
		return "InvoiceDetailRecord [transactionType=" + transactionType + ", refId=" + refId + ", correctionReference="
				+ correctionReference + ", salesTransactionId=" + salesTransactionId + ", workId=" + workId
				+ ", releaseId=" + releaseId + ", resourceId=" + resourceId + ", isrc=" + isrc + ", iswc=" + iswc
				+ ", workcode=" + workcode + ", workTitle=" + workTitle + ", useQuantity=" + useQuantity
				+ ", appliedTariff=" + appliedTariff + ", royaltyType=" + royaltyType + ", revenueBasis=" + revenueBasis
				+ ", originalReleaseRevenueBasis=" + originalReleaseRevenueBasis + ", originalResourceRevenueBasis="
				+ originalResourceRevenueBasis + ", royality=" + royality + ", resourceShare=" + resourceShare
				+ ", restrictions=" + restrictions + ", claimLicensorCombined=" + claimLicensorCombined
				+ ", claimCopconCombined=" + claimCopconCombined + ", claimUnmatchCombined=" + claimUnmatchCombined
				+ ", claimPdCombined=" + claimPdCombined + ", claimNotCollectedCombined=" + claimNotCollectedCombined
				+ ", amountInvoicedTotal=" + amountInvoicedTotal + ", claimLicensorMech=" + claimLicensorMech
				+ ", claimLicensorPerf=" + claimLicensorPerf + ", amountLicensorMech=" + amountLicensorMech
				+ ", amountLicensorPerf=" + amountLicensorPerf + ", amountCopconMech=" + amountCopconMech
				+ ", amountCopconPerf=" + amountCopconPerf + ", amountPdMech=" + amountPdMech + ", amountPdPerf="
				+ amountPdPerf + ", amountNotCollectedMech=" + amountNotCollectedMech + ", amountNotCollectedPerf="
				+ amountNotCollectedPerf + ", amountUnmatchedMech=" + amountUnmatchedMech + ", amountUnmatchedPerf="
				+ amountUnmatchedPerf + ", claimCopconMech=" + claimCopconMech + ", claimCopconPerf=" + claimCopconPerf
				+ ", claimPdMech=" + claimPdMech + ", claimPdPerf=" + claimPdPerf + ", claimNotCollectedMech="
				+ claimNotCollectedMech + ", claimNotCollectedrPerf=" + claimNotCollectedrPerf + ", claimUnmatchedMech="
				+ claimUnmatchedMech + ", claimUnmatchedPerf=" + claimUnmatchedPerf + ", recordType=" + recordType
				+ "]";
	}
	
}
