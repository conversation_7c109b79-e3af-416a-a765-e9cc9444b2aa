package tw.org.must.must.model.dist.vo.v750;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import tw.org.must.must.common.json.AlexBigDecimalSerializer;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @Author: hanDa
 * @Date: 2021/5/20 16:15
 * @Version:1.0
 * @Description:
 */
@Data
public class SummaryTotal750 {
    private String distNo;
    @JsonSerialize(using = AlexBigDecimalSerializer.class)
    private BigDecimal totalAmount;
    private int negative = 1;

    public SummaryTotal750() {
    }


    public String getTotalAmount()
    {
        DecimalFormat df = new DecimalFormat("#,##0.00");
        BigDecimal scaledAmount = totalAmount.setScale(2, RoundingMode.HALF_UP).abs();
        String formatted = df.format(scaledAmount);
        if (totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            return "-$" + formatted;
        } else {
            return "$" + formatted;
        }
    }

    public SummaryTotal750(String distNo, BigDecimal totalAmount) {
        this.distNo = distNo;
        this.totalAmount = totalAmount;
        this.negative = totalAmount.compareTo(BigDecimal.ZERO) < 0 ? 0 : 1;
    }
}