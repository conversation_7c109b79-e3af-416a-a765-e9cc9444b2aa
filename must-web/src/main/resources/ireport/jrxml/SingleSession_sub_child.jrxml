<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="workSocietyCode" pageWidth="595" pageHeight="842" whenNoDataType="BlankPage" columnWidth="555"
              leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" isFloatColumnFooter="true"
              uuid="22aa7901-06ab-4a82-995e-e384762d3725">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <property name="com.jaspersoft.studio.unit." value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
    <subDataset name="Dataset1" uuid="e6c501f5-2b12-433f-8e17-7115b97aba47">
        <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
        <queryString>
            <![CDATA[]]>
        </queryString>
    </subDataset>
    <parameter name="SUBREPORT_DIR" class="java.lang.String">
        <defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
    </parameter>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="name" class="java.lang.String"/>
    <field name="socName" class="java.lang.String"/>
    <field name="role" class="java.lang.String"/>
    <field name="companies" class="java.lang.Object"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <detail>
        <band height="29" splitType="Stretch">
            <elementGroup>
                <textField isStretchWithOverflow="true" evaluationTime="Band" isBlankWhenNull="true">
                    <reportElement x="0" y="0" width="150" height="28" uuid="0e2741b4-ca17-4ad8-ba3c-5a410fc556f4"/>
                    <textElement>
                        <font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
                        <paragraph lineSpacing="Proportional"/>
                    </textElement>
                    <textFieldExpression><![CDATA[$F{name}+"("+$F{role}+")"]]></textFieldExpression>
                </textField>
                <subreport>
                    <reportElement x="230" y="0" width="345" height="28" uuid="69156c01-1859-47c4-ad82-176aa1dc9e9b"/>
                    <dataSourceExpression>
                        <![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("companies")]]></dataSourceExpression>
                    <subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"Lyricist_company.jasper"]]></subreportExpression>
                </subreport>
                <textField isStretchWithOverflow="true" isBlankWhenNull="true">
                    <reportElement x="150" y="0" width="60" height="28" uuid="13e8252a-fe72-4586-a34a-4650a8c6be42"/>
                    <textElement>
                        <font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
                    </textElement>
                    <textFieldExpression><![CDATA[ $F{socName} == null || $F{socName}.equals("") ? "" : "("+$F{socName}+")"]]></textFieldExpression>
                </textField>
            </elementGroup>
        </band>
    </detail>
</jasperReport>
