package com.firstbrave.api.ccidv14.spotify.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.ccid.base.spotify.CCIDRecordPrefixSpotify;

public class InvoiceDetailRecordV14 extends CCIDRecordPrefixSpotify {

	@ApiContentType(index=1,length = 60)
	private String workTitle;

	@ApiContentType(index=2,length = 60)
	private String resourceId;

	@ApiContentType(index=3,length = 12)
	private String isrc;

	@ApiContentType(index=4,length = 11)
	private String iswc;

	@ApiContentType(index=5,length = 10)
	private String useQuantity;

	@ApiContentType(index=6,length = 6)
	private String claimLicensorMech;

	@ApiContentType(index=7,length = 6)
	private String claimLicensorPerf;

	@ApiContentType(index=8,length = 6)
	private String claimLicensorCombined;

	@ApiContentType(index=9,length = 30)
	private String amountInvoicedMech;

	@ApiContentType(index=10,length = 30)
	private String amountInvoicedPerf;

	@ApiContentType(index=11,length = 30)
	private String amountInvoicedTotal;

	@ApiContentType(index=12,length = 18)
	private String tradingBrand;

	public String getWorkTitle() {
		return workTitle;
	}

	public void setWorkTitle(String workTitle) {
		this.workTitle = workTitle;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId;
	}

	public String getIsrc() {
		return isrc;
	}

	public void setIsrc(String isrc) {
		this.isrc = isrc;
	}

	public String getIswc() {
		return iswc;
	}

	public void setIswc(String iswc) {
		this.iswc = iswc;
	}

	public String getUseQuantity() {
		return useQuantity;
	}

	public void setUseQuantity(String useQuantity) {
		this.useQuantity = useQuantity;
	}

	public String getClaimLicensorMech() {
		return claimLicensorMech;
	}

	public void setClaimLicensorMech(String claimLicensorMech) {
		this.claimLicensorMech = claimLicensorMech;
	}

	public String getClaimLicensorPerf() {
		return claimLicensorPerf;
	}

	public void setClaimLicensorPerf(String claimLicensorPerf) {
		this.claimLicensorPerf = claimLicensorPerf;
	}

	public String getClaimLicensorCombined() {
		return claimLicensorCombined;
	}

	public void setClaimLicensorCombined(String claimLicensorCombined) {
		this.claimLicensorCombined = claimLicensorCombined;
	}

	public String getAmountInvoicedMech() {
		return amountInvoicedMech;
	}

	public void setAmountInvoicedMech(String amountInvoicedMech) {
		this.amountInvoicedMech = amountInvoicedMech;
	}

	public String getAmountInvoicedPerf() {
		return amountInvoicedPerf;
	}

	public void setAmountInvoicedPerf(String amountInvoicedPerf) {
		this.amountInvoicedPerf = amountInvoicedPerf;
	}

	public String getAmountInvoicedTotal() {
		return amountInvoicedTotal;
	}

	public void setAmountInvoicedTotal(String amountInvoicedTotal) {
		this.amountInvoicedTotal = amountInvoicedTotal;
	}

	public String getTradingBrand() {
		return tradingBrand;
	}

	public void setTradingBrand(String tradingBrand) {
		this.tradingBrand = tradingBrand;
	}

	@Override
	public String toString() {
		return "InvoiceDetailRecord [workTitle=" + workTitle + ", resourceId=" + resourceId+ ", isrc=" + isrc+ ", iswc=" + iswc
				+ ", useQuantity=" + useQuantity+ ", claimLicensorMech=" + claimLicensorMech+ ", claimLicensorPerf=" + claimLicensorPerf+ ", claimLicensorCombined=" + claimLicensorCombined
				+ ", amountInvoicedMech=" + amountInvoicedMech+ ", amountInvoicedPerf=" + amountInvoicedPerf+ ", amountInvoicedTotal=" + amountInvoicedTotal
				+ "]";
	}
	
}
