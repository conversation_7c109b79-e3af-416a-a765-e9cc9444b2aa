package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimResultCcid;

import java.util.List;

@Repository
public interface ClaimResultCcidMapper extends BaseMapper<ClaimResultCcid> {


    List<ClaimResultCcid> findClaimResultCcidGroupByParams();

    List<Long> findFileMappingIdsByHeaderIdAndNotClaimFlag(@Param("headerId") Long headerId, @Param("claimFlag") String claimFlag);

    void updateTest(@Param("ids") List<Long> ids);

    List<ClaimResultCcid> getTest(@Param("id")Long id);

    List<ClaimResultCcid> selectByHeaderId(@Param("headerId")Long headerId);

    @Select("select max(id) from claim_result_ccid where header_id = #{headerId}")
    Long getMaxId(Long headerId);

    @Select("SELECT file_mapping_id FROM  (" +
            "SELECT file_mapping_id FROM claim_result_ccid where header_id = #{headerId} group by file_mapping_id " +
            ") as ta WHERE NOT EXISTS (SELECT 1 from list_match_data_dsp_done tb WHERE ta.file_mapping_id = tb.file_mapping_id  )")
    List<Long> getFileMappingIdForDelete(Long headerId);

    Integer deleteAll();

}