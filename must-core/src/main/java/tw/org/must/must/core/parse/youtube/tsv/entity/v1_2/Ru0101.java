package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class Ru0101 extends BaseTsvEntity {


	
	private String BlockId;
	
	private String SummaryRecordId;
	
	private String DspReleaseId;
	
	private String Usages;
	
	private String ContentCategory;



	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getDspReleaseId() {
		return DspReleaseId;
	}

	public void setDspReleaseId(String dspReleaseId) {
		DspReleaseId = dspReleaseId;
	}

	public String getUsages() {
		return Usages;
	}

	public void setUsages(String usages) {
		Usages = usages;
	}

	public String getContentCategory() {
		return ContentCategory;
	}

	public void setContentCategory(String contentCategory) {
		ContentCategory = contentCategory;
	}
	
}
