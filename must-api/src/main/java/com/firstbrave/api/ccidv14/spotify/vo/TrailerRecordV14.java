package com.firstbrave.api.ccidv14.spotify.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.ccid.base.spotify.CCIDRecordPrefixSpotify;

public class TrailerRecordV14 extends CCIDRecordPrefixSpotify {

    
    @ApiContentType(index=1,length = 30)
    private String totalAmountLicensor;

	public String getTotalAmountLicensor() {
		return totalAmountLicensor;
	}

	public void setTotalAmountLicensor(String totalAmountLicensor) {
		this.totalAmountLicensor = totalAmountLicensor;
	}

	@Override
	public String toString() {
		return "TrailerRecord [totalAmountLicensor=" + totalAmountLicensor +  "]";
	}

}
