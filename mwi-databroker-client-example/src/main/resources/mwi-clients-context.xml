<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	<import resource="classpath:common-mwi-query-utils-context.xml" />
		
	<bean id="mwiDataBrokerServiceFactory" parent="defaultServiceFactory">
		<property name="serviceClass" value="net.fasttrackdcn.mwi.databroker.MwiDataBrokerService" />
	</bean>
	
	<bean id="mwiCombinedViewServiceFactory" parent="defaultServiceFactory">
		<property name="serviceClass" value="net.fasttrackdcn.mwi.combinedview.MwiCombinedViewService" />
	</bean>
		
</beans>