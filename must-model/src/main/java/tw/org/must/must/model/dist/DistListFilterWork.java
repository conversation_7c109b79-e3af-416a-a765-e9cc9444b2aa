package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`dist_list_filter_work`")
public class DistListFilterWork extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`list_source_code`")
    private String listSourceCode;

    /**
     * 
     */
    @Column(name = "`list_category_code`")
    private String listCategoryCode;

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 作品权利类别，PER、MEC ...
     */
    @Column(name = "`work_right_type`")
    private String workRightType;

    /**
     * 作品id
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 作品唯一值 "soc代号-work_id"
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    /**
     * 
     */
    @Column(name = "`work_title`")
    private String workTitle;

    /**
     * 
     */
    @Column(name = "`work_artists`")
    private String workArtists;

    /**
     * 作曲，多个用;分隔
     */
    @Column(name = "`work_composers`")
    private String workComposers;

    /**
     * 作词，多个用;分隔
     */
    @Column(name = "`work_authors`")
    private String workAuthors;

    /**
     * 
     */
    @Column(name = "`create_user_id`")
    private Long createUserId;

    /**
     * 
     */
    @Column(name = "`create_user_name`")
    private String createUserName;

    /**
     * 
     */
    @Column(name = "`is_include`")
    private String isInclude;

    /**
     * 来源分配编号
     */
    @Column(name = "`from_dist_no`")
    private String fromDistNo;

    public String getListSourceCode() {
        return listSourceCode;
    }

    public void setListSourceCode(String listSourceCode) {
        this.listSourceCode = listSourceCode;
    }

    public String getListCategoryCode() {
        return listCategoryCode;
    }

    public void setListCategoryCode(String listCategoryCode) {
        this.listCategoryCode = listCategoryCode;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getWorkRightType() {
        return workRightType;
    }

    public void setWorkRightType(String workRightType) {
        this.workRightType = workRightType;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getWorkTitle() {
        return workTitle;
    }

    public void setWorkTitle(String workTitle) {
        this.workTitle = workTitle;
    }

    public String getWorkArtists() {
        return workArtists;
    }

    public void setWorkArtists(String workArtists) {
        this.workArtists = workArtists;
    }

    public String getWorkComposers() {
        return workComposers;
    }

    public void setWorkComposers(String workComposers) {
        this.workComposers = workComposers;
    }

    public String getWorkAuthors() {
        return workAuthors;
    }

    public void setWorkAuthors(String workAuthors) {
        this.workAuthors = workAuthors;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getIsInclude() {
        return isInclude;
    }

    public void setIsInclude(String isInclude) {
        this.isInclude = isInclude;
    }

    public String getFromDistNo() {
        return fromDistNo;
    }

    public void setFromDistNo(String fromDistNo) {
        this.fromDistNo = fromDistNo;
    }
}