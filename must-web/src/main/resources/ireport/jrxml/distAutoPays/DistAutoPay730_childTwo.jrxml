<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="730_child" pageWidth="800" pageHeight="842" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="37719497-d533-4a83-9713-8223b11d6df6">
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="distNo" class="java.lang.String"/>
	<field name="royalAmount" class="java.lang.String"/>
	<field name="royalDescribe" class="java.lang.String"/>
	<field name="distAutoPayDetail" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="50" height="20" uuid="f7d6b4c9-aa1a-4113-8be9-7c673717ae3f"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[DIST. NO.]]></text>
			</staticText>
			<textField>
				<reportElement x="50" y="0" width="40" height="20" uuid="5c2a8f3f-2da9-462e-a2bf-13e3fb9d07eb"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="90" y="0" width="660" height="20" uuid="2a977e66-e535-43f8-80a8-3db4da1527cf"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distAutoPayDetail")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"distAutoPays/DistAutoPay730_childThird.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
