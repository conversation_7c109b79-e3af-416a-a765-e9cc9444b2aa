package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalAgrContent;

@Repository
public interface OrcalAgrContentMapper {

	List<OrcalAgrContent> getAgrContentList(String lastAgrAmendTime);

    OrcalAgrContent getAgrContent(String agrNo);

    List<OrcalAgrContent> selectByDateByRowNum(String lastAgrAmendTime, int start, int end);

    List<OrcalAgrContent> selectPrimaryKey(int start, int end) ;

    List<OrcalAgrContent> selectOrcalAgrContentByArgNos(List<String> agrNoList);
}
