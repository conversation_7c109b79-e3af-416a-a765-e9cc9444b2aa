package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_autopay_net_pay_member`")
public class DistAutopayNetPayMember extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`ip_base_no`")
    private String ipBaseNo;

    /**
     * 
     */
    @Column(name = "`pa_name_no`")
    private String paNameNo;

    /**
     * 支付金额
     */
    @Column(name = "`pay_amount`")
    private BigDecimal payAmount;

    /**
     * N or L
     */
    @Column(name = "`ip_type`")
    private String ipType;

    /**
     * 
     */
    @Column(name = "`retain_code`")
    private String retainCode;

    /**
     * 本地收款金额  正数
     */
    @Column(name = "`local_taxable_income`")
    private BigDecimal localTaxableIncome;

    /**
     * 海外收款金额  正数
     */
    @Column(name = "`overseas_taxable_income`")
    private BigDecimal overseasTaxableIncome;

    /**
     * 税年度
     */
    @Column(name = "`tax_year`")
    private Integer taxYear;

    /**
     * 废弃---所得税
     */
    @Column(name = "`withheld_tax`")
    private BigDecimal withheldTax;

    /**
     * 管理费  负数
     */
    @Column(name = "`commission_amount`")
    private BigDecimal commissionAmount;

    /**
     * 支付日期
     */
    @Column(name = "`pay_date`")
    private Date payDate;

    /**
     * 
     */
    @Column(name = "`check_indicator`")
    private String checkIndicator;

    /**
     * 支付方式T=telegraphic transfer, A=Auto transfer, D=Draft, C=Cheque, O=Overseas Draft, X=Not provided'
     */
    @Column(name = "`payment_method`")
    private String paymentMethod;

    /**
     * --废弃扣除费用，deduction
     */
    @Column(name = "`non_taxable_income`")
    private BigDecimal nonTaxableIncome;

    /**
     * 支付编号
     */
    @Column(name = "`autopay_no`")
    private String autopayNo;

    /**
     * 
     */
    @Column(name = "`remark`")
    private String remark;

    /**
     * 调整管理费 负数
     */
    @Column(name = "`adj_commission_amount`")
    private BigDecimal adjCommissionAmount;

    /**
     * 调整所得税
     */
    @Column(name = "`adj_withheld_tax`")
    private BigDecimal adjWithheldTax;

    /**
     * 
     */
    @Column(name = "`ref_ip_base_no`")
    private String refIpBaseNo;

    /**
     * 
     */
    @Column(name = "`transfer_ip_base_no`")
    private String transferIpBaseNo;

    /**
     * 企业税率
     */
    @Column(name = "`sales_tax_rate`")
    private BigDecimal salesTaxRate;

    /**
     * 企业税额
     */
    @Column(name = "`sales_tax_amount`")
    private BigDecimal salesTaxAmount;

    /**
     * 发票编号
     */
    @Column(name = "`inv_no`")
    private String invNo;

    /**
     * Y  or N,默认为Y，修改为N表示hold款暂不支付，团体会员未设置发票也为N，设置了发票后修改为Y
     */
    @Column(name = "`is_pay`")
    private String pay;

    /**
     * 会员名称
     */
    @Column(name = "`pa_name`")
    private String paName;

    /**
     * 所得税率
     */
    @Column(name = "`taxable_rate`")
    private BigDecimal taxableRate;

    /**
     * 所得税金额，负数
     */
    @Column(name = "`taxable_amount`")
    private BigDecimal taxableAmount;

    /**
     * 管理费率
     */
    @Column(name = "`commission_rate`")
    private BigDecimal commissionRate;

    /**
     * 汇率
     */
    @Column(name = "`exchange_rate`")
    private BigDecimal exchangeRate;

    /**
     * 
     */
    @Column(name = "`create_user`")
    private String createUser;

    /**
     * 
     */
    @Column(name = "`amend_user`")
    private String amendUser;

    /**
     * 权利金     正数
     */
    @Column(name = "`royalty_amount`")
    private BigDecimal royaltyAmount;

    /**
     * 权利金调整 正数
     */
    @Column(name = "`adj_royalty_amount`")
    private BigDecimal adjRoyaltyAmount;

    /**
     * 特殊扣除费用，负数
     */
    @Column(name = "`deduction`")
    private BigDecimal deduction;

    /**
     * 预付款，正数
     */
    @Column(name = "`advance`")
    private BigDecimal advance;

    /**
     * 支付货币，默认TWD
     */
    @Column(name = "`pay_currency`")
    private String payCurrency;

    /**
     * 银行编号
     */
    @Column(name = "`bank_no`")
    private Long bankNo;

    /**
     * 账户名称
     */
    @Column(name = "`bank_account_name`")
    private String bankAccountName;

    /**
     * 银行账户id
     */
    @Column(name = "`bank_account_no`")
    private String bankAccountNo;

    /**
     * 基础金额，UPA费用
     */
    @Column(name = "`basic_amount`")
    private BigDecimal basicAmount;

    @Column(name = "`retain_id`")
    private Long retainId ;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getIpType() {
        return ipType;
    }

    public void setIpType(String ipType) {
        this.ipType = ipType;
    }

    public String getRetainCode() {
        return retainCode;
    }

    public void setRetainCode(String retainCode) {
        this.retainCode = retainCode;
    }

    public BigDecimal getLocalTaxableIncome() {
        return localTaxableIncome;
    }

    public void setLocalTaxableIncome(BigDecimal localTaxableIncome) {
        this.localTaxableIncome = localTaxableIncome;
    }

    public BigDecimal getOverseasTaxableIncome() {
        return overseasTaxableIncome;
    }

    public void setOverseasTaxableIncome(BigDecimal overseasTaxableIncome) {
        this.overseasTaxableIncome = overseasTaxableIncome;
    }

    public Integer getTaxYear() {
        return taxYear;
    }

    public void setTaxYear(Integer taxYear) {
        this.taxYear = taxYear;
    }

    public BigDecimal getWithheldTax() {
        return withheldTax;
    }

    public void setWithheldTax(BigDecimal withheldTax) {
        this.withheldTax = withheldTax;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getCheckIndicator() {
        return checkIndicator;
    }

    public void setCheckIndicator(String checkIndicator) {
        this.checkIndicator = checkIndicator;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public BigDecimal getNonTaxableIncome() {
        return nonTaxableIncome;
    }

    public void setNonTaxableIncome(BigDecimal nonTaxableIncome) {
        this.nonTaxableIncome = nonTaxableIncome;
    }

    public String getAutopayNo() {
        return autopayNo;
    }

    public void setAutopayNo(String autopayNo) {
        this.autopayNo = autopayNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getAdjCommissionAmount() {
        return adjCommissionAmount;
    }

    public void setAdjCommissionAmount(BigDecimal adjCommissionAmount) {
        this.adjCommissionAmount = adjCommissionAmount;
    }

    public BigDecimal getAdjWithheldTax() {
        return adjWithheldTax;
    }

    public void setAdjWithheldTax(BigDecimal adjWithheldTax) {
        this.adjWithheldTax = adjWithheldTax;
    }

    public String getRefIpBaseNo() {
        return refIpBaseNo;
    }

    public void setRefIpBaseNo(String refIpBaseNo) {
        this.refIpBaseNo = refIpBaseNo;
    }

    public String getTransferIpBaseNo() {
        return transferIpBaseNo;
    }

    public void setTransferIpBaseNo(String transferIpBaseNo) {
        this.transferIpBaseNo = transferIpBaseNo;
    }

    public BigDecimal getSalesTaxRate() {
        return salesTaxRate;
    }

    public void setSalesTaxRate(BigDecimal salesTaxRate) {
        this.salesTaxRate = salesTaxRate;
    }

    public BigDecimal getSalesTaxAmount() {
        return salesTaxAmount;
    }

    public void setSalesTaxAmount(BigDecimal salesTaxAmount) {
        this.salesTaxAmount = salesTaxAmount;
    }

    public String getInvNo() {
        return invNo;
    }

    public void setInvNo(String invNo) {
        this.invNo = invNo;
    }

    public String getPay() {
        return pay;
    }

    public void setPay(String pay) {
        this.pay = pay;
    }

    public String getPaName() {
        return paName;
    }

    public void setPaName(String paName) {
        this.paName = paName;
    }

    public BigDecimal getTaxableRate() {
        return taxableRate;
    }

    public void setTaxableRate(BigDecimal taxableRate) {
        this.taxableRate = taxableRate;
    }

    public BigDecimal getTaxableAmount() {
        return taxableAmount;
    }

    public void setTaxableAmount(BigDecimal taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getAmendUser() {
        return amendUser;
    }

    public void setAmendUser(String amendUser) {
        this.amendUser = amendUser;
    }

    public BigDecimal getRoyaltyAmount() {
        return royaltyAmount;
    }

    public void setRoyaltyAmount(BigDecimal royaltyAmount) {
        this.royaltyAmount = royaltyAmount;
    }

    public BigDecimal getAdjRoyaltyAmount() {
        return adjRoyaltyAmount;
    }

    public void setAdjRoyaltyAmount(BigDecimal adjRoyaltyAmount) {
        this.adjRoyaltyAmount = adjRoyaltyAmount;
    }

    public BigDecimal getDeduction() {
        return deduction;
    }

    public void setDeduction(BigDecimal deduction) {
        this.deduction = deduction;
    }

    public BigDecimal getAdvance() {
        return advance;
    }

    public void setAdvance(BigDecimal advance) {
        this.advance = advance;
    }

    public String getPayCurrency() {
        return payCurrency;
    }

    public void setPayCurrency(String payCurrency) {
        this.payCurrency = payCurrency;
    }

    public Long getBankNo() {
        return bankNo;
    }

    public void setBankNo(Long bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public BigDecimal getBasicAmount() {
        return basicAmount;
    }

    public void setBasicAmount(BigDecimal basicAmount) {
        this.basicAmount = basicAmount;
    }

    public Long getRetainId() {
        return retainId;
    }

    public void setRetainId(Long retainId) {
        this.retainId = retainId;
    }
}