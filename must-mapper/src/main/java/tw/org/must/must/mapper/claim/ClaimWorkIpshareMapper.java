package tw.org.must.must.mapper.claim;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimWorkIpshare;

import java.util.List;

@Repository
public interface ClaimWorkIpshareMapper extends BaseMapper<ClaimWorkIpshare> {
    List<ClaimWorkIpshare> getClaimWorkIpShareBy(String rightType);

    void insertDuplicateClaimWorkIpshareList(List<ClaimWorkIpshare> claimWorkIpShareList);
}