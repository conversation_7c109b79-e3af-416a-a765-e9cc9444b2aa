package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalMbrMemberMembership;

@Repository
public interface OrcalMbrMemberMembershipMapper {

	List<OrcalMbrMemberMembership> getMbrMemberShipList(String lastMbrMemberAmendTime);

	List<OrcalMbrMemberMembership> selectByDateByRowNum(String lastMbrMemberAmendTime, int start, int end);

	List<OrcalMbrMemberMembership> getMemberShipOracle(List<String> ipBaseNos);

}
