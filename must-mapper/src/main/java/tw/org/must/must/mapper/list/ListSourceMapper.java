package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListSource;

import java.util.List;
import java.util.Set;

@Repository
public interface ListSourceMapper extends BaseMapper<ListSource> {

    List<ListSource> listListSourceByDistNo(@Param("distNo") String distNo);

    List<ListSource> selectSourceWithPoolCode(@Param("sourceNameList") Set<String> sourceNameList) ;
}
