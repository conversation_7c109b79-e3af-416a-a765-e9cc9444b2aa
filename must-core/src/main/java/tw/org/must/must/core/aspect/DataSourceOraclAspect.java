package tw.org.must.must.core.aspect;

import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import tw.org.must.must.core.datasource.DBContextHolder;

@Aspect
@Component
public class DataSourceOraclAspect {

    @Pointcut("!@annotation(tw.org.must.must.core.annotation.Master) " +
            "&& !(execution(* tw.org.must.must.mapper..*.select*(..)) " +
            "|| execution(* tw.org.must.must.mapper..*.list*(..)) " +
            "|| execution(* tw.org.must.must.mapper..*.get*(..))) "+
            "&& execution(* tw.org.must.must.mapper.orcal.*.*(..))"
    		)
    public void readPointcut() {

    }

    @Before("readPointcut()")
    public void read() {
        DBContextHolder.oracle();
    }
    
    @After("readPointcut()")
    public void readPointcutEnd() {
        DBContextHolder.master();
    }
}
