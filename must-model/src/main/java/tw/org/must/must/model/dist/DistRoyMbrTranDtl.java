package tw.org.must.must.model.dist;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`dist_roy_mbr_tran_dtl`")
public class DistRoyMbrTranDtl extends BaseEntity {

    /**
     * 分配編號
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * ip base no
     */
    @Column(name = "`ip_base_no`")
    private String ipBaseNo;

    /**
     * 
     */
    @Column(name = "`pa_name_no`")
    private String paNameNo;

    /**
     * 
     */
    @Column(name = "`pa_name`")
    private String paName;

    /**
     * 描述
     */
    @Column(name = "`tran_descpt`")
    private String tranDescpt;

    /**
     * tran 費用
     */
    @Column(name = "`tran_amt`")
    private BigDecimal tranAmt;

    /**
     * ref_tran_type
     */
    @Column(name = "`tran_type`")
    private Integer tranType;

    /**
     * 是否扣稅標識，N or Y
     */
    @Column(name = "`withheld_tax_ind`")
    private String withheldTaxInd;

    /**
     * 是否重新計算稅率直接更改，對應界面中的deduction
     */
    @Column(name = "`non_taxable_income_ind`")
    private String nonTaxableIncomeInd;

    /**
     * 待定，不知道後續用途
     */
    @Column(name = "`add_after_dist_ind`")
    private String addAfterDistInd;

    /**
     * 
     */
    @Column(name = "`auto_ind`")
    private String autoInd;

    /**
     * 
     */
    @Column(name = "`commission_ind`")
    private String commissionInd;

    /**
     * 
     */
    @Column(name = "`tran_item_type`")
    private String tranItemType;

    @Transient
    private String payStatus; //付款狀態

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public String getPaName() {
        return paName;
    }

    public void setPaName(String paName) {
        this.paName = paName;
    }

    public String getTranDescpt() {
        return tranDescpt;
    }

    public void setTranDescpt(String tranDescpt) {
        this.tranDescpt = tranDescpt;
    }

    public BigDecimal getTranAmt() {
        return tranAmt;
    }

    public void setTranAmt(BigDecimal tranAmt) {
        this.tranAmt = tranAmt;
    }

    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    public String getWithheldTaxInd() {
        return withheldTaxInd;
    }

    public void setWithheldTaxInd(String withheldTaxInd) {
        this.withheldTaxInd = withheldTaxInd;
    }

    public String getNonTaxableIncomeInd() {
        return nonTaxableIncomeInd;
    }

    public void setNonTaxableIncomeInd(String nonTaxableIncomeInd) {
        this.nonTaxableIncomeInd = nonTaxableIncomeInd;
    }

    public String getAddAfterDistInd() {
        return addAfterDistInd;
    }

    public void setAddAfterDistInd(String addAfterDistInd) {
        this.addAfterDistInd = addAfterDistInd;
    }

    public String getAutoInd() {
        return autoInd;
    }

    public void setAutoInd(String autoInd) {
        this.autoInd = autoInd;
    }

    public String getCommissionInd() {
        return commissionInd;
    }

    public void setCommissionInd(String commissionInd) {
        this.commissionInd = commissionInd;
    }

    public String getTranItemType() {
        return tranItemType;
    }

    public void setTranItemType(String tranItemType) {
        this.tranItemType = tranItemType;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }
}