<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.16.0.final using JasperReports Library version 6.16.0-48579d909b7943b64690c65c71e07e0b80981928  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_fiche_rpt_subreport_otherTitle" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fb506072-2bad-45c1-964e-a63c3177c298">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="name" class="java.lang.String"/>
	<field name="zipCode" class="java.lang.String"/>
	<field name="area" class="java.lang.String"/>
	<field name="address" class="java.lang.String"/>
	<field name="attn" class="java.lang.String"/>
	<field name="memberNo" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="93" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement x="1" y="0" width="198" height="11" uuid="52cc04bb-7904-422f-b3ea-4ef31441853a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="45" y="13" width="148" height="12" uuid="78aec70d-7303-40c5-b9fa-7ad55b8960ea">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{zipCode}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="28" width="198" height="11" uuid="e7152c8a-cdd5-4097-b90b-a7a583e6d384">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{area}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="42" width="198" height="11" uuid="699bba46-23a1-4857-9b22-34cfac778b0a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{address}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="35" y="62" width="156" height="12" uuid="d6b07a1f-adb1-404b-8d92-0622d8a37fc4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{attn}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="199" y="62" width="100" height="11" uuid="0ab3d640-8cc3-4746-9fb5-756af2ef4d3b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{memberNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="13" width="45" height="12" uuid="8a0330e6-6abd-446c-b53b-2371ccbf5e8f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[郵遞區號 :]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="62" width="35" height="12" uuid="3dac1815-5462-4bce-b011-599f8c1f76fc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[ATTN :]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
