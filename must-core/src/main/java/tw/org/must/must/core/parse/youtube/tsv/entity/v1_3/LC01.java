package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;
@Data
public class LC01 extends BaseTsvEntity {
    private String BlockId;
    private String ResourceReference;
    private String DspResourceId;
    private String ISRC;
    private String ResourceTitle;
    private String ResourceSubTitle;
    private String ResourceDisplayArtistName;
    private String ResourceDisplayArtistPartyId;
    private String ResourceDuration;
    private String ResourceType;
    private String MusicalWorkISWC;
    private String MusicalWorkComposerAuthorName;
    private String MusicalWorkComposerAuthorPartyId;
    private String MusicalWorkArrangerName;
    private String MusicalWorkArrangerPartyId;
    private String MusicPublisherName;
    private String MusicPublisherPartyId;
    private String MusicalWorkContributorName;
    private String MusicalWorkContributorPartyId;
    private String ProprietaryMusicalWorkId;
    private String DataProviderName;
    private String IsSubjectToOwnershipConflict;
}
