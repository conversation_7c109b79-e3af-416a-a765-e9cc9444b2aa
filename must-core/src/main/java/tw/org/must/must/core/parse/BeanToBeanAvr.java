package tw.org.must.must.core.parse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.firstbrave.api.avr.vo.AlternateAvWorkTitle;
import com.firstbrave.api.avr.vo.AvWorkNotification;
import com.firstbrave.api.avr.vo.CueExistingWorkUsage;
import com.firstbrave.api.avr.vo.GroupHeader;
import com.firstbrave.api.avr.vo.GroupTrailer;
import com.firstbrave.api.avr.vo.InterestedPartyForMusicalWork;
import com.firstbrave.api.avr.vo.MusicalWorkInformation;
import com.firstbrave.api.avr.vo.ShareForMusicalWork;
import com.firstbrave.api.avr.vo.TransmissionHeader;
import com.firstbrave.api.avr.vo.TransmissionTrailer;

import tw.org.must.must.common.enums.GenreEnum;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.redis.NoGener;
import tw.org.must.must.model.avr.AvrWork;
import tw.org.must.must.model.avr.AvrWorkShare;
import tw.org.must.must.model.avr.AvrWorkTitle;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkComponent;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;

public class BeanToBeanAvr {

	public static void main(String[] args) {
		BigDecimal s = new BigDecimal(12.34);
		int i = (int) (s.doubleValue() * 100);
		System.out.println(i);
	}

	public static AvrWork transform(TransmissionHeader hdr, AvWorkNotification avr) {
		AvrWork work = new AvrWork();
		work.setAirDate(DateParse.parseDate(avr.getAirDate(), "yyyyMMdd"));
		work.setAvWorkNo(avr.getSocietyAvWorkNumber());
//		work.setAvWorkSocietyCode(avr.gets);
		//
//		work.setAvWorkUniqueKey(Constants.getWorkUniqueKey(work.getAvWorkSocietyCode(), work.getAvWorkNo()));
		
		
		work.setCategory(avr.getCategory());
		work.setCopyrightDate(null);
		work.setCopyrightNo(null);
		work.setCueSheetType(avr.getCueSheetType());
		work.setCueType(null);
		work.setCueUsage(null);
		work.setDestination(avr.getDestination());
		work.setDurationTotalMusic(avr.getTotalMusicDuration());
		work.setDurationUsage(null);
		work.setDurationWork(avr.getAvWorkDuration());
		work.setEpisodeNo(avr.getEpisodeNumber());
		work.setEpisodeTitle(avr.getEpisodeTitle());
		work.setInstrumentalOrVocal(null);
		work.setIsrc(null);
		work.setIswc(null);
		work.setOrigin(null);
		if (Objects.nonNull(avr.getYearOfProduction())){
			work.setProduceYear(avr.getYearOfProduction().toString());
		}
		work.setProductionNo(avr.getProductionNumber());
		work.setRevisionCode(avr.getCodeOfRevision());
		work.setRevisionDate(DateParse.parseDate(avr.getDateOfRevision(), "yyyyMMdd"));
		work.setSeriesNo(avr.getSeriesIdentifier());
		work.setSocCode(hdr.getSenderId().substring(6));
		work.setSocSeriesNo(avr.getSocietySeriesNumber());
		work.setSocWorkNo(StringUtils.isBlank(avr.getSocietyAvWorkNumber()) ? UUID.randomUUID().toString()
				: avr.getSocietyAvWorkNumber());
		work.setUsageCount(null);
		work.setWorkCountry(avr.getCountryOfOrigin());
		work.setWorkLanguage(avr.getCisLanguageCode());
		work.setWorkTerritory(avr.getTisNumericCode());
		work.setWorkTitle(avr.getAvWorkTitle());
		work.setWorkType(avr.getType());
		work.setStatus(0);
		work.init();
		return work;
	}

	public static AvrWork transform(TransmissionHeader hdr, AvWorkNotification avr, CueExistingWorkUsage cue,
			MusicalWorkInformation mwi) {
		AvrWork work = new AvrWork();
		work.setAirDate(null);
		work.setAvWorkNo(avr.getSocietyAvWorkNumber());
//		work.setAvWorkSocietyCode(null);
//		work.setAvWorkUniqueKey(Constants.getWorkUniqueKey(work.getAvWorkSocietyCode(), work.getAvWorkNo()));
		
		work.setCategory(mwi.getMusicalWorkDistributionCategory());
		work.setCopyrightDate(DateParse.parseDate(mwi.getCopyrightDate(), "yyyyMMdd"));
		work.setCopyrightNo(mwi.getCopyrightNumber());
		work.setCueSheetType(null);
		work.setCueType(cue.getCueType());
		work.setCueUsage(cue.getCueUsage());
		work.setDestination(null);
		work.setDurationTotalMusic(null);
		work.setDurationUsage(cue.getDuration());
		work.setDurationWork(mwi.getDuration());
		work.setEpisodeNo(null);
		work.setEpisodeTitle(null);
		work.setInstrumentalOrVocal(cue.getInstrumentalVocal());
		work.setIsrc(cue.getIsrc());
		work.setIswc(cue.getIswc());
		work.setOrigin(cue.getOrigin());
		work.setProduceYear(null);
		work.setProductionNo(null);
		work.setRevisionCode(null);
		work.setRevisionDate(null);
		work.setSeriesNo(null);
		work.setSocCode(hdr.getSenderId().substring(6));
		work.setSocSeriesNo(null);
		work.setSocWorkNo(StringUtils.isBlank(mwi.getSocietyWorkNumber()) ? UUID.randomUUID().toString()
				: mwi.getSocietyWorkNumber());
		work.setUsageCount(cue.getNumberOfUses());
		work.setWorkCountry(null);
		work.setWorkLanguage(mwi.getCisLanguageCode());
		work.setWorkTerritory(null);
		work.setWorkTitle(mwi.getWorkTitle());
		work.setWorkType(null);
		work.setStatus(0);
		work.init();
		return work;
	}

	public static AvrWorkTitle transform(TransmissionHeader hdr, AvWorkNotification avr, AlternateAvWorkTitle aat) {
		AvrWorkTitle workTitle = new AvrWorkTitle();
		workTitle.setEpisodeTitle(aat.getAlternateEpisodeTitle());
		workTitle.setEpisodeTitleLanguage(aat.getCisLanguageCodeOfEpisodeTitle());
		workTitle.setSocCode(hdr.getSenderId().substring(6));
		workTitle.setSocWorkNo(avr.getSocietyAvWorkNumber());
		workTitle.setTitle(aat.getAlternateTitle());
		workTitle.setTitleLanguage(aat.getCisLanguageCode());
		workTitle.setTitleTerritory(aat.getTisNumericCode());
		workTitle.setTitleType(aat.getTitleType());
		workTitle.setStatus(0);
		workTitle.init();
		return workTitle;
	}

	public static AvrWorkShare transform(TransmissionHeader hdr, AvWorkNotification avr,
			InterestedPartyForMusicalWork mip, ShareForMusicalWork msr) {
		AvrWorkShare workShare = new AvrWorkShare();
		workShare.setAffiliation(msr.getAffiliation());
		workShare.setFirstName(mip.getFirstName());
		workShare.setIpiNameNo(mip.getIpiName());
		workShare.setIpRole(mip.getIpRole());
		workShare.setName(mip.getName());
		workShare.setRightType(msr.getTypeOfRight());
		workShare.setShares(msr.getShares());
		workShare.setSocCode(hdr.getSenderId().substring(6));
		workShare.setSocWorkNo(avr.getSocietyAvWorkNumber());
		workShare.setUsSoc(msr.getUsSocietyOfLicenseIndicator());
		workShare.setStatus(0);
		workShare.init();
		return workShare;
	}

	public static TransmissionHeader transformToHdr() {
		TransmissionHeader hdr = new TransmissionHeader();
		Date date = new Date();
		hdr.setCreationDate(DateParse.format(date, "yyyyMMdd"));
		hdr.setCreationTime(DateParse.format(date, "hhmmss"));
		hdr.setRecordType("HDR");
		hdr.setSenderId("000000161");
		hdr.setSenderName("MUST society");
		hdr.setSenderType("SO");
		hdr.setStandardVersionNumber("01.10");
		hdr.setTransmissionDate(DateParse.format(date, "yyyyMMdd"));
		return hdr;
	}

	public static GroupHeader transformToGrh(int groupId) {
		GroupHeader grh = new GroupHeader();
		long batchNo = NoGener.getIncrement("AVR_BATCH_REQUEST_NO");
		grh.setBatchRequest((int) batchNo);
		grh.setGroupId(groupId);
		grh.setRecordType("GRH");
		grh.setSubmissionType(null);
		grh.setTransactionType("AVR");
		grh.setVersionNumber("01.20");
		return grh;
	}

	public static GroupTrailer transformToGrt(int groupId, int recordCount, int transactionCount) {
		GroupTrailer grt = new GroupTrailer();
		grt.setCurrencyIndicator(null);
		grt.setGroupId(groupId);
		grt.setRecordCount(recordCount);
		grt.setRecordType("GRT");
		grt.setTotalMonetaryValue(null);
		grt.setTransactionCount(transactionCount);
		return grt;
	}

	public static TransmissionTrailer transformToTrl(int groupCount, int recordCount, int transactionCount) {
		TransmissionTrailer trl = new TransmissionTrailer();
		trl.setGroupCount(groupCount);
		trl.setRecordCount(recordCount);
		trl.setRecordType("TRL");
		trl.setTransactionCount(transactionCount);
		return trl;
	}

	public static AvWorkNotification transformToAvr(WrkWork work, WrkWorkTitle title, int transactionSequenceNo,
			int recordSequenceNo) {
		AvWorkNotification avr = new AvWorkNotification();
		avr.setAirDate(null);
		avr.setAvWorkDuration(coverDuration(title.getDurationM()==null?0:title.getDurationM(), title.getDurationS()==null?0:title.getDurationS()));
		avr.setAvWorkTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		avr.setCategory("FIL".equals(title.getGenreCode()) ? "F" : "S");
		avr.setCisLanguageCode(null);
		avr.setCodeOfRevision(null);
		avr.setContentVersion(null);
		avr.setCountryOfOrigin(null);
		avr.setCueSheetType(null);
		avr.setDateOfRevision(null);
		avr.setDestination(null);
		avr.setEpisodeNumber(title.getEpisodeNo() == null ? null : String.valueOf(title.getEpisodeNo()));
		avr.setEpisodeTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		avr.setFiller1(null);
		avr.setFiller2(null);
		avr.setFiller3(null);
		avr.setProductionNumber(null);
		avr.setRecordSequenceNo(recordSequenceNo);
		avr.setRecordType("AVR");
		avr.setSeriesIdentifier(null);
		avr.setSocietyAvWorkNumber(work.getWorkId().toString());
		avr.setSocietySeriesNumber(null);
		avr.setTisNumericCode(null);
		avr.setTotalMusicDuration(null);
		avr.setTransactionSequenceNo(transactionSequenceNo);
		avr.setType(null);
		avr.setYearOfProduction(null);
		return avr;
	}

	public static AlternateAvWorkTitle transformToAat(WrkWorkTitle title, int transactionSequenceNo,
			int recordSequenceNo) {
		AlternateAvWorkTitle aat = new AlternateAvWorkTitle();
		aat.setAlternateEpisodeTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		aat.setAlternateTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		aat.setCisLanguageCode(title.getLanguageCode());
		aat.setCisLanguageCodeOfEpisodeTitle(null);
		aat.setRecordSequenceNo(recordSequenceNo);
		aat.setRecordType("AAT");
		aat.setTisNumericCode(null);
		aat.setTitleType(title.getTitleType());
		aat.setTransactionSequenceNo(transactionSequenceNo);
		return aat;
	}

	public static CueExistingWorkUsage transformToCue(WrkWork work, WrkWorkTitle title, WrkWorkComponent component,
			int transactionSequenceNo, int recordSequenceNo) {
		CueExistingWorkUsage cue = new CueExistingWorkUsage();
		cue.setCueType(null);
		cue.setCueUsage(coverCueUsage(component.getUsageType()));
		cue.setDuration(coverDuration(component.getDurationM(), component.getDurationS()));
		cue.setInstrumentalVocal("V");
		cue.setIsrc(null);
		cue.setIswc(null);
		cue.setNumberOfUses(1);
		cue.setOrigin(null);
		cue.setRecordSequenceNo(recordSequenceNo);
		cue.setRecordType("CUE");
		cue.setSocietyWorkNumber(work.getWorkSocietyCode() + "" + work.getWorkId());
		cue.setTransactionSequenceNo(transactionSequenceNo);
		cue.setWorkTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		return cue;
	}

	public static MusicalWorkInformation transformToMwi(WrkWork work, WrkWorkTitle title, int transactionSequenceNo,
			int recordSequenceNo) {
		MusicalWorkInformation mwi = new MusicalWorkInformation();
		mwi.setCisLanguageCode(null);
		mwi.setCompositeType(null);
		mwi.setCopyrightDate(null);
		mwi.setCopyrightNumber(null);
		mwi.setDuration(coverDuration(title.getDurationM(), title.getDurationS()));
		mwi.setExcerptType(null);
		mwi.setFiller(null);
		mwi.setIswc(null);
		mwi.setLyricAdaptation(null);
		//Genre 曲风对应关系
		if(StringUtils.isNotBlank(title.getGenreCode())){
			try{
				String category = Enum.valueOf(GenreEnum.class,title.getGenreCode()).getGenre();
				mwi.setMusicalWorkDistributionCategory(category);
			}catch (IllegalArgumentException e){
			}
		}
		mwi.setMusicArrangement(null);
		mwi.setRecordedIndicator(null);
		mwi.setRecordSequenceNo(recordSequenceNo);
		mwi.setRecordType("MWI");
		mwi.setSocietyWorkNumber(work.getWorkId().toString());
		mwi.setTextMusicRelationship(null);
		mwi.setTransactionSequenceNo(transactionSequenceNo);
		mwi.setVersionType(null);
		mwi.setWorkTitle(title.getTitle() == null ? title.getTitleEn() : title.getTitle());
		return mwi;
	}

	public static InterestedPartyForMusicalWork transformToMip(WrkWorkIpShare share, MbrIpName name,
			int transactionSequenceNo, int recordSequenceNo) {
		InterestedPartyForMusicalWork mip = new InterestedPartyForMusicalWork();
		mip.setFiller1(null);
		mip.setFiller2(null);
		mip.setFirstName(null);
		mip.setInterestedPartyNumber(null);
		mip.setIpiName(share.getIpNameNo());
		mip.setIpRole(share.getWorkIpRole());
		mip.setName(name.getName());
		mip.setRecordSequenceNo(recordSequenceNo);
		mip.setRecordType("MIP");
		mip.setTransactionSequenceNo(transactionSequenceNo);
		return mip;
	}

	public static ShareForMusicalWork transformToMsr(WrkWorkIpShare share, int transactionSequenceNo,
			int recordSequenceNo) {
		ShareForMusicalWork msr = new ShareForMusicalWork();
		msr.setAffiliation(null);
		msr.setFiller(null);
		msr.setIpiName(share.getIpNameNo());
		msr.setRecordSequenceNo(recordSequenceNo);
		msr.setRecordType("MSR");
		msr.setShares((int) (share.getIpShare().doubleValue() * 100));
		msr.setTransactionSequenceNo(transactionSequenceNo);
		msr.setTypeOfRight(share.getRightType());
		msr.setUsSocietyOfLicenseIndicator(null);
		return msr;
	}

	private static String coverDuration(Integer minute, Integer seconds) {
		if (minute == null){
			minute = 0;
		}
		if (seconds == null){
			seconds = 0;
		}
		minute += seconds / 60;
		int h = (minute / 60);
		int m = minute % 60;
		int s = seconds % 60;
		StringBuilder sb = new StringBuilder();
		if (h > 9) {
			sb.append(h);
		} else {
			sb.append("0").append(h);
		}
		if (m > 9) {
			sb.append(m);
		} else {
			sb.append("0").append(m);
		}
		if (s > 9) {
			sb.append(s);
		} else {
			sb.append("0").append(s);
		}
		return sb.toString();
	}

	private static String coverCueUsage(String usageType) {
		if (StringUtils.isEmpty(usageType)) {
			return null;
		}
		switch (usageType) {
		case "FB":
			return "B";
		case "FT":
			return "O";
		case "ST":
			return "O";
		case "FV":
			return "F";
		case "TM":
			return "L";
		default:
			return null;

		}
	}

	public static AvrWorkTitle getTitleByAvrWork(AvrWork work) {
		AvrWorkTitle avrWorkTitle = new AvrWorkTitle();
		avrWorkTitle.init();
		avrWorkTitle.setStatus(0);
		avrWorkTitle.setSocCode(work.getSocCode());
		avrWorkTitle.setSocWorkNo(work.getSocWorkNo());
		avrWorkTitle.setTitle(work.getWorkTitle());
		avrWorkTitle.setTitleLanguage(work.getWorkLanguage());
		avrWorkTitle.setTitleType(work.getWorkType());
		avrWorkTitle.setTitleTerritory(work.getWorkTerritory());
		avrWorkTitle.setEpisodeTitle(work.getEpisodeTitle());
		avrWorkTitle.setEpisodeTitleLanguage(work.getWorkLanguage());
		return avrWorkTitle;
	}
}
