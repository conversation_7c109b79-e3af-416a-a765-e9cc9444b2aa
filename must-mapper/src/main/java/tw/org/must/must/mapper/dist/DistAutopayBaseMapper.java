package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistAutopayBase;

import java.util.List;

@Repository
public interface DistAutopayBaseMapper extends BaseMapper<DistAutopayBase> {

    List<DistAutopayBase> getAutoPayNoList(@Param("autopayNo") String autopayNo);


}