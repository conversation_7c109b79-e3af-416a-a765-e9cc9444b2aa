package tw.org.must.must.core.parse.youtube.tsv.handle;

import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import tw.org.must.must.common.enums.DeletedEnum;
import tw.org.must.must.common.enums.IsShowEnum;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.*;
import tw.org.must.must.core.service.claim.ClaimMaxRevenueService;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListFileQueue;

import java.io.File;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 解析youtube   tsv文件
 *
 * <AUTHOR>
 */
@Deprecated
//@Service
public class ParseYoutubeTsvServiceOld {

	Logger logger = LoggerFactory.getLogger(ParseYoutubeTsvServiceOld.class);

	@Autowired
	private ListDspFileBaseService listDspFileBaseService;
	@Autowired
	private ListDspFileDataMappingService listDspFileDataMappingService;
	@Autowired
	private ClaimMaxRevenueService claimMaxRevenueService;
	@Autowired
	private ClaimMinimaInfoService claimMinimaInfoService;
	private static ListFileQueue listFileQueue;

	List<Sy0401> Sy0401List = new ArrayList<Sy0401>();
	List<SY0202> Sy0202List = new ArrayList<SY0202>();
	Header header = new Header();
	//	Map<String,BigDecimal> checkTotalUsages = new HashMap<>();
	Map<String, Long> connectListDspFileBaseMap = new HashMap<>();
	boolean isParse = false;

	String preBlockId = "";
	BigDecimal totalSy0202Usage = BigDecimal.ZERO;
	BigDecimal totalRu0101Usage = BigDecimal.ZERO;
	List<TsvCommonObject> tsvCommonObjectList = new ArrayList<>();


	Footer footer = new Footer();

	private ThreadLocal<TsvCommonObject> tsvCommonObjectThreadLocal = new ThreadLocal<>();
	private ThreadLocal<Su0302> nowSu0302ThreadLocal = new ThreadLocal<>();

	public void parse(ListFileQueue lfq) throws Exception {
		listFileQueue = lfq;
		parse(lfq.getFilePath());
	}

	/**
	 * Youtube  解析tsv类型文件
	 *
	 * @param filepath 文件全路径名
	 * @throws Exception 异常
	 */
	public void parse(String filepath) throws Exception {
//		FileInputStream fileInputStream = null;
		Map<String, List<String>> mapHeader = new HashMap<String, List<String>>();
		Map<String, String> map = new LinkedHashMap<>();
//			fileInputStream = new FileInputStream(new File(filepath));
		LineIterator lineIterator = FileUtils.lineIterator(new File(filepath), "Utf-8");
		while (lineIterator.hasNext()) {
			String line = lineIterator.nextLine();
			if (StringUtils.isBlank(line)) {
				continue;
			}
			if (line.startsWith("HEAD")) {
				String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
				createHeader(split);
				continue;
			}
			if (line.startsWith("#")) {
				dealWithString(line, mapHeader);
				continue;
			}
			line = line.replace("\\\\\t", "/").replace("\\\t", "\\").replace("\\", "/");
			String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
			String firstPoint = split[0];
			List<String> listMapKey = mapHeader.get(firstPoint);
			int remainValue = listMapKey.size() - split.length;
			if (remainValue != 0) {
				logger.info("头部字段与明细字段匹配不上的数据：" + line);
				continue;
			}

			getMapOfSplit(map, split, listMapKey);
			execute(map);

			map.clear();
			if (tsvCommonObjectList.size() >= 1000) {
				saveDspFileBase(filepath);
				saveDspFileDataMapping(tsvCommonObjectList);
				tsvCommonObjectList.clear();
			}


		}
		if (tsvCommonObjectList.size() > 0) {
//				saveDspFileBase();
			saveDspFileDataMapping(tsvCommonObjectList);
//				// 文件结尾 根据footer来更新dspFileBase
//				updateDspFileBase();
			logger.info("=============================================剩余最后" + tsvCommonObjectList.size() + "条数据处理完毕。");
			tsvCommonObjectList.clear();
		}
		logger.info("totalSy0202Usage is " + totalSy0202Usage);
		logger.info("totalRu0101Usage is " + totalRu0101Usage);
		LineIterator.closeQuietly(lineIterator);
	}

	private void createHeader(String[] split) {
		header.setName(split[0]);
		header.setMessageVersion(split[1]);

		header.setProfile(split[2]);

		header.setProfileVersion(split[3]);

		header.setMessageId(split[4]);

		header.setMessageCreatedDateTime(split[5].replace("T", " ").replace("Z", ""));

		header.setFileNumber(split[6]);

		header.setNumberofFiles(split[7]);

		header.setUsageStartDate(split[8]);

		header.setUsageEndDate(split[9]);

		header.setSenderPartyId(split[10]);

		header.setSenderName(split[11]);

		header.setServiceDescription(split[12]);

		header.setRecipientPartyId(split[13]);

		header.setRecipientName(split[14]);

		header.setRepresentedRepertoire(split[15]);
	}

	private void execute(Map<String, String> map) {

		// 处理文件头部		header  一个文件只有一个头
		if (map.containsKey("HEAD")) {

			header.setName("HEAD");
			String MessageVersion = map.get("MessageVersion");
			header.setMessageVersion(MessageVersion);

			String Profile = map.get("Profile");
			header.setProfile(Profile);

			String ProfileVersion = map.get("ProfileVersion");
			header.setProfileVersion(ProfileVersion);

			String MessageId = map.get("MessageId");
			header.setMessageId(MessageId);

			String MessageCreatedDateTime = map.get("MessageCreatedDateTime");
			MessageCreatedDateTime = MessageCreatedDateTime.replace("T", " ").replace("Z", "");
			header.setMessageCreatedDateTime(MessageCreatedDateTime);

			String FileNumber = map.get("FileNumber");
			header.setFileNumber(FileNumber);

			String NumberofFiles = map.get("NumberofFiles");
			header.setNumberofFiles(NumberofFiles);

			String UsageStartDate = map.get("UsageStartDate");
			header.setUsageStartDate(UsageStartDate);

			String UsageEndDate = map.get("UsageEndDate");
			header.setUsageEndDate(UsageEndDate);

			String SenderPartyId = map.get("SenderPartyId");
			header.setSenderPartyId(SenderPartyId);

			String SenderName = map.get("SenderName");
			header.setSenderName(SenderName);

			String ServiceDescription = map.get("ServiceDescription");
			header.setServiceDescription(ServiceDescription);

			String RecipientPartyId = map.get("RecipientPartyId");
			header.setRecipientPartyId(RecipientPartyId);

			String RecipientName = map.get("RecipientName");
			header.setRecipientName(RecipientName);

			String RepresentedRepertoire = map.get("RepresentedRepertoire");
			header.setRepresentedRepertoire(RepresentedRepertoire);
		}

		// AVOD的时候处理 SY0202节点  汇总信息
		if (map.containsKey("SY02.02")) {
			SY0202 sy0202 = new SY0202();
			sy0202.setName("SY02.02");
			String summaryRecordId = map.get("SummaryRecordId");
			sy0202.setSummaryRecordId(summaryRecordId);
			String distributionChannel = map.get("DistributionChannel");
			sy0202.setDistributionChannel(distributionChannel);
			String distributionChannelDPID = map.get("DistributionChannelDPID");
			sy0202.setDistributionChannelDPID(distributionChannelDPID);
			String commercialModel = map.get("CommercialModel");
			sy0202.setCommercialModel(commercialModel);
			String useType = map.get("UseType");
			sy0202.setUseType(useType);
			String territory = map.get("Territory");
			sy0202.setTerritory(territory);
			String serviceDescription = map.get("ServiceDescription");
			sy0202.setServiceDescription(serviceDescription);
			String usages = map.get("Usages");
			sy0202.setUsages(usages);
			String users = map.get("Users");
			sy0202.setUsers(users);
			String currency = map.get("Currency");
			sy0202.setCurrency(currency);
			String netRevenue = map.get("NetRevenue");
			sy0202.setNetRevenue(netRevenue);
			String rightsController = map.get("RightsController");
			sy0202.setRightsController(rightsController);
			String rightsControllerPartyId = map.get("RightsControllerPartyId");
			sy0202.setRightsControllerPartyId(rightsControllerPartyId);
			String allocatedUsages = map.get("AllocatedUsages");
			sy0202.setAllocatedUsages(allocatedUsages);
			String allocatedRevenue = map.get("AllocatedRevenue");
			sy0202.setAllocatedRevenue(allocatedRevenue);
			String allocatedNetRevenue = map.get("AllocatedNetRevenue");
			sy0202.setAllocatedNetRevenue(allocatedNetRevenue);
			String rightsType = map.get("RightsType");
			sy0202.setRightsType(rightsType);
			String contentCategory = map.get("ContentCategory");
			sy0202.setContentCategory(contentCategory);
			String exchangeRateBaseCurrency = map.get("ExchangeRateBaseCurrency");
			sy0202.setExchangeRateBaseCurrency(exchangeRateBaseCurrency);
			String exchangeRate = map.get("ExchangeRate");
			sy0202.setExchangeRate(exchangeRate);
			String rightsTypePercentage = map.get("RightsTypePercentage");
			sy0202.setRightsTypePercentage(rightsTypePercentage);
			Sy0202List.add(sy0202);
		}

		// 处理SY04.01节点  汇总信息
		if (map.containsKey("SY04.01")) {
			//ldfdm.setFileBaseId(listDspFileBase.getId());
			Sy0401 sy0401 = new Sy0401();
			sy0401.setName("SY04.01");
//        		ldfdm.setProduct("SY04.01");
			String SummaryRecordId = map.get("SummaryRecordId");
			sy0401.setSummaryRecordId(SummaryRecordId);
			String DistributionChannel = map.get("DistributionChannel");
			sy0401.setDistributionChannel(DistributionChannel);
			String DistributionChannelDPID = map.get("DistributionChannelDPID");
			sy0401.setDistributionChannelDPID(DistributionChannelDPID);
			String CommercialModel = map.get("CommercialModel");
			sy0401.setCommercialModel(CommercialModel);
			String UseType = map.get("UseType");
			sy0401.setUseType(UseType);
			String Territory = map.get("Territory");
			sy0401.setTerritory(Territory);
			String ServiceDescription = map.get("ServiceDescription");
			sy0401.setServiceDescription(ServiceDescription);
			String SubscriberType = map.get("SubscriberType");
			sy0401.setSubscriberType(SubscriberType);
			String Subscribers = map.get("Subscribers");
			sy0401.setSubscribers(Subscribers);
			String SubPeriodStartDate = map.get("SubPeriodStartDate");
			sy0401.setSubPeriodStartDate(SubPeriodStartDate);
			String SubPeriodEndDate = map.get("SubPeriodEndDate");
			sy0401.setSubPeriodEndDate(SubPeriodEndDate);
			String UsagesInSubPeriod = map.get("UsagesInSubPeriod");
			sy0401.setUsagesInSubPeriod(UsagesInSubPeriod);
			String Currency = map.get("Currency");
			sy0401.setCurrency(Currency);
			String ExchangeRateBaseCurrency = map.get("ExchangeRateBaseCurrency");
			sy0401.setExchangeRateBaseCurrency(ExchangeRateBaseCurrency);
			String ExchangeRate = map.get("ExchangeRate");
			sy0401.setExchangeRate(ExchangeRate);
			String ConsumerPaidUnitPrice = map.get("ConsumerPaidUnitPrice");
			sy0401.setConsumerPaidUnitPrice(ConsumerPaidUnitPrice);
			String Net = map.get("Net");
			sy0401.setNet(Net);
			String MusicUsagePercentage = map.get("MusicUsagePercentage");
			sy0401.setMusicUsagePercentage(MusicUsagePercentage);
			Sy0401List.add(sy0401);
		}

		// 处理SY09节点		汇总信息
		if (map.containsKey("SY09")) {
			Sy09 sy09 = new Sy09();
			sy09.setName("SY09");
//        		ldfdm.setProduct("SY09");
			String SummaryRecordId = map.get("SummaryRecordId");
			sy09.setSummaryRecordId(SummaryRecordId);
			String CommercialModel = map.get("CommercialModel");
			sy09.setCommercialModel(CommercialModel);
			String UseType = map.get("UseType");
			sy09.setUseType(UseType);
			String Territory = map.get("Territory");
			sy09.setTerritory(Territory);
			String ServiceDescription = map.get("ServiceDescription");
			sy09.setServiceDescription(ServiceDescription);
			String SubscriberType = map.get("SubscriberType");
			sy09.setSubscriberType(SubscriberType);
			String RightsController = map.get("RightsController");
			sy09.setRightsController(RightsController);
			String RightsControllerPartyID = map.get("RightsControllerPartyID");
			sy09.setRightsControllerPartyID(RightsControllerPartyID);
			String RightsType = map.get("RightsType");
			sy09.setRightsType(RightsType);
			String TotalUsages = map.get("TotalUsages");
			sy09.setTotalUsages(TotalUsages);
			String AllocatedUsages = map.get("AllocatedUsages");
			sy09.setAllocatedUsages(AllocatedUsages);
			String NetRevenue = map.get("NetRevenue");
			sy09.setNetRevenue(NetRevenue);
			String IndirectNetRevenue = map.get("IndirectNetRevenue");
			sy09.setIndirectNetRevenue(IndirectNetRevenue);
			String RightsControllerMarketShare = map.get("RightsControllerMarketShare");
			sy09.setRightsControllerMarketShare(RightsControllerMarketShare);
			String Currency = map.get("Currency");
			sy09.setCurrency(Currency);
			String ExchangeRateBaseCurrency = map.get("ExchangeRateBaseCurrency");
			sy09.setExchangeRateBaseCurrency(ExchangeRateBaseCurrency);
			String ExchangeRate = map.get("ExchangeRate");
			sy09.setExchangeRate(ExchangeRate);
			String RightsTypePercentage = map.get("RightsTypePercentage");
			sy09.setRightsTypePercentage(RightsTypePercentage);
			String SubPeriodStartDate = map.get("SubPeriodStartDate");
			sy09.setSubPeriodStartDate(SubPeriodStartDate);
			String SubPeriodEndDate = map.get("SubPeriodEndDate");
			sy09.setSubPeriodEndDate(SubPeriodEndDate);
		}

		// 处理SY05.02		汇总信息
		if (map.containsKey("SY05.02")) {
			Sy0502 sy0502 = new Sy0502();
			sy0502.setName("SY05.02");
//        		ldfdm.setProduct("SY05.02");
			String SummaryRecordId = map.get("SummaryRecordId");
			sy0502.setSummaryRecordId(SummaryRecordId);
			String DistributionChannel = map.get("DistributionChannel");
			sy0502.setDistributionChannel(DistributionChannel);
			String DistributionChannelDPID = map.get("DistributionChannelDPID");
			sy0502.setDistributionChannelDPID(DistributionChannelDPID);
			String CommercialModel = map.get("CommercialModel");
			sy0502.setCommercialModel(CommercialModel);
			String UseType = map.get("UseType");
			sy0502.setUseType(UseType);
			String Territory = map.get("Territory");
			sy0502.setTerritory(Territory);
			String ServiceDescription = map.get("ServiceDescription");
			sy0502.setServiceDescription(ServiceDescription);
			String RightsController = map.get("RightsController");
			sy0502.setRightsController(RightsController);
			String RightsControllerPartyID = map.get("RightsControllerPartyID");
			sy0502.setRightsControllerPartyID(RightsControllerPartyID);
			String RightsType = map.get("RightsType");
			sy0502.setRightsType(RightsType);
			String TotalUsages = map.get("TotalUsages");
			sy0502.setTotalUsages(TotalUsages);
			String AllocatedUsages = map.get("AllocatedUsages");
			sy0502.setAllocatedUsages(AllocatedUsages);
			String MusicUsageRatio = map.get("MusicUsageRatio");
			sy0502.setMusicUsageRatio(MusicUsageRatio);
			String AllocatedNetRevenue = map.get("AllocatedNetRevenue");
			sy0502.setAllocatedNetRevenue(AllocatedNetRevenue);
			String AllocatedRevenue = map.get("AllocatedRevenue");
			sy0502.setAllocatedRevenue(AllocatedRevenue);
			String RightsControllerMarketShare = map.get("RightsControllerMarketShare");
			sy0502.setRightsControllerMarketShare(RightsControllerMarketShare);
			String Currency = map.get("Currency");
			sy0502.setCurrency(Currency);
			String ExchangeRateBaseCurrency = map.get("ExchangeRateBaseCurrency");
			sy0502.setExchangeRateBaseCurrency(ExchangeRateBaseCurrency);
			String ExchangeRate = map.get("ExchangeRate");
			sy0502.setExchangeRate(ExchangeRate);
			String SubscriberType = map.get("SubscriberType");
			sy0502.setSubscriberType(SubscriberType);
			String SubPeriodStartDate = map.get("SubPeriodStartDate");
			sy0502.setSubPeriodStartDate(SubPeriodStartDate);
			String SubPeriodEndDate = map.get("SubPeriodEndDate");
			sy0502.setSubPeriodEndDate(SubPeriodEndDate);
			String ContentCategory = map.get("ContentCategory");
			sy0502.setContentCategory(ContentCategory);
			String RightsTypePercentage = map.get("RightsTypePercentage");
			sy0502.setRightsTypePercentage(RightsTypePercentage);
		}

		// 处理块节点  	AS01.01		BlockId   每个AS0101就是一个tsvCommonObject
		if (map.containsKey("AS01.01")) {
			String nowBlockId = map.get("BlockId");
			if (StringUtils.isNotBlank(preBlockId) && !nowBlockId.equals(preBlockId)) {
				logger.info("pre blockId is :" + preBlockId);
				TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
				if (tsvCommonObject.getMw0101() == null || tsvCommonObject.getSu0302List() == null || tsvCommonObject.getAs0101() == null || tsvCommonObject.getRu0101List() == null) {
					logger.info("节点空·········································");
				}

				tsvCommonObjectList.add(tsvCommonObject);
				tsvCommonObjectThreadLocal.remove();
				nowSu0302ThreadLocal.remove();
//					preMap.clear();
				logger.info("now blockId is :" + nowBlockId);
				logger.info("tsvCommonObjectList size is " + tsvCommonObjectList.size());
			}
			preBlockId = nowBlockId;
			As0101 as0101 = new As0101();
			as0101.setName("AS01.01");
			as0101.setBlockId(nowBlockId);
			as0101.setResourceReference(map.get("ResourceReference"));
			as0101.setDspResourceId(map.get("DspResourceId"));
			as0101.setISRC(map.get("ISRC"));
			as0101.setTitle(map.get("Title"));
			as0101.setSubTitle(map.get("SubTitle"));
			as0101.setDisplayArtistName(map.get("DisplayArtistName"));
			as0101.setDisplayArtistPartyId(map.get("DisplayArtistPartyId"));
			as0101.setDuration(map.get("Duration"));
			Matcher m = Pattern.compile("PT(?<H>\\d+)H(?<M>\\d+)M(?<S>\\d+)S").matcher(map.get("Duration"));
			if (m.find()) {
				as0101.setDurationM(m.group("M"));
				as0101.setDurationS(m.group("S"));
			}

			as0101.setResourceType(map.get("ResourceType"));

			as0101.setIsMasterRecording(map.get("IsMasterRecording"));
			TsvCommonObject tsvCommonObject = new TsvCommonObject();
			tsvCommonObject.setAs0101(as0101);

			tsvCommonObjectThreadLocal.set(tsvCommonObject);
		}

		//处理块 节点   RU01.01	BlockId  汇总
		if (map.containsKey("RU01.01")) {
			Ru0101 ru0101 = new Ru0101();
			ru0101.setName("RU01.01");
			ru0101.setBlockId(map.get("BlockId"));
			ru0101.setSummaryRecordId(map.get("SummaryRecordId"));
			ru0101.setDspReleaseId(map.get("DspReleaseId"));
			ru0101.setUsages(map.get("Usages"));
			ru0101.setContentCategory(map.get("ContentCategory"));
			TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();

			List<Ru0101> ru0101List = tsvCommonObject.getRu0101List();
			if (null == ru0101List || ru0101List.size() == 0) {
				ru0101List = new ArrayList<>();
			}
			ru0101List.add(ru0101);
			tsvCommonObject.setRu0101List(ru0101List);
			//mapResult.put(map.get("BlockId"), tsvCommonObject);
		}

		// 处理块节点 	SU03.02	BlockId
		if (map.containsKey("SU03.02")) {
			Su0302 su0302 = new Su0302();
			su0302.setName("SU03.02");
//        		ldfdm.setProduct("SU03.02");
			su0302.setBlockId(preBlockId);
			su0302.setSalesTransactionId(map.get("SalesTransactionId"));
			su0302.setSummaryRecordId(map.get("SummaryRecordId"));
			su0302.setDspReleaseId(map.get("DspReleaseId"));
			su0302.setDspResourceId(map.get("DspResourceId"));
			su0302.setUsages(map.get("Usages"));
			su0302.setNetRevenue(map.get("NetRevenue"));
			su0302.setValidityPeriodStart(map.get("ValidityPeriodStart"));
			su0302.setValidityPeriodEnd(map.get("ValidityPeriodEnd"));
			su0302.setContentCategory(map.get("ContentCategory"));
			su0302.setIsRoyaltyBearing(map.get("IsRoyaltyBearing"));
			TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();

			List<Su0302> su0302List = tsvCommonObject.getSu0302List();
			if (null == su0302List || su0302List.size() == 0) {
				su0302List = new ArrayList<>();
			}
			su0302List.add(su0302);
			tsvCommonObject.setSu0302List(su0302List);
			nowSu0302ThreadLocal.set(su0302);

		}

		// 处理块节点	LI01.02	BlockId
		if (map.containsKey("LI01.02")) {
			LI0102 li0102 = new LI0102();
			li0102.setName("LI01.02");
			li0102.setBlockId(preBlockId);
			li0102.setSummaryRecordId(map.get("SummaryRecordId"));
			li0102.setRightsController(map.get("RightsController"));
			li0102.setRightsControllerPartyId(map.get("RightsControllerPartyId"));
			li0102.setRightsControllerWorkID(map.get("RightsControllerWorkID"));
			li0102.setRightShare(map.get("RightShare%"));
			li0102.setRightType(map.get("RightType"));
			li0102.setAllocatedNetRevenue(map.get("AllocatedNetRevenue"));
			li0102.setAllocatedAmount(map.get("AllocatedAmount"));
			li0102.setAllocatedUsages(map.get("AllocatedUsages"));
			Su0302 su0302 = nowSu0302ThreadLocal.get();

			if (su0302 != null) {

				String validityPeriodStart = su0302.getValidityPeriodStart();
				String validityPeriodEnd = su0302.getValidityPeriodEnd();
				String uniqueKey = validityPeriodStart + validityPeriodEnd;
				li0102.setUniqueKey(uniqueKey);
				List<LI0102> li0102List = su0302.getLi0102List();
				if (null == li0102List) {
					li0102List = new ArrayList<>();
				}
				li0102List.add(li0102);
				su0302.setLi0102List(li0102List);
			}
		}

		// 处理块节点	MW01.01	BlockId	当MW01.01 出现在RU01.01之前 表示为主标题
		if (map.containsKey("MW01.01")) {
			Mw0101 mw0101 = new Mw0101();
			mw0101.setName("Mw01.01");
			mw0101.setBlockId(preBlockId);
			mw0101.setDspWorkId(map.get("DspWorkId"));
			mw0101.setISWC(map.get("ISWC"));
			mw0101.setTitle(map.get("Title"));
			mw0101.setSubTitle(map.get("SubTitle"));
			mw0101.setComposerAuthor(map.get("ComposerAuthor"));
			mw0101.setComposerAuthorPartyId(map.get("ComposerAuthorPartyId"));
			mw0101.setArranger(map.get("Arranger"));
			mw0101.setArrangerPartyId(map.get("ArrangerPartyId"));
			mw0101.setMusicPublisher(map.get("MusicPublisher"));
			mw0101.setMusicPublisherPartyId(map.get("MusicPublisherPartyId"));
			mw0101.setWorkContributor(map.get("WorkContributor"));
			mw0101.setWorkContributorPartyId(map.get("WorkContributorPartyId"));
			mw0101.setDataProvider(map.get("DataProvider"));
			mw0101.setProprietaryWorkId(map.get("ProprietaryWorkId"));
			Su0302 su0302 = nowSu0302ThreadLocal.get();
			TsvCommonObject tsvCommonObject = this.tsvCommonObjectThreadLocal.get();

			if (su0302 != null) {

				String validityPeriodStart = su0302.getValidityPeriodStart();
				String validityPeriodEnd = su0302.getValidityPeriodEnd();
				String uniqueKey = validityPeriodStart + validityPeriodEnd;
				mw0101.setUniqueKey(uniqueKey);
				List<Mw0101> mw0101List = su0302.getMw0101List();
				if (null == mw0101List) {
					mw0101List = new ArrayList<>();
				}
				mw0101List.add(mw0101);
				su0302.setMw0101List(mw0101List);
			} else {
				tsvCommonObject.setMw0101(mw0101);

			}
		}

		//  处理文件尾部信息	FOOT	一个文件只有一个尾
		if (map.containsKey("FOOT")) {

			//添加最后的一个
			TsvCommonObject tsvCommonObject = tsvCommonObjectThreadLocal.get();
			if (tsvCommonObject != null) {
				tsvCommonObjectList.add(tsvCommonObject);
			}
			footer.setName("FOOT");
			if (map.containsKey("NumberOfLinesInFile")) {
				String NumberOfLinesInFile = map.get("NumberOfLinesInFile"); // 文件记录数
				footer.setNumberOfLinesInFile(NumberOfLinesInFile);
			}
			if (map.containsKey("NumberOfLinesInReport")) {
				String NumberOfLinesInReport = map.get("NumberOfLinesInReport"); //
				footer.setNumberOfLinesInReport(NumberOfLinesInReport);
			}
			if (map.containsKey("NumberOfSummaryRecords")) {
				String NumberOfSummaryRecords = map.get("NumberOfSummaryRecords");//
				footer.setNumberOfSummaryRecords(NumberOfSummaryRecords);
			}
			if (map.containsKey("NumberOfBlocksInFile")) {
				String NumberOfBlocksInFile = map.get("NumberOfBlocksInFile");// block的总数
				footer.setNumberOfBlocksInFile(NumberOfBlocksInFile);
			}
			if (map.containsKey("NumberOfBlocksInReport")) {
				String NumberOfBlocksInReport = map.get("NumberOfBlocksInReport"); //
				footer.setNumberOfBlocksInReport(NumberOfBlocksInReport);
			}
		}
	}

	private void saveDspFileBase(String filepath) {
		if (isParse) {
			return;
		}
		if (null != Sy0202List && Sy0202List.size() > 0) {
			List<SY0202> GESy0202List = Sy0202List.stream().filter(it -> "all".equals(it.getRightsController())).filter(it -> "GE".equals(it.getContentCategory())).collect(Collectors.toList());
			List<SY0202> GEMusicSy0202List = Sy0202List.stream().filter(it -> "all".equals(it.getRightsController())).filter(it -> "GEMusic".equals(it.getContentCategory())).collect(Collectors.toList());
			List<SY0202> MusicSy0202List = Sy0202List.stream().filter(it -> "all".equals(it.getRightsController())).filter(it -> "Music".equals(it.getContentCategory())).collect(Collectors.toList());
			// 校验GE \ MUSIC \ GEMUSIC all
			try {
				createListDspFileBaseForAVOD(filepath, "GE", GESy0202List);
				createListDspFileBaseForAVOD(filepath, "GEMusic", GEMusicSy0202List);
				createListDspFileBaseForAVOD(filepath, "Music", MusicSy0202List);
				isParse = true;
			} catch (Exception e) {
				logger.error(e.getMessage());
				e.printStackTrace();
			}
		}
		// 每一个block下 suo302的usages = ru0101的usages,否则提示 文件明细与汇总金额不匹配
//		}else{
//			Sy0401List = Sy0401List.stream().filter(it ->!"all".equals(it.getServiceDescription())).collect(Collectors.toList());// 去除all的数据
//			// 循环Sy0401List 写入listDspFileBase
//			try {
//				createListDspFileBase(listDspFileBaseList);
//				isParse = true;
//			} catch (ParseException e) {
//				logger.error(e.getMessage());
//				e.printStackTrace();
//			}
//		}

//		if(listDspFileBaseList.size()>0){
//			listDspFileBaseService.addList(listDspFileBaseList);
//		}


	}

//	private void createListDspFileBase(List<ListDspFileBase> listDspFileBaseList) throws ParseException {
//		List<ClaimMaxRevenue> claimMaxRevenueList = new ArrayList<>();
//		if(null != Sy0401List && Sy0401List.size() >0){
//			for (Sy0401 sy0401:Sy0401List) {
//				ListDspFileBase listDspFileBase = new ListDspFileBase();
//				listDspFileBase.init();
//				listDspFileBase.setCommercialModel(sy0401.getCommercialModel());
//				listDspFileBase.setCurrency(sy0401.getCurrency());
//				String product = sy0401.getSubscriberType();
//				String startDate = sy0401.getSubPeriodStartDate();
//				String endDate = sy0401.getSubPeriodEndDate();
//				// claim_max_revenue
//				ClaimMaxRevenue claimMaxRevenue = new ClaimMaxRevenue();
//				claimMaxRevenue.init();
//				claimMaxRevenue.setCurrency(sy0401.getCurrency());
//				claimMaxRevenue.setLicensor("MUST");
//				claimMaxRevenue.setStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(startDate));
//				claimMaxRevenue.setEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(endDate));
//				claimMaxRevenue.setProduct(product);
//				claimMaxRevenue.setTerrCode(sy0401.getTerritory());
//				claimMaxRevenue.setSubscriberNumber(new BigDecimal(sy0401.getSubscribers()));
//				claimMaxRevenue.setNetRevenue(StringUtils.isNotBlank(sy0401.getNet())?new BigDecimal(sy0401.getNet()):BigDecimal.ZERO);
//				claimMaxRevenueList.add(claimMaxRevenue);
//				// TODO 产品如何关联明细
//				// claim_minima_info  查看是否存在当前产品  SVOD  先去查询 没有创建，有的话获取id
//				ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId(product, null);
//				if(null == claimMinimaInfo){
//					claimMinimaInfo = new ClaimMinimaInfo();
//					claimMinimaInfo.init();
//					claimMinimaInfo.setCompnyName("YouTuBe");
//					claimMinimaInfo.setProductFullName(product);
//					claimMinimaInfoService.add(claimMinimaInfo);
//				}
//				listDspFileBase.setClaimMinimaInfoId(claimMinimaInfo.getId());
//				listDspFileBaseList.add(listDspFileBase);
//			}
//			claimMaxRevenueService.addList(claimMaxRevenueList);
//		}
//
//	}

	private void createListDspFileBaseForAVOD(String filepath, String type, List<SY0202> sy0202List) throws ParseException {
		if (null != sy0202List && sy0202List.size() > 0) {
			SY0202 sy0202 = sy0202List.get(0);
			ListDspFileBase listDspFileBase = new ListDspFileBase();
			listDspFileBase.init();
			listDspFileBase.setCurrency(sy0202.getCurrency());
			listDspFileBase.setCommercialModel(sy0202.getServiceDescription());
			// 对应的产品需要固定为某一个，默认为数据的
			String usageStartDate = header.getUsageStartDate();
			listDspFileBase.setListFileStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
			String usageEndDate = header.getUsageEndDate();
			listDspFileBase.setListFileEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
//			String fileNumber = header.getFileNumber();
			listDspFileBase.setDspCompany(header.getSenderName());
			listDspFileBase.setDist(0);
			listDspFileBase.setDistStatus(0);
			listDspFileBase.setFilePath(filepath);
//			String fileName = listFileQueue.getFileName();
			String fileName = "DSR_MUST-CS_YouTube_Usage-AdSupport-Music-All_2019-Q4_TW_1of1_20200122T132333.tsv";
			listDspFileBase.setFileName(fileName + "_" + type);
			listDspFileBase.setFileStatus(1);
			listDspFileBase.setCategoryCode(type);
//			listDspFileBase.setFileQuenceId(listFileQueue.getId());
			listDspFileBase.setFileQuenceId(400l);
			String usages = sy0202.getUsages();
			if (StringUtils.isNotBlank(usages)) {
				totalSy0202Usage = totalSy0202Usage.add(new BigDecimal(usages));
				listDspFileBase.setListTotalClickCount(new BigDecimal(usages));
			}
			String numberOfFiles = header.getNumberofFiles();
			if (StringUtils.isNotBlank(numberOfFiles)) {
				listDspFileBase.setListFileTotal(Long.valueOf(numberOfFiles));
			}
			// 此处可能会出现异常，查询出来多个结果！
			ClaimMinimaInfo avod = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId("AVOD", null);
			if (null != avod) {
				listDspFileBase.setClaimMinimaInfoId(avod.getId());
			}
			listDspFileBase.setIsShow(IsShowEnum.show.code());
			listDspFileBase.setDeleted(DeletedEnum.normal.code());
			listDspFileBaseService.add(listDspFileBase);
			connectListDspFileBaseMap.put(type, listDspFileBase.getId());
		}
	}


	private void saveDspFileDataMapping(List<TsvCommonObject> tsvCommonObjectList) {
		List<ListDspFileDataMapping> ldfdmList = new ArrayList<ListDspFileDataMapping>();
		for (TsvCommonObject tsvCommonObject : tsvCommonObjectList
		) {
			// 一个block下的title和subTitle都需要去重后拼接在一起
			tsvCommonObjectChangeToDspFileDataMapping(ldfdmList, tsvCommonObject);
		}
		listDspFileDataMappingService.addList(ldfdmList);
		ldfdmList.clear();
	}

	private void tsvCommonObjectChangeToDspFileDataMapping(List<ListDspFileDataMapping> ldfdmList, TsvCommonObject tsvCommonObject) {
		List<Su0302> su0302List = tsvCommonObject.getSu0302List();
		if (null != su0302List && su0302List.size() > 0) {
			for (Su0302 su0302 : su0302List) {
				ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
				listDspFileDataMapping.init();
				JSONObject object = new JSONObject();
				createListDspFileDataMappingByAs0101(object, listDspFileDataMapping, tsvCommonObject.getAs0101());
				createListDspFileDataMappingByRu0101(object, tsvCommonObject.getRu0101List());
				createListDspFileDataMappingByMw0101(null, object, listDspFileDataMapping, tsvCommonObject.getMw0101());
				createListDspFileDataMappingBySu0302(object, listDspFileDataMapping, su0302);

				// 生成dataUniqueKey  和 dataUniqueKeyStr
				listDspFileDataMapping.setDataUniqueKeyStr(listDspFileDataMapping.getBusinessId());
				listDspFileDataMapping.setDataUniqueKey(Md5.getMd5(listDspFileDataMapping.getDataUniqueKeyStr()));

				List<LI0102> li0102List = su0302.getLi0102List();
				if (null != li0102List && li0102List.size() > 0) {
					List<Map<String, String>> extJsonList = new ArrayList<>();
					for (LI0102 li0102 :
							li0102List) {
						dealLi0102(li0102, extJsonList, listDspFileDataMapping);
					}
					object.put("li0102List", extJsonList);
				}

				List<Mw0101> mw0101List = su0302.getMw0101List();
				if (null != mw0101List && mw0101List.size() > 0) {
					List<Map<String, String>> extJsonList = new ArrayList<>();
					for (Mw0101 mw0101 : mw0101List
					) {
						createListDspFileDataMappingByMw0101(extJsonList, object, listDspFileDataMapping, mw0101);
					}
					object.put("mw0101List", extJsonList);
				}

				listDspFileDataMapping.setExtJson(object.toString());
				ldfdmList.add(listDspFileDataMapping);
			}
		}
	}

	private void dealLi0102(LI0102 li0102, List<Map<String, String>> extJsonList, ListDspFileDataMapping listDspFileDataMapping) {

		Map<String, String> extMap = new HashMap<>();
		if (li0102 == null) {
			logger.info(listDspFileDataMapping.getGroupCode() + "块未找到li0102节点");
			return;
		}

		String allocatedAmount = li0102.getAllocatedAmount();
		if (StringUtils.isNotBlank(allocatedAmount)) {
			extMap.put("AllocatedAmount", allocatedAmount + "");
		}
		String allocatedNetRevenue = li0102.getAllocatedNetRevenue();
		if (StringUtils.isNotBlank(allocatedNetRevenue)) {
			extMap.put("AllocatedNetRevenue", allocatedNetRevenue + "");
		}
		String allocatedUsages = li0102.getAllocatedUsages();
		if (StringUtils.isNotBlank(allocatedUsages)) {
			extMap.put("AllocatedUsages", allocatedUsages + "");
		}

		String li0102Name = li0102.getName();
		if (StringUtils.isNotBlank(li0102Name)) {
			extMap.put("name", "LI01.02");
		}

		String rightsController = li0102.getRightsController();
		if (StringUtils.isNotBlank(rightsController)) {
			extMap.put("RightsController", rightsController);
		}

		String rightsControllerPartyId = li0102.getRightsControllerPartyId();
		if (StringUtils.isNotBlank(rightsControllerPartyId)) {
			extMap.put("RightsControllerPartyId", rightsControllerPartyId);
		}

		String rightsControllerWorkID = li0102.getRightsControllerWorkID();
		if (StringUtils.isNotBlank(rightsControllerWorkID)) {
			extMap.put("RightsControllerWorkID", rightsControllerWorkID);
		}
		String rightShare = li0102.getRightShare();
		extMap.put("RightShare", rightShare);

		String rightType = li0102.getRightType();
		if (StringUtils.isNotBlank(rightType)) {
			extMap.put("RightType", rightType);
			if ("PerformingRight".equals(rightType) && !"Conflict".equals(rightsController)) {
				if (StringUtils.isNotBlank(rightShare)) {
					BigDecimal perRightShare = new BigDecimal(rightShare);
					BigDecimal perShare = listDspFileDataMapping.getPerShare();
					if (null != perShare) {
						perRightShare = perRightShare.add(perShare);
					}
					listDspFileDataMapping.setPerShare(perRightShare);
				}
			}
			if ("MechanicalRight".equals(rightType) && !"Conflict".equals(rightsController)) {
				if (StringUtils.isNotBlank(rightShare)) {
					BigDecimal mecRightShare = new BigDecimal(rightShare);
					BigDecimal mecShare = listDspFileDataMapping.getMecShare();
					if (null != mecShare) {
						mecRightShare = mecRightShare.add(mecShare);
					}
					listDspFileDataMapping.setMecShare(mecRightShare);
				}
			}
		}
		String summaryRecordId = li0102.getSummaryRecordId();
		if (StringUtils.isNotBlank(summaryRecordId)) {
			extMap.put("SummaryRecordId", summaryRecordId);
		}
		extJsonList.add(extMap);
	}

	private void createListDspFileDataMappingBySu0302(JSONObject object, ListDspFileDataMapping listDspFileDataMapping, Su0302 su0302) {

		Map<String, String> extMap = new HashMap<>();
		if (su0302 == null) {

			logger.info(listDspFileDataMapping.getGroupCode() + "块未找到su0302节点");
			return;
		}
		String blockId = su0302.getBlockId();
		if (StringUtils.isNotBlank(blockId)) {
			String groupCode = listDspFileDataMapping.getGroupCode();
			if (!StringUtils.isNotBlank(groupCode)) {
				listDspFileDataMapping.setGroupCode(blockId);
			}
		}

		String contentCategory = su0302.getContentCategory();
		Long listDspFileBaseId = connectListDspFileBaseMap.get(contentCategory);
		listDspFileDataMapping.setFileBaseId(listDspFileBaseId);
		if (StringUtils.isNotBlank(contentCategory)) {
			listDspFileDataMapping.setUsage(contentCategory);
		}

		String dspReleaseId = su0302.getDspReleaseId();
		if (StringUtils.isNotBlank(dspReleaseId)) {
			listDspFileDataMapping.setReleaseId(dspReleaseId);
			listDspFileDataMapping.setBusinessId(dspReleaseId);
		}

		String netRevenue = su0302.getNetRevenue();
		if (StringUtils.isNotBlank(netRevenue)) {
			listDspFileDataMapping.setWorkPrice(new BigDecimal(netRevenue));
		}

		String usages = su0302.getUsages();
		if (StringUtils.isNotBlank(usages)) {
			listDspFileDataMapping.setClickNumber(new BigDecimal(usages));
		}

		String validityPeriodEnd = su0302.getValidityPeriodEnd();
		if (StringUtils.isNotBlank(validityPeriodEnd)) {
			Date endDate = null;
			try {
				endDate = new SimpleDateFormat("yyyy-MM-dd").parse(validityPeriodEnd);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			listDspFileDataMapping.setListFileEndTime(endDate);
		}
		String validityPeriodStart = su0302.getValidityPeriodStart();
		if (StringUtils.isNotBlank(validityPeriodStart)) {
			Date startDate = null;
			try {
				startDate = new SimpleDateFormat("yyyy-MM-dd").parse(validityPeriodStart);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			listDspFileDataMapping.setListFileStartTime(startDate);
		}

		String isRoyaltyBearing = su0302.getIsRoyaltyBearing();
		if (StringUtils.isNotBlank(isRoyaltyBearing)) {
			extMap.put("IsRoyaltyBearing", isRoyaltyBearing);
		}

		String su0302Name = su0302.getName();
		if (StringUtils.isNotBlank(su0302Name)) {
			extMap.put("name", su0302Name);
		}

		String salesTransactionId = su0302.getSalesTransactionId();
		if (StringUtils.isNotBlank(salesTransactionId)) {
			extMap.put("SalesTransactionId", salesTransactionId);
		}

		String summaryRecordId = su0302.getSummaryRecordId();
		if (StringUtils.isNotBlank(summaryRecordId)) {
			extMap.put("SummaryRecordId", summaryRecordId);
		}

		object.put("su0302", extMap);
	}

	private void createListDspFileDataMappingByMw0101(List<Map<String, String>> extJsonList, JSONObject object, ListDspFileDataMapping listDspFileDataMapping, Mw0101 mw0101) {

		Map<String, String> extMap = new HashMap<>();
		if (mw0101 == null) {
			logger.info(listDspFileDataMapping.getGroupCode() + "块未找到mw0101节点");
			return;
		}

		String composerAuthor = mw0101.getComposerAuthor();
		if (StringUtils.isNotBlank(composerAuthor) && !"-".equals(composerAuthor)) {
			String composer = listDspFileDataMapping.getComposer();
			if (StringUtils.isNotBlank(composer) && !composer.contains(composerAuthor)) {
				composerAuthor = composer + ";" + composerAuthor;
			}
			listDspFileDataMapping.setComposer(composerAuthor);
			String author = listDspFileDataMapping.getAuthor();
			if (StringUtils.isNotBlank(author) && !author.contains(composerAuthor)) {
				composerAuthor = author + ";" + composerAuthor;
			}
			listDspFileDataMapping.setAuthor(composerAuthor);
		}

		String iswc = mw0101.getISWC();
		if (StringUtils.isNotBlank(iswc) && !"-".equals(iswc)) {
			String iswcExisted = listDspFileDataMapping.getIswc();
			if (StringUtils.isNotBlank(iswcExisted) && !iswc.contains(iswcExisted)) {
				iswc = iswcExisted + ";" + iswc;
			}
			listDspFileDataMapping.setIswc(iswc);
		}

		String musicPublisher = mw0101.getMusicPublisher();
		if (StringUtils.isNotBlank(musicPublisher)) {
			listDspFileDataMapping.setPublisher(musicPublisher);
		}

		String mw0101SubTitle = mw0101.getSubTitle();
		if (StringUtils.isNotBlank(mw0101SubTitle) && !"-".equals(mw0101SubTitle)) {
			String albumTitle = listDspFileDataMapping.getAlbumTitle();
			if (StringUtils.isNotBlank(albumTitle) && !albumTitle.contains(mw0101SubTitle)) {
				mw0101SubTitle = albumTitle + ";" + mw0101SubTitle;
			}
			listDspFileDataMapping.setAlbumTitle(mw0101SubTitle);
		}
		String mw0101Title = mw0101.getTitle();
		if (StringUtils.isNotBlank(mw0101Title) && !"-".equals(mw0101Title)) {
			String title = listDspFileDataMapping.getTitle();
			if (StringUtils.isNotBlank(title) && !title.contains(mw0101Title)) {
				mw0101Title = title + ";" + mw0101Title;
			}
			listDspFileDataMapping.setTitle(mw0101Title);
		}

		if (object.get("mw0101") == null) {
			String arranger = mw0101.getArranger();
			if (StringUtils.isNotBlank(arranger)) {
				extMap.put("Arranger", arranger);
			}

			String arrangerPartyId = mw0101.getArrangerPartyId();
			if (StringUtils.isNotBlank(arrangerPartyId)) {
				extMap.put("ArrangerPartyId", arrangerPartyId);
			}

			String composerAuthorPartyId = mw0101.getComposerAuthorPartyId();
			if (StringUtils.isNotBlank(composerAuthorPartyId)) {
				extMap.put("ComposerAuthorPartyId", composerAuthorPartyId);
			}

			String dataProvider = mw0101.getDataProvider();
			if (StringUtils.isNotBlank(dataProvider)) {
				extMap.put("DataProvider", dataProvider);
			}

			String musicPublisherPartyId = mw0101.getMusicPublisherPartyId();
			if (StringUtils.isNotBlank(musicPublisherPartyId)) {
				extMap.put("MusicPublisherPartyId", musicPublisherPartyId);
			}

			String mw0101Name = mw0101.getName();
			if (StringUtils.isNotBlank(mw0101Name)) {
				extMap.put("name", mw0101Name);
			}

			String proprietaryWorkId = mw0101.getProprietaryWorkId();
			if (StringUtils.isNotBlank(proprietaryWorkId)) {
				extMap.put("ProprietaryWorkId", proprietaryWorkId);
			}

			String workContributor = mw0101.getWorkContributor();
			if (StringUtils.isNotBlank(workContributor)) {
				extMap.put("WorkContributor", workContributor);
			}
			String workContributorPartyId = mw0101.getWorkContributorPartyId();
			if (StringUtils.isNotBlank(workContributorPartyId)) {
				extMap.put("WorkContributorPartyId", workContributorPartyId);
			}

			String dspWorkId = mw0101.getDspWorkId();
			if (StringUtils.isNotBlank(dspWorkId) && !"-".equals(dspWorkId)) {
				extMap.put("WorkContributorPartyId", dspWorkId);
			}
			if (null == extJsonList) {
				object.put("mw0101", extMap);
			} else {
				extJsonList.add(extMap);
			}
		}
	}

	private void createListDspFileDataMappingByRu0101(JSONObject object, List<Ru0101> ru0101List) {
		if (null != ru0101List && ru0101List.size() > 0) {
			List<Map<String, String>> extJsonList = new ArrayList<>();
			if (null == object.get("ru0101List")) {
				for (Ru0101 ru0101 : ru0101List
				) {
					Map<String, String> extMap = new HashMap<>();
					String summaryRecordId = ru0101.getSummaryRecordId();
					extMap.put("SummaryRecordId", summaryRecordId);
					String usages = ru0101.getUsages();
					if (StringUtils.isNotBlank(usages)) {
						BigDecimal totalUsages = BigDecimal.ZERO;
						String[] split = usages.split("\\|");
						if (split.length > 1) {
							for (String key : split
							) {
								totalUsages.add(new BigDecimal(key));
							}
						} else {
							totalUsages = new BigDecimal(split[0]);
						}
						extMap.put("FormatUsages", totalUsages.toString());
						totalRu0101Usage = totalRu0101Usage.add(totalUsages);
					}
					extMap.put("Usages", usages);

					String contentCategory = ru0101.getContentCategory();
					if ("Music".equals(contentCategory)) {
						extMap.put("Music", contentCategory);
					}
					if ("GEMusic".equals(contentCategory)) {
						extMap.put("GEMusic", contentCategory);
					}
					if ("GE".equals(contentCategory)) {
						extMap.put("GE", contentCategory);
					}
					extJsonList.add(extMap);
				}
			}
			object.put("ru0101List", extJsonList);
		}
	}

	private void createListDspFileDataMappingByAs0101(JSONObject object, ListDspFileDataMapping listDspFileDataMapping, As0101 as0101) {
		listDspFileDataMapping.setGroupCode(as0101.getBlockId());// 用于分组
		listDspFileDataMapping.setIsrc(as0101.getISRC());
		listDspFileDataMapping.setResourceId(as0101.getDspResourceId());
		listDspFileDataMapping.setWorkArtist(as0101.getDisplayArtistName());
		listDspFileDataMapping.setDurationStr(as0101.getDuration());
		listDspFileDataMapping.setDurationM(StringUtils.isNotBlank(as0101.getDurationM()) ? Integer.valueOf(as0101.getDurationM()) : null);
		listDspFileDataMapping.setDurationS(StringUtils.isNotBlank(as0101.getDurationS()) ? Integer.valueOf(as0101.getDurationS()) : null);
		listDspFileDataMapping.setReleaseType(as0101.getResourceType());
		String as0101Title = as0101.getTitle();
		if (StringUtils.isNotBlank(as0101Title) && !"-".equals(as0101Title)) {
			listDspFileDataMapping.setTitle(as0101Title);
		}
		String as0101SubTitle = as0101.getSubTitle();
		if (StringUtils.isNotBlank(as0101SubTitle) && !"-".equals(as0101SubTitle)) {
			listDspFileDataMapping.setAlbumTitle(as0101SubTitle);
		}
		if (null == object.get("as0101")) {
			Map<String, String> extMap = new HashMap<>();
			extMap.put("name", "AS0101");

			String resourceReference = as0101.getResourceReference();
			extMap.put("ResourceReference", resourceReference);

			String displayArtistPartyId = as0101.getDisplayArtistPartyId();
			extMap.put("DisplayArtistPartyId", displayArtistPartyId);

			String isMasterRecording = as0101.getIsMasterRecording();
			extMap.put("IsMasterRecording", isMasterRecording);
			object.put("as0101", extMap);
		}
	}


	/**
	 * @param split
	 * @param listMapKey 里面存的是 map的key
	 * @return
	 */
	private static Map<String, String> getMapOfSplit(Map<String, String> map, String[] split, List<String> listMapKey) {
		for (int i = 0; i < listMapKey.size(); i++) {
			map.put(listMapKey.get(i), split[i]);
		}
		return map;
	}

	/**
	 * 将map的key 封装到list中
	 */

	private static void dealWithString(String line, Map<String, List<String>> map) {
		String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
		String key = split[0].replace("#", "");
		List<String> listMapKey = new ArrayList<>();
		for (String s : split) {
			if (s.contains(key))
				s = key;
			listMapKey.add(s);
		}
		map.put(key, listMapKey);
	}

	private Date dateOfStr(String date) {
		DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
		try {
			return format1.parse(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void main(String[] args) {

		ParseYoutubeTsvServiceOld ps = new ParseYoutubeTsvServiceOld();
		try {
			ps.parse("C:\\Users\\<USER>\\Desktop\\must\\youtubeTsv\\DSR_MUST-CS_YouTube_Usage-Subscription-Music_2019-Q4_TW_1of1_20200122T122033.tsv");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
