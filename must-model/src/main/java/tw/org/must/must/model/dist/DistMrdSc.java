package tw.org.must.must.model.dist;

import org.bouncycastle.asn1.cms.PasswordRecipientInfo;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Table(name = "`dist_mrd_sc`")
public class DistMrdSc extends BaseEntity {

    /**
     * 授权编号
     */
    @Column(name = "`sc_no`")
    private String scNo;

    /**
     * 
     */
    @Column(name = "`sc_title_en`")
    private String scTitleEn;

    /**
     * 
     */
    @Column(name = "`sc_title_ch`")
    private String scTitleCh;

    /**
     * label 表id，根据该id关联上产品
     */
    @Column(name = "`label_id`")
    private Long labelId;

    /**
     * sc_type表code
     */
    @Column(name = "`sc_type_code`")
    private String scTypeCode;

    /**
     * 授权作品数量
     */
    @Column(name = "`tracks`")
    private Integer tracks;

    /**
     * 时长分
     */
    @Column(name = "`duration_m`")
    private Integer durationM;

    /**
     * 时长秒
     */
    @Column(name = "`duration_s`")
    private Integer durationS;

    /**
     * 发行时间
     */
    @Column(name = "`release_date`")
    private Date releaseDate;

    /**
     * ref_country表国家code
     */
    @Column(name = "`country_code`")
    private String countryCode;

    /**
     * 还不确定用途，先不使用
     */
    @Column(name = "`bar_code`")
    private String barCode;

    /**
     * 授权时间
     */
    @Column(name = "`receive_date`")
    private Date receiveDate;

    /**
     * 创建用户
     */
    @Column(name = "`create_user`")
    private String createUser;

    /**
     * 修改用户
     */
    @Column(name = "`amend_user`")
    private String amendUser;

    @Column(name = "producer_code")
    private String producerCode;

    @Transient
    private String producerName;

    @Transient
    private List<DistMrdScWork> distMrdScWorkList;

    /**
     * ref_country表国家name
     */
    @Transient
    private String countryName;

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public List<DistMrdScWork> getDistMrdScWorkList() {
        return distMrdScWorkList;
    }

    public void setDistMrdScWorkList(List<DistMrdScWork> distMrdScWorkList) {
        this.distMrdScWorkList = distMrdScWorkList;
    }

    public String getProducerName() {
        return producerName;
    }

    public void setProducerName(String producerName) {
        this.producerName = producerName;
    }

    public String getProducerCode() {
        return producerCode;
    }

    public void setProducerCode(String producerCode) {
        this.producerCode = producerCode;
    }

    public String getScNo() {
        return scNo;
    }

    public void setScNo(String scNo) {
        this.scNo = scNo;
    }

    public String getScTitleEn() {
        return scTitleEn;
    }

    public void setScTitleEn(String scTitleEn) {
        this.scTitleEn = scTitleEn;
    }

    public String getScTitleCh() {
        return scTitleCh;
    }

    public void setScTitleCh(String scTitleCh) {
        this.scTitleCh = scTitleCh;
    }

    public Long getLabelId() {
        return labelId;
    }

    public void setLabelId(Long labelId) {
        this.labelId = labelId;
    }

    public String getScTypeCode() {
        return scTypeCode;
    }

    public void setScTypeCode(String scTypeCode) {
        this.scTypeCode = scTypeCode;
    }

    public Integer getTracks() {
        return tracks;
    }

    public void setTracks(Integer tracks) {
        this.tracks = tracks;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public Date getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getAmendUser() {
        return amendUser;
    }

    public void setAmendUser(String amendUser) {
        this.amendUser = amendUser;
    }



}