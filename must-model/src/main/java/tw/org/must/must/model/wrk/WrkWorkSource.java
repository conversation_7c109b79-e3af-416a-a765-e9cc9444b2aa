package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`wrk_work_source`")
public class WrkWorkSource extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 作品编号
     */
    @Column(name = "work_id")
    @NotNull(message="WrkWorkSource中的workId不能为空！")
    private Long workId;

    /**
     * 作品所属协会
     */
    @Column(name = "work_society_code")
    @NotNull(message="WrkWorkSource中的workSocietyCode不能为空！")
    private Integer workSocietyCode;

    /**
     * 来源类别，Agreement(AGR), electronic work reg(EWR), Fiche international(FIC), Internet(WEB), Letter(LTR), Monitor(MON), Notifi. card(NOC), progm.return(PRE), Cue sheet(CUE), Record label(LAB), Unknown(UNC), WWL(WWL), Work Info Database(WID), Common Wrk Reg(CWR),
     */
    @Column(name = "source_type")
    @NotBlank(message="sourceType不能为空！")
    private String sourceType;

    /**
     * 通知来源ip name no，Income source can be a society, an IP or a producer
     */
    @Column(name = "notify_souce_id")
    @NotBlank(message="notifySouceId不能为空！")
    private String notifySouceId;

    /**
     * 通知时间
     */
    @Column(name = "notify_date")
    @NotBlank(message="notifyDate不能为空！")
    private Date notifyDate;

    /**
     * 填写协会
     */
    @Column(name = "input_soc")
    private Integer inputSoc;
    
    /**
     * 协会名称或者会员名称  与notifySouceId有关
     */
    @Column(name = "name")
    private String name;
    
    @Column(name="work_unique_key")
    private String workUniqueKey;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getNotifySouceId() {
        return notifySouceId;
    }

    public void setNotifySouceId(String notifySouceId) {
        this.notifySouceId = notifySouceId;
    }

    public Date getNotifyDate() {
        return notifyDate;
    }

    public void setNotifyDate(Date notifyDate) {
        this.notifyDate = notifyDate;
    }

    public Integer getInputSoc() {
        return inputSoc;
    }

    public void setInputSoc(Integer inputSoc) {
        this.inputSoc = inputSoc;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

}