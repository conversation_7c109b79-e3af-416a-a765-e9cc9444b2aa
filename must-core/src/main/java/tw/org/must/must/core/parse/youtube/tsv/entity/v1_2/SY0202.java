package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class SY0202  extends BaseTsvEntity{
    private  String SummaryRecordId;
    private  String DistributionChannel;
    private  String DistributionChannelDPID;
    private  String CommercialModel;
    private  String UseType;
    private  String Territory;
    private  String ServiceDescription;
    private  String Usages;
    private  String Users;
    private  String Currency;
    private  String NetRevenue;
    private  String RightsController;
    private  String RightsControllerPartyId;
    private  String AllocatedUsages;
    private  String AllocatedRevenue;
    private  String AllocatedNetRevenue;
    private  String RightsType;
    private  String ContentCategory;
    private  String ExchangeRateBaseCurrency;
    private  String ExchangeRate;
    private  String RightsTypePercentage;


    public String getRightsTypePercentage() {
        return RightsTypePercentage;
    }

    public void setRightsTypePercentage(String rightsTypePercentage) {
        RightsTypePercentage = rightsTypePercentage;
    }

    public String getExchangeRate() {
        return ExchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        ExchangeRate = exchangeRate;
    }

    public String getExchangeRateBaseCurrency() {
        return ExchangeRateBaseCurrency;
    }

    public void setExchangeRateBaseCurrency(String exchangeRateBaseCurrency) {
        ExchangeRateBaseCurrency = exchangeRateBaseCurrency;
    }

    public String getSummaryRecordId() {
        return SummaryRecordId;
    }

    public void setSummaryRecordId(String summaryRecordId) {
        SummaryRecordId = summaryRecordId;
    }

    public String getDistributionChannel() {
        return DistributionChannel;
    }

    public void setDistributionChannel(String distributionChannel) {
        DistributionChannel = distributionChannel;
    }

    public String getDistributionChannelDPID() {
        return DistributionChannelDPID;
    }

    public void setDistributionChannelDPID(String distributionChannelDPID) {
        DistributionChannelDPID = distributionChannelDPID;
    }

    public String getCommercialModel() {
        return CommercialModel;
    }

    public void setCommercialModel(String commercialModel) {
        CommercialModel = commercialModel;
    }

    public String getUseType() {
        return UseType;
    }

    public void setUseType(String useType) {
        UseType = useType;
    }

    public String getTerritory() {
        return Territory;
    }

    public void setTerritory(String territory) {
        Territory = territory;
    }

    public String getServiceDescription() {
        return ServiceDescription;
    }

    public void setServiceDescription(String serviceDescription) {
        ServiceDescription = serviceDescription;
    }

    public String getUsages() {
        return Usages;
    }

    public void setUsages(String usages) {
        Usages = usages;
    }

    public String getUsers() {
        return Users;
    }

    public void setUsers(String users) {
        Users = users;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public String getNetRevenue() {
        return NetRevenue;
    }

    public void setNetRevenue(String netRevenue) {
        NetRevenue = netRevenue;
    }

    public String getRightsController() {
        return RightsController;
    }

    public void setRightsController(String rightsController) {
        RightsController = rightsController;
    }

    public String getRightsControllerPartyId() {
        return RightsControllerPartyId;
    }

    public void setRightsControllerPartyId(String rightsControllerPartyId) {
        RightsControllerPartyId = rightsControllerPartyId;
    }

    public String getAllocatedUsages() {
        return AllocatedUsages;
    }

    public void setAllocatedUsages(String allocatedUsages) {
        AllocatedUsages = allocatedUsages;
    }

    public String getAllocatedRevenue() {
        return AllocatedRevenue;
    }

    public void setAllocatedRevenue(String allocatedRevenue) {
        AllocatedRevenue = allocatedRevenue;
    }

    public String getAllocatedNetRevenue() {
        return AllocatedNetRevenue;
    }

    public void setAllocatedNetRevenue(String allocatedNetRevenue) {
        AllocatedNetRevenue = allocatedNetRevenue;
    }

    public String getRightsType() {
        return RightsType;
    }

    public void setRightsType(String rightsType) {
        RightsType = rightsType;
    }

    public String getContentCategory() {
        return ContentCategory;
    }

    public void setContentCategory(String contentCategory) {
        ContentCategory = contentCategory;
    }

}
