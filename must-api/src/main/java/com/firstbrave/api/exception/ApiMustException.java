package com.firstbrave.api.exception;

/**
 * @description:
 * @author: handa
 * @time: 2019/11/11 10:50
 */
public class ApiMustException extends RuntimeException {

    private int errCode;
    private String fieldName;
    private Object fieldValue;
    private String errorMessage;
    public ApiMustException() {
        super();
    }

    public ApiMustException(int code,String fieldName,Object fieldValue,String errorMessage) {
        super(errorMessage);
        this.errCode = code;
        this.errorMessage =errorMessage;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }
    public ApiMustException(int code, String msg) {
        super(msg);
        this.errCode = code;
    }

    public ApiMustException(String msg) {
        super(msg);
    }
    public int getErrCode() {
        return errCode;
    }

    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }

    @Override
    public String toString() {
        return "{" +
                "errCode=" + errCode +
                ", fieldName='" + fieldName + '\'' +
                ", fieldValue=" + fieldValue +
                ", errorMessage='" + errorMessage + '\'' +
                "} ";
    }
}
