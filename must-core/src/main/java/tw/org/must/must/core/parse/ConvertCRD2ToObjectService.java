package tw.org.must.must.core.parse;

import com.firstbrave.api.base.CrdRecord;
import com.firstbrave.api.crd.vo.*;
import com.firstbrave.api.crd.vo.v3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.mapper.crd.*;
import tw.org.must.must.model.crd.*;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;

import java.util.List;

@Service
public class ConvertCRD2ToObjectService {

    private final Crd2AhpHdrMapper crd2AhpHdrMapper;
    private final Crd2CtlGrhMapper crd2CtlGrhMapper;
    private final Crd2CtlGrtMapper crd2CtlGrtMapper;
    private final Crd2CtlHdrMapper crd2CtlHdrMapper;
    private final Crd2CtlTrlMapper crd2CtlTrlMapper;
    private final Crd2MwnDtlAdjMapper crd2MwnDtlAdjMapper;
    private final Crd2MwnDtlFeoMapper crd2MwnDtlFeoMapper;
    private final Crd2MwnDtlIccMapper crd2MwnDtlIccMapper;
    private final Crd2MwnDtlMdrMapper crd2MwnDtlMdrMapper;
    private final Crd2MwnDtlMdsMapper crd2MwnDtlMdsMapper;
    private final Crd2MwnDtlMdtMapper crd2MwnDtlMdtMapper;
    private final Crd2MwnDtlMipMapper crd2MwnDtlMipMapper;
    private final Crd2MwnDtlOipMapper crd2MwnDtlOipMapper;
    private final Crd2MwnDtlRrpMapper crd2MwnDtlRrpMapper;
    private final Crd2MwnDtlWbiMapper crd2MwnDtlWbiMapper;
    private final Crd2MwnDtlWeaMapper crd2MwnDtlWeaMapper;
    private final Crd2MwnDtlWepMapper crd2MwnDtlWepMapper;
    private final Crd2MwnDtlWuiMapper crd2MwnDtlWuiMapper;
    private final Crd2MwnHdrMapper crd2MwnHdrMapper;
    private final Crd2SdnDtlApiMapper crd2SdnDtlApiMapper;
    private final Crd2SdnDtlCcrMapper crd2SdnDtlCcrMapper;
    private final Crd2SdnDtlEsiMapper crd2SdnDtlEsiMapper;
    private final Crd2SdnDtlIpiMapper crd2SdnDtlIpiMapper;
    private final Crd2SdnDtlRpiMapper crd2SdnDtlRpiMapper;
    private final Crd2SdnDtlTdiMapper crd2SdnDtlTdiMapper;
    private final Crd2SdnHdrMapper crd2SdnHdrMapper;
    private final Crd2SinDtlSidMapper crd2SinDtlSidMapper;
    private final Crd2SinHdrMapper crd2SinHdrMapper;
    private final Crd3AckDtlSpwMapper crd3AckDtlSpwMapper;
    private final Crd3AckDtlUpaMapper crd3AckDtlUpaMapper;
    private final Crd3AckHdrMapper crd3AckHdrMapper;
    private final Crd3AhpHdrMapper crd3AhpHdrMapper;
    private final Crd3CtlGrhMapper crd3CtlGrhMapper;
    private final Crd3CtlGrtMapper crd3CtlGrtMapper;
    private final Crd3CtlHdrMapper crd3CtlHdrMapper;
    private final Crd3CtlTrlMapper crd3CtlTrlMapper;
    private final Crd3MwnDtlAdjMapper crd3MwnDtlAdjMapper;
    private final Crd3MwnDtlFeoMapper crd3MwnDtlFeoMapper;
    private final Crd3MwnDtlIccMapper crd3MwnDtlIccMapper;
    private final Crd3MwnDtlMdrMapper crd3MwnDtlMdrMapper;
    private final Crd3MwnDtlMdsMapper crd3MwnDtlMdsMapper;
    private final Crd3MwnDtlMdtMapper crd3MwnDtlMdtMapper;
    private final Crd3MwnDtlMipMapper crd3MwnDtlMipMapper;
    private final Crd3MwnDtlOipMapper crd3MwnDtlOipMapper;
    private final Crd3MwnDtlWbiMapper crd3MwnDtlWbiMapper;
    private final Crd3MwnDtlWeaMapper crd3MwnDtlWeaMapper;
    private final Crd3MwnDtlWepMapper crd3MwnDtlWepMapper;
    private final Crd3MwnDtlWerMapper crd3MwnDtlWerMapper;
    private final Crd3MwnDtlWuiMapper crd3MwnDtlWuiMapper;
    private final Crd3MwnHdrMapper crd3MwnHdrMapper;
    private final Crd3RgtHdrMapper crd3RgtHdrMapper;
    private final Crd3SdnDtlApiMapper crd3SdnDtlApiMapper;
    private final Crd3SdnDtlCcrMapper crd3SdnDtlCcrMapper;
    private final Crd3SdnDtlEsiMapper crd3SdnDtlEsiMapper;
    private final Crd3SdnDtlIpiMapper crd3SdnDtlIpiMapper;
    private final Crd3SdnDtlRpiMapper crd3SdnDtlRpiMapper;
    private final Crd3SdnDtlTdiMapper crd3SdnDtlTdiMapper;
    private final Crd3SdnHdrMapper crd3SdnHdrMapper;
    private final Crd3SinDtlSidMapper crd3SinDtlSidMapper;
    private final Crd3SinHdrMapper crd3SinHdrMapper;
    private final Crd2MwnDtlWerMapper crd2MwnDtlWerMapper;

    @Autowired
    private ConvertCRD2ToObjectService(Crd2AhpHdrMapper crd2AhpHdrMapper,
                                       Crd2CtlGrhMapper crd2CtlGrhMapper,
                                       Crd2CtlGrtMapper crd2CtlGrtMapper,
                                       Crd2CtlHdrMapper crd2CtlHdrMapper,
                                       Crd2CtlTrlMapper crd2CtlTrlMapper,
                                       Crd2MwnDtlAdjMapper crd2MwnDtlAdjMapper,
                                       Crd2MwnDtlFeoMapper crd2MwnDtlFeoMapper,
                                       Crd2MwnDtlIccMapper crd2MwnDtlIccMapper,
                                       Crd2MwnDtlMdrMapper crd2MwnDtlMdrMapper,
                                       Crd2MwnDtlMdsMapper crd2MwnDtlMdsMapper,
                                       Crd2MwnDtlMdtMapper crd2MwnDtlMdtMapper,
                                       Crd2MwnDtlMipMapper crd2MwnDtlMipMapper,
                                       Crd2MwnDtlOipMapper crd2MwnDtlOipMapper,
                                       Crd2MwnDtlRrpMapper crd2MwnDtlRrpMapper,
                                       Crd2MwnDtlWbiMapper crd2MwnDtlWbiMapper,
                                       Crd2MwnDtlWeaMapper crd2MwnDtlWeaMapper,
                                       Crd2MwnDtlWepMapper crd2MwnDtlWepMapper,
                                       Crd2MwnDtlWuiMapper crd2MwnDtlWuiMapper,
                                       Crd2MwnHdrMapper crd2MwnHdrMapper,
                                       Crd2SdnDtlApiMapper crd2SdnDtlApiMapper,
                                       Crd2SdnDtlCcrMapper crd2SdnDtlCcrMapper,
                                       Crd2SdnDtlEsiMapper crd2SdnDtlEsiMapper,
                                       Crd2SdnDtlIpiMapper crd2SdnDtlIpiMapper,
                                       Crd2SdnDtlRpiMapper crd2SdnDtlRpiMapper,
                                       Crd2SdnDtlTdiMapper crd2SdnDtlTdiMapper,
                                       Crd2SdnHdrMapper crd2SdnHdrMapper,
                                       Crd2SinDtlSidMapper crd2SinDtlSidMapper,
                                       Crd2SinHdrMapper crd2SinHdrMapper,
                                       Crd3AckDtlSpwMapper crd3AckDtlSpwMapper,
                                       Crd3AckDtlUpaMapper crd3AckDtlUpaMapper,
                                       Crd3AckHdrMapper crd3AckHdrMapper,
                                       Crd3AhpHdrMapper crd3AhpHdrMapper,
                                       Crd3CtlGrhMapper crd3CtlGrhMapper,
                                       Crd3CtlGrtMapper crd3CtlGrtMapper,
                                       Crd3CtlHdrMapper crd3CtlHdrMapper,
                                       Crd3CtlTrlMapper crd3CtlTrlMapper,
                                       Crd3MwnDtlAdjMapper crd3MwnDtlAdjMapper,
                                       Crd3MwnDtlFeoMapper crd3MwnDtlFeoMapper,
                                       Crd3MwnDtlIccMapper crd3MwnDtlIccMapper,
                                       Crd3MwnDtlMdrMapper crd3MwnDtlMdrMapper,
                                       Crd3MwnDtlMdsMapper crd3MwnDtlMdsMapper,
                                       Crd3MwnDtlMdtMapper crd3MwnDtlMdtMapper,
                                       Crd3MwnDtlMipMapper crd3MwnDtlMipMapper,
                                       Crd3MwnDtlOipMapper crd3MwnDtlOipMapper,
                                       Crd3MwnDtlWbiMapper crd3MwnDtlWbiMapper,
                                       Crd3MwnDtlWeaMapper crd3MwnDtlWeaMapper,
                                       Crd3MwnDtlWepMapper crd3MwnDtlWepMapper,
                                       Crd3MwnDtlWerMapper crd3MwnDtlWerMapper,
                                       Crd3MwnDtlWuiMapper crd3MwnDtlWuiMapper,
                                       Crd3MwnHdrMapper crd3MwnHdrMapper,
                                       Crd3RgtHdrMapper crd3RgtHdrMapper,
                                       Crd3SdnDtlApiMapper crd3SdnDtlApiMapper,
                                       Crd3SdnDtlCcrMapper crd3SdnDtlCcrMapper,
                                       Crd3SdnDtlEsiMapper crd3SdnDtlEsiMapper,
                                       Crd3SdnDtlIpiMapper crd3SdnDtlIpiMapper,
                                       Crd3SdnDtlRpiMapper crd3SdnDtlRpiMapper,
                                       Crd3SdnDtlTdiMapper crd3SdnDtlTdiMapper,
                                       Crd3SdnHdrMapper crd3SdnHdrMapper,
                                       Crd3SinDtlSidMapper crd3SinDtlSidMapper,
                                       Crd3SinHdrMapper crd3SinHdrMapper,
                                       Crd2MwnDtlWerMapper crd2MwnDtlWerMapper){
        this.crd2AhpHdrMapper = crd2AhpHdrMapper;
        this.crd2CtlGrhMapper = crd2CtlGrhMapper;
        this.crd2CtlGrtMapper = crd2CtlGrtMapper;
        this.crd2CtlHdrMapper = crd2CtlHdrMapper;
        this.crd2CtlTrlMapper = crd2CtlTrlMapper;
        this.crd2MwnDtlAdjMapper = crd2MwnDtlAdjMapper;
        this.crd2MwnDtlFeoMapper = crd2MwnDtlFeoMapper;
        this.crd2MwnDtlIccMapper = crd2MwnDtlIccMapper;
        this.crd2MwnDtlMdrMapper = crd2MwnDtlMdrMapper;
        this.crd2MwnDtlMdsMapper = crd2MwnDtlMdsMapper;
        this.crd2MwnDtlMdtMapper = crd2MwnDtlMdtMapper;
        this.crd2MwnDtlMipMapper = crd2MwnDtlMipMapper;
        this.crd2MwnDtlOipMapper = crd2MwnDtlOipMapper;
        this.crd2MwnDtlRrpMapper = crd2MwnDtlRrpMapper;
        this.crd2MwnDtlWbiMapper = crd2MwnDtlWbiMapper;
        this.crd2MwnDtlWeaMapper = crd2MwnDtlWeaMapper;
        this.crd2MwnDtlWepMapper = crd2MwnDtlWepMapper;
        this.crd2MwnDtlWuiMapper = crd2MwnDtlWuiMapper;
        this.crd2MwnHdrMapper = crd2MwnHdrMapper;
        this.crd2SdnDtlApiMapper = crd2SdnDtlApiMapper;
        this.crd2SdnDtlCcrMapper = crd2SdnDtlCcrMapper;
        this.crd2SdnDtlEsiMapper = crd2SdnDtlEsiMapper;
        this.crd2SdnDtlIpiMapper = crd2SdnDtlIpiMapper;
        this.crd2SdnDtlRpiMapper = crd2SdnDtlRpiMapper;
        this.crd2SdnDtlTdiMapper = crd2SdnDtlTdiMapper;
        this.crd2SdnHdrMapper = crd2SdnHdrMapper;
        this.crd2SinDtlSidMapper = crd2SinDtlSidMapper;
        this.crd2SinHdrMapper = crd2SinHdrMapper;
        this.crd3AckDtlSpwMapper = crd3AckDtlSpwMapper;
        this.crd3AckDtlUpaMapper = crd3AckDtlUpaMapper;
        this.crd3AckHdrMapper = crd3AckHdrMapper;
        this.crd3AhpHdrMapper = crd3AhpHdrMapper;
        this.crd3CtlGrhMapper = crd3CtlGrhMapper;
        this.crd3CtlGrtMapper = crd3CtlGrtMapper;
        this.crd3CtlHdrMapper = crd3CtlHdrMapper;
        this.crd3CtlTrlMapper = crd3CtlTrlMapper;
        this.crd3MwnDtlAdjMapper = crd3MwnDtlAdjMapper;
        this.crd3MwnDtlFeoMapper = crd3MwnDtlFeoMapper;
        this.crd3MwnDtlIccMapper = crd3MwnDtlIccMapper;
        this.crd3MwnDtlMdrMapper = crd3MwnDtlMdrMapper;
        this.crd3MwnDtlMdsMapper = crd3MwnDtlMdsMapper;
        this.crd3MwnDtlMdtMapper = crd3MwnDtlMdtMapper;
        this.crd3MwnDtlMipMapper = crd3MwnDtlMipMapper;
        this.crd3MwnDtlOipMapper = crd3MwnDtlOipMapper;
        this.crd3MwnDtlWbiMapper = crd3MwnDtlWbiMapper;
        this.crd3MwnDtlWeaMapper = crd3MwnDtlWeaMapper;
        this.crd3MwnDtlWepMapper = crd3MwnDtlWepMapper;
        this.crd3MwnDtlWerMapper = crd3MwnDtlWerMapper;
        this.crd3MwnDtlWuiMapper = crd3MwnDtlWuiMapper;
        this.crd3MwnHdrMapper = crd3MwnHdrMapper;
        this.crd3RgtHdrMapper = crd3RgtHdrMapper;
        this.crd3SdnDtlApiMapper = crd3SdnDtlApiMapper;
        this.crd3SdnDtlCcrMapper = crd3SdnDtlCcrMapper;
        this.crd3SdnDtlEsiMapper = crd3SdnDtlEsiMapper;
        this.crd3SdnDtlIpiMapper = crd3SdnDtlIpiMapper;
        this.crd3SdnDtlRpiMapper = crd3SdnDtlRpiMapper;
        this.crd3SdnDtlTdiMapper = crd3SdnDtlTdiMapper;
        this.crd3SdnHdrMapper = crd3SdnHdrMapper;
        this.crd3SinDtlSidMapper = crd3SinDtlSidMapper;
        this.crd3SinHdrMapper = crd3SinHdrMapper;
        this.crd2MwnDtlWerMapper = crd2MwnDtlWerMapper;
    }




    public void convert(List<CrdRecord> crdRecordList, ListOverseasFileBase listOverseasFileBase){

        for(CrdRecord crdRecord : crdRecordList){
            if( crdRecord instanceof TransmissionHeader){
                TransmissionHeader transmissionHeader = (TransmissionHeader) crdRecord;
                Crd2CtlHdr crd = new Crd2CtlHdr();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setFileLineNo(transmissionHeader.getLine());
                crd.setFileLine(transmissionHeader.toString());
                crd.setFileCreationTime(transmissionHeader.getCreationTime());
                crd.setRecordType(transmissionHeader.getRecordType());
                crd.setSenderType("SO");
                crd.setSenderId(transmissionHeader.getSenderId());
                crd.setSenderName(transmissionHeader.getSenderName());
                crd.setVersion(transmissionHeader.getStandardVersionNumber());
                crd.setTransmissionDate(transmissionHeader.getTransmissionDate());
                crd.init();
                crd2CtlHdrMapper.insert(crd);
            } else if(crdRecord instanceof TransmissionTrailer){
                TransmissionTrailer transmissionTrailer = (TransmissionTrailer) crdRecord;
                Crd2CtlTrl crd = new Crd2CtlTrl();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setRecordType(transmissionTrailer.getRecordType());
                crd.setFileLineNo(transmissionTrailer.getLine());
                crd.setFileLine(transmissionTrailer.toString());
                crd.setTransCountNum(transmissionTrailer.getTransactionCount());
                crd.setTransactionCount(transmissionTrailer.getTransactionCount());
                crd.setGroupCount(transmissionTrailer.getGroupCount());
                crd.setRecCountNum(transmissionTrailer.getRecordCount());
                crd.setRecordCount(transmissionTrailer.getRecordCount());
                crd.init();
                crd2CtlTrlMapper.insert(crd);
            } else if(crdRecord instanceof GroupHeader){
                GroupHeader groupHeader = (GroupHeader)crdRecord;
                Crd2CtlGrh crd = new Crd2CtlGrh();
                crd.setRecordType(groupHeader.getRecordType());
                crd.setFileLineNo(groupHeader.getLine());
                crd.setFileLine(groupHeader.toString());
                crd.setFileId(listOverseasFileBase.getId());
                crd.setGroupId(groupHeader.getGroupId());
                crd.setTransactionType(groupHeader.getTransactionType());
                crd.setSmDistType(groupHeader.getSubmissionType());
                crd.setBatchRequest(groupHeader.getBatchRequest());
                crd.setBatchRequestNum(groupHeader.getBatchRequest());
                crd.setVersionNo(groupHeader.getVersionNumber());
                crd.init();
                crd2CtlGrhMapper.insert(crd);
            } else if(crdRecord instanceof GroupTrailer){
                GroupTrailer groupTrailer = (GroupTrailer)crdRecord;
                Crd2CtlGrt crd = new Crd2CtlGrt();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setRecordType(groupTrailer.getRecordType());
                crd.setFileLineNo(groupTrailer.getLine());
                crd.setFileLine(groupTrailer.toString());
                crd.setGroupId(groupTrailer.getGroupId());
                crd.setAmount(groupTrailer.getTotalMonetaryValue());
                crd.setAmountNum(groupTrailer.getTotalMonetaryValue());
                crd.setCurrencyCode(groupTrailer.getCurrencyIndicator());
                crd.setRecCountNum(groupTrailer.getRecordCount());
                crd.setRecordCount(groupTrailer.getRecordCount());
                crd.setTransactionCount(groupTrailer.getTransactionCount());
                crd.setTransCountNum(groupTrailer.getTransactionCount());
                crd.init();
                crd2CtlGrtMapper.insert(crd);
            } else if(crdRecord instanceof SocietyDistributionNotificationTransactionHeader){
                SocietyDistributionNotificationTransactionHeader sdn = (SocietyDistributionNotificationTransactionHeader)crdRecord;
                Crd2SdnHdr crd2 = new Crd2SdnHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setRecordType(sdn.getRecordType());
                crd2.setFileLineNo(sdn.getLine());
                crd2.setFileLine(sdn.toString());
                crd2.setGroupId(0);
                crd2.setTransactionSeq(sdn.getTransactionSequenceNo());
                crd2.setRecordSeq(sdn.getRecordSequenceNo());
                crd2.setRemitSocDistId(sdn.getRemittingSocietyDistributionIdentifier());
                crd2.setRightType(sdn.getTypeOfRightCategory());
                crd2.setAccountPeriodStart(sdn.getAccountingPeriodStart());
                crd2.setAccountPeriodEnd(sdn.getAccountingPeriodEnd());
                crd2.setRecipientCode(sdn.getRecipientSocietyPublisherCode());
                crd2.setRecipientName(sdn.getNameOfRecipient());
                crd2.setBankPayDate(sdn.getBankPaymentDate());
                crd2.setAmountDecimalPlace(sdn.getAmountDecimalPlaces());
                crd2.setPercentDecimalPlace(sdn.getPercentageDecimalPlaces());
                crd2.setVersionNo(sdn.getVersionNumber());
                crd2.setRemitCurrency(sdn.getRemittanceCurrency());
                crd2.init();
                crd2SdnHdrMapper.insert(crd2);
            } else if(crdRecord instanceof ExploitationSourceInformation){
                ExploitationSourceInformation esi = (ExploitationSourceInformation)crdRecord;
                Crd2SdnDtlEsi crd2 = new Crd2SdnDtlEsi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(esi.getRecordType());
                crd2.setTransactionSeq(esi.getTransactionSequenceNo());
                crd2.setRecordSeq(esi.getRecordSequenceNo());
                crd2.setExploitSourceId(esi.getExploitationSourceIdentifier());
                crd2.setExploitSourceName(esi.getExploitationSourceName());
                crd2.setExploitSourceType(esi.getExploitationSourceType());
                crd2.setExploitTerritoryCode(esi.getExploitationTerritoryCode() + "");
                crd2.setExploitTerritoryCodeValidFrom(esi.getExploitationTerritoryCodeValidFromDate());
                crd2.setExploitTerritoryName(esi.getExploitationTerritoryAbbreviatedName());
                crd2.setExploitTerritoryNameValidFrom(esi.getExploitationTerritoryAbbreviatedNameValidFromDate());
                crd2.setFileLineNo(esi.getLine());
                crd2.setFileLine(esi.toString());
                crd2.init();
                crd2SdnDtlEsiMapper.insert(crd2);
            } else if(crdRecord instanceof InterestedPartyInformation){
                InterestedPartyInformation ipi = (InterestedPartyInformation)crdRecord;
                Crd2SdnDtlIpi crd2 = new Crd2SdnDtlIpi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(ipi.getRecordType());
                crd2.setTransactionSeq(ipi.getTransactionSequenceNo());
                crd2.setRecordSeq(ipi.getRecordSequenceNo());
                crd2.setIpNo(ipi.getInterestedPartyNumber());
                crd2.setIpType(ipi.getGenericTypeOfIp());
                crd2.setIpNameNo(ipi.getInterestedPartyNumber());
                crd2.setIpName(ipi.getIpName());
                crd2.setIpFirstName(ipi.getIpFirstName());
                crd2.setFileLineNo(ipi.getLine());
                crd2.setFileLine(ipi.toString());
                crd2.init();
                crd2SdnDtlIpiMapper.insert(crd2);
            } else if(crdRecord instanceof AudioVisualProgramInformation){
                AudioVisualProgramInformation api = (AudioVisualProgramInformation)crdRecord;
                Crd2SdnDtlApi crd2 = new Crd2SdnDtlApi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(api.getRecordType());
                crd2.setTransactionSeq(api.getTransactionSequenceNo());
                crd2.setRecordSeq(api.getRecordSequenceNo());
                crd2.setSocAvWorkId(api.getSocietyAvWorkIdentifier());
//                crd2.setIsan();
                crd2.setAvIndexNo(api.getAvIndexNumber());
                crd2.setType(api.getType());
                crd2.setAvWorkCategory(api.getAvWorkCategory());
                crd2.setAvWorkTitle(api.getAvWorkTitle());
                crd2.setLocalAvWorkTitle(api.getLocalAvWorkTitle());
                crd2.setOriginalEpisodeTitle(api.getOriginalEpisodeTitle());
                crd2.setLocalEpisodeTitle(api.getLocalEpisodeTitle());
                crd2.setOrigianlEpisodeNo(api.getLocalEpisodeNumber());
                crd2.setLocalEpisodeNo(api.getLocalEpisodeNumber());
                crd2.setProducerName(api.getProducerName());
                crd2.setDirectorName(api.getDirectorName());
                crd2.setActorName(api.getActorName());
                crd2.setTotalDuration(api.getTotalDuration());
                crd2.setMusicDuration(api.getMusicDuration());
                crd2.setProtectedMusicDuration(api.getProtectedMusicDuration());
                crd2.setCountryTerritoryCode(api.getCountryOfOriginTerritoryCode());
                crd2.setCountryTerritoryCodeValidFrom(api.getCountryOfOriginTerritoryCodeValidFromDate());
                crd2.setCountryTerritoryName(api.getCountryOfOriginTerritoryAbbreviatedName());
                crd2.setCountryTerritoyrNameValidFrom(api.getCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd2.setYearOfProduction(api.getYearOfProduction());
                crd2.setFileLineNo(api.getLine());
                crd2.setFileLine(api.toString());
                crd2.init();
                crd2SdnDtlApiMapper.insert(crd2);
            } else if(crdRecord instanceof CurrencyConversionRate){
                CurrencyConversionRate ccr = (CurrencyConversionRate)crdRecord;
                Crd2SdnDtlCcr crd2 = new Crd2SdnDtlCcr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(ccr.getRecordType());
                crd2.setTransactionSeq(ccr.getTransactionSequenceNo());
                crd2.setRecordSeq(ccr.getRecordSequenceNo());
                crd2.setCurrencyCode(ccr.getCurrencyCode());
                crd2.setRate(ccr.getRate());
                crd2.setFileLineNo(ccr.getLine());
                crd2.setFileLine(ccr.toString());
                crd2.init();
                crd2SdnDtlCcrMapper.insert(crd2);
            } else if(crdRecord instanceof RecordedProductInformation){
                RecordedProductInformation rpi = (RecordedProductInformation)crdRecord;
                Crd2SdnDtlRpi crd2 = new Crd2SdnDtlRpi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(rpi.getRecordType());
                crd2.setTransactionSeq(rpi.getTransactionSequenceNo());
                crd2.setRecordSeq(rpi.getRecordSequenceNo());
                crd2.setRecordedProductId(rpi.getRecordedProductIdentifier());
                crd2.setEanUpcCode(rpi.getEanUpcCode());
                crd2.setProductCatalogueNo(rpi.getProductCatalogueNumber());
                crd2.setScType(rpi.getSoundCarrierType());
                crd2.setScTitle(rpi.getSoundCarrierTitle());
                crd2.setScCountryTerritoryCode(rpi.getSoundCarrierCountryOfOriginTerritoryCode());
                crd2.setScCountryTerritoryCodeFrom(rpi.getSoundCarrierCountryOfOriginTerritoryCodeValidFromDate());
                crd2.setScCountryTerritoryName(rpi.getSoundCarrierCountryOfOriginTerritoryAbbreviatedName());
                crd2.setScCountryTerritoryNameFrom(rpi.getSoundCarrierCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd2.setTrackCountryTerritoryCode(rpi.getTrackCountryOfOriginTerritoryCode());
                crd2.setTrackCountryTerritoryCodeFrom(rpi.getTrackCountryOfOriginTerritoryCodeValidFromDate());
                crd2.setTrackCountryTerritoryName(rpi.getTrackCountryOfOriginTerritoryAbbreviatedName());
                crd2.setTrackCountryTerritoryNameFrom(rpi.getTrackCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd2.setFiller("");
                crd2.setProductTracks(rpi.getProductTracks());
                crd2.setPackQuanity(rpi.getPackQuantity());
                crd2.setItemTracks(rpi.getItemTracks());
                crd2.setTrackProrateBasis(rpi.getTrackProrateBasis());
                crd2.setProductTotalDuration(rpi.getProductTotalDuration());
                crd2.setProductMusicDuration(rpi.getProductMusicDuration());
                crd2.setReleaseDate(rpi.getReleaseDate());
                crd2.setFileLineNo(rpi.getLine());
                crd2.setFileLine(rpi.toString());
                crd2.init();
                crd2SdnDtlRpiMapper.insert(crd2);
            } else if(crdRecord instanceof TerritorySpecificDistributionInformation){
                TerritorySpecificDistributionInformation tdi = (TerritorySpecificDistributionInformation)crdRecord;
                Crd2SdnDtlTdi crd2 = new Crd2SdnDtlTdi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(tdi.getRecordType());
                crd2.setTransactionSeq(tdi.getTransactionSequenceNo());
                crd2.setRecordSeq(tdi.getRecordSequenceNo());
                crd2.setAttributeName(tdi.getAttributeName());
                crd2.setAttributeValue(tdi.getAttributeValue());
                crd2.setFileLineNo(tdi.getLine());
                crd2.setFileLine(tdi.toString());
                crd2.init();
                crd2SdnDtlTdiMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkNotificationTransactionHeader){
                MusicalWorkNotificationTransactionHeader mwn = (MusicalWorkNotificationTransactionHeader)crdRecord;
                Crd2MwnHdr crd2 = new Crd2MwnHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mwn.getRecordType());
                crd2.setTransactionSeq(mwn.getTransactionSequenceNo());
                crd2.setRecordSeq(mwn.getRecordSequenceNo());
                crd2.setSocWorkId(mwn.getSocietyWorkIdentifier());
                crd2.setOriginalWorkTitle(mwn.getOriginalWorkTitle());
                crd2.setLocalWorkTitle(mwn.getLocalWorkTitle());
                crd2.setIswc(mwn.getIswc());
                crd2.setCompositeWorkIndicator(mwn.getCompositeWorkIndicator());
                crd2.setCompositeWorkId(mwn.getCompositeWorkIdentifier());
                crd2.setCompositeWorkTitle(mwn.getCompositeWorkTitle());
                crd2.setWorkPercentageOfComposite(mwn.getWorkPercentageOfComposite());
                crd2.setInstrumentalVocalUse(mwn.getInstrumentalOrVocalUse());
                crd2.setMusicalWorkDistCategory(mwn.getMusicalWorkDistributionCategory());
                crd2.setOpusNo(mwn.getOpusNumber());
                crd2.setSongCodeOfPublisher(mwn.getSongCodeOfPublisher());
                crd2.setFileLineNo(mwn.getLine());
                crd2.setFileLine(mwn.toString());
                crd2.init();
                crd2MwnHdrMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatus){
                MusicalWorkDistributionStatus mds = (MusicalWorkDistributionStatus)crdRecord;
                Crd2MwnDtlMds crd2 = new Crd2MwnDtlMds();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mds.getRecordType());
                crd2.setTransactionSeq(mds.getTransactionSequenceNo());
                crd2.setRecordSeq(mds.getRecordSequenceNo());
                crd2.setMdsId(mds.getMdsIdentifier() + "");
                crd2.setShareType(mds.getTypeOfShareStructure());
                crd2.setPayRuleIndicator(mds.getPaymentRuleIndicator());
                crd2.setWorkStatusStart(mds.getWorkStatusStartDate());
                crd2.setWorkStatusEnd(mds.getWorkStatusEndDate());
                crd2.setFileLineNo(mds.getLine());
                crd2.setFileLine(mds.toString());
                crd2.init();
                crd2MwnDtlMdsMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatusRight){
                MusicalWorkDistributionStatusRight mdr = (MusicalWorkDistributionStatusRight)crdRecord;
                Crd2MwnDtlMdr crd2 = new Crd2MwnDtlMdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mdr.getRecordType());
                crd2.setTransactionSeq(mdr.getTransactionSequenceNo());
                crd2.setRecordSeq(mdr.getRecordSequenceNo());
                crd2.setRightType(mdr.getTypeOfRightCategory());
                crd2.setRightCode(mdr.getRightCode());
                crd2.setFileLineNo(mdr.getLine());
                crd2.setFileLine(mdr.toString());
                crd2.init();
                crd2MwnDtlMdrMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatusTerritory){
                MusicalWorkDistributionStatusTerritory mdt = (MusicalWorkDistributionStatusTerritory)crdRecord;
                Crd2MwnDtlMdt crd2 = new Crd2MwnDtlMdt();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mdt.getRecordType());
                crd2.setTransactionSeq(mdt.getTransactionSequenceNo());
                crd2.setRecordSeq(mdt.getRecordSequenceNo());
                crd2.setTerritoryCode(mdt.getInterestTerritoryTisn() + "");
                crd2.setTerritoryCodeFrom(mdt.getInterestTerritoryTisnValidFromDate());
                crd2.setTerritoryTisan(mdt.getInterestTerritoryTisan());
                crd2.setTerritoryTisanFrom(mdt.getInterestTerritoryTisanValidFromDate());
                crd2.setIndicator(""); //TODO
                crd2.setFileLineNo(mdt.getLine());
                crd2.setFileLine(mdt.toString());
                crd2.init();
                crd2MwnDtlMdtMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkInterestedParty){
                MusicalWorkInterestedParty mip = (MusicalWorkInterestedParty)crdRecord;
                Crd2MwnDtlMip crd2 = new Crd2MwnDtlMip();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mip.getRecordType());
                crd2.setTransactionSeq(mip.getTransactionSequenceNo());
                crd2.setRecordSeq(mip.getRecordSequenceNo());
                crd2.setIpNo(mip.getInterestedPartyNumber());
                crd2.setIpSoc(mip.getIpSociety() + "");
                crd2.setIpRole(mip.getIpRole());
                crd2.setShareNumerator(mip.getShareNumerator());
                crd2.setShareDenominator(mip.getShareDenominator());
                crd2.setSharePercentage(mip.getSharePercentage());
                crd2.setNationalAgreementNo(mip.getNationalAgreementNumber());
                crd2.setNationalAdminNo(mip.getNationalAgreementNumber());
                crd2.setContractIndicator(mip.getContractIndicator());
                crd2.setFileLineNo(mip.getLine());
                crd2.setFileLine(mip.toString());
                crd2.init();
                crd2MwnDtlMipMapper.insert(crd2);
            } else if(crdRecord instanceof InterestedPartyCategorizedContingencyPayments){
                InterestedPartyCategorizedContingencyPayments icc = (InterestedPartyCategorizedContingencyPayments)crdRecord;
                Crd2MwnDtlIcc crd2 = new Crd2MwnDtlIcc();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(icc.getRecordType());
                crd2.setTransactionSeq(icc.getTransactionSequenceNo());
                crd2.setRecordSeq(icc.getRecordSequenceNo());
                crd2.setRefId(icc.getReferenceIdentifier());
                crd2.setExploitSourceId(icc.getExploitationSourceIdentifier());
                crd2.setCurrencyCode(icc.getCurrency());
                crd2.setSourceSocConAmt(icc.getSourceSocietyContingencyAmount());
                crd2.setSourceSocConAmtSign(icc.getSourceSocietyContingencyAmountSign());
                crd2.setRemitSocConAmt(icc.getRemittingSocietyContingencyAmount());
                crd2.setRemitSocConAmtSign(icc.getRemittingSocietyContingencyAmountSign());
                crd2.setContingencyRate(icc.getContingencyRate());
                crd2.setDistCategory(icc.getDistributionCategory());
                crd2.setIpNo(icc.getInterestedPartyNumber());
                crd2.setFileLineNo(icc.getLine());
                crd2.setFileLine(icc.toString());
                crd2.init();
                crd2MwnDtlIccMapper.insert(crd2);
            } else if(crdRecord instanceof WorkExploitationPerformance){
                WorkExploitationPerformance wep = (WorkExploitationPerformance)crdRecord;
                Crd2MwnDtlWep crd = new Crd2MwnDtlWep();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setGroupId(0);
                crd.setRecordType(wep.getRecordType());
                crd.setTransactionSeq(wep.getTransactionSequenceNo());
                crd.setRecordSeq(wep.getRecordSequenceNo());
                crd.setRefId(wep.getReferenceIdentifier());
                crd.setDistCategory(wep.getDistributionCategory());
                crd.setExploitSourceId(wep.getExploitationSourceIdentifier());
                crd.setQuantity(wep.getQuantity());
                crd.setAggregationPeriod(wep.getAggregationPeriodType());
                crd.setExploitDate(wep.getExploitationDate());
                crd.setExploitTime(wep.getExploitationTime());
                crd.setIsrc(wep.getIsrc());
                crd.setPerformingArtistLastName(wep.getPerformingArtistLastName());
                crd.setPerformingArtistFirstName(wep.getPerformingArtistFirstName());
                crd.setSurveyType(wep.getSurveyType());
                crd.setDayOfWeekCode(wep.getDayOfWeekCode());
                crd.setBasisOfRateCal(wep.getBasisOfRateCalculation());
                crd.setMinSecPointValue(wep.getMinuteSecondPointValue() + "");
                crd.setBonusLevelPerformanceInd(wep.getBonusLevelPerformanceIndicator());
                crd.setNationalGroupingId(wep.getNationalGroupingIdentifier());
                crd.setCurrency(wep.getCurrency());
                crd.setGrossRoyaltyAmount(wep.getGrossRoyaltyAmount());
                crd.setTaxRate(wep.getTaxRate());
                crd.setTaxAmount(wep.getTaxAmount());
                crd.setCommissionAmount(wep.getCommissionAmount());
                crd.setSourceSoc(wep.getExploitationSourceIdentifier());
                crd.setSourceSocTaxAmount(wep.getSourceSocietyTaxAmount());
                crd.setSourceSocCommissionAmount(wep.getSourceSocietyCommissionAmount());
                crd.setRemitRoyaltyAmount(wep.getGrossRoyaltyAmount());
                crd.setFileLineNo(wep.getLine());
                crd.setFileLine(wep.toString());
                crd.init();
                crd2MwnDtlWepMapper.insert(crd);
            } else if(crdRecord instanceof WorkExploitationAudiovisualProgramPerformance){
                WorkExploitationAudiovisualProgramPerformance wea = (WorkExploitationAudiovisualProgramPerformance)crdRecord;
                Crd2MwnDtlWea crd2 = new Crd2MwnDtlWea();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(wea.getRecordType());
                crd2.setTransactionSeq(wea.getTransactionSequenceNo());
                crd2.setRecordSeq(wea.getRecordSequenceNo());
                crd2.setRefId(wea.getReferenceIdentifier());
                crd2.setDistCategory(wea.getDistributionCategory());
                crd2.setExploitSourceId(wea.getExploitationSourceIdentifier());
                crd2.setQuantity(wea.getQuantity());
                crd2.setAggregationPeriodType(wea.getAggregationPeriodType());
                crd2.setExploitDate(wea.getExploitationDate());
                crd2.setExploitTime(wea.getExploitationTime());
                crd2.setSocAvWorkId(wea.getSocietyAvWorkIdentifier());
                crd2.setDayOfWeekCode(wea.getDayOfWeekCode());
                crd2.setOrigin(wea.getOrigin());
                crd2.setCueSequence(wea.getCueSequence() + "");
                crd2.setDuration(wea.getDuration());
                crd2.setProductCatalogueNo(wea.getProductCatalogueNumber());
                crd2.setSurveyType(wea.getSurveyType());
                crd2.setBasisOfRateCal(wea.getBasisOfRateCalculation());
                crd2.setNationalGroupingId(wea.getNationalGroupingIdentifier());
                crd2.setCurrency(wea.getCurrency());
                crd2.setGrossRoyaltyAmount(wea.getGrossRoyaltyAmount());
                crd2.setTaxRate(wea.getTaxRate() );
                crd2.setTaxAmount(wea.getTaxAmount() );
                crd2.setCommissionAmount(wea.getCommissionAmount());
                crd2.setSourceSoc(wea.getSourceSociety() + "");
                crd2.setSourceSocTaxAmount(wea.getSourceSocietyTaxAmount());
                crd2.setSourceSocCommissionAmount(wea.getSourceSocietyCommissionAmount());
                crd2.setRemitRoyaltyAmount(wea.getGrossRoyaltyAmount());
                crd2.setFileLineNo(wea.getLine());
                crd2.setFileLine(wea.toString());
                crd2.init();
                crd2MwnDtlWeaMapper.insert(crd2);
            } else if(crdRecord instanceof WorkExploitationRecording){
                WorkExploitationRecording wer = new WorkExploitationRecording();
                Crd2MwnDtlWer crd2 = new Crd2MwnDtlWer();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(wer.getRecordType());
                crd2.setTransactionSeq(wer.getTransactionSequenceNo());
                crd2.setRecordSeq(wer.getRecordSequenceNo());
                crd2.setRefId(wer.getReferenceIdentifier());
                crd2.setDistCategory(wer.getDistributionCategory());
                crd2.setExploitSourceId(wer.getExploitationSourceIdentifier());
                crd2.setQuantity(wer.getQuantity());
                crd2.setRecordedProductNo(wer.getRecordedProductNumber());
                crd2.setIsrc(wer.getIsrc());
                crd2.setCountryLicensingTerritoryCode(wer.getCountryOfLicensingTerritoryCode() + "");
                crd2.setCountryLicensingTerritoryCodeFrom(wer.getCountryOfLicensingTerritoryCodeValidFromDate());
                crd2.setCountryLicensingTerritoryName(wer.getCountryOfLicensingTerritoryAbbreviatedName());
                crd2.setCountryLicensingTerritoryNameFrom(wer.getCountryOfLicensingTerritoryAbbreviatedNameValidFromDate());
                crd2.setBasePriceType(wer.getBasePriceType());
                crd2.setSaleType(wer.getSaleType());
                crd2.setSalesPeriodStart(wer.getSalesPeriodStart());
                crd2.setSalesPeriodEnd(wer.getSalesPeriodEnd());
                crd2.setRoyaltyBasisPrice(wer.getRoyaltyBasisPrice());
                crd2.setTrackCountryTerritoryCode(wer.getTrackCountryOfOriginTerritoryCode() + "");
                crd2.setTrackCountryTerritoryCodeFrom(wer.getTrackCountryOfOriginTerritoryCodeValidFromDate());
                crd2.setTrackCountryTerritoryName(wer.getTrackCountryOfOriginTerritoryAbbreviatedName());
                crd2.setTrackCountryTerritoryNameFrom(wer.getTrackCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd2.setDuration(wer.getDuration());
                crd2.setTrackSharePercentage(wer.getTrackSharePercentage() );
                crd2.setCommissionRate(wer.getCommissionRate());
                crd2.setNationalGroupingId(wer.getNationalGroupingIdentifier() + "");
                crd2.setCurrency(wer.getCurrency());
                crd2.setGrossRoyaltyAmount(wer.getGrossRoyaltyAmount() );
                crd2.setTaxRate(wer.getTaxRate());
                crd2.setTaxAmount(wer.getTaxAmount() );
                crd2.setCommissionAmount(wer.getCommissionAmount());
                crd2.setSourceSoc(wer.getSourceSociety() + "");
                crd2.setSourceSocTaxAmount(wer.getSourceSocietyTaxAmount() );
                crd2.setSourceSocCommissionAmount(wer.getSourceSocietyCommissionAmount() );
                crd2.setRemitRoyaltyAmount(wer.getRemittedRoyaltyAmount() );
                crd2.setAggregationPeriodType(wer.getAggregationPeriodType() + "");
                crd2.setFileLineNo(wer.getLine());
                crd2.setFileLine(wer.toString());
                crd2.init();
                crd2MwnDtlWerMapper.insert(crd2);
            } else if(crdRecord instanceof WorkExploitationPerformanceBonusIndications){
                WorkExploitationPerformanceBonusIndications wbi = (WorkExploitationPerformanceBonusIndications)crdRecord;
                Crd2MwnDtlWbi crd2 = new Crd2MwnDtlWbi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(wbi.getRecordType());
                crd2.setTransactionSeq(wbi.getTransactionSequenceNo());
                crd2.setRecordSeq(wbi.getRecordSequenceNo());
                crd2.setWorkExploitRefId(wbi.getWorkExploitationReferenceIdentifier());
                crd2.setRefId(wbi.getReferenceIdentifier());
                crd2.setBonusLevelPerformanceIndicator(wbi.getBonusLevelPerformanceIndicator());
                crd2.setNationalGroupingId(wbi.getNationalGroupingIdentifier());
                crd2.setCurrency(wbi.getCurrency());
                crd2.setGrossRoyaltyAmount(wbi.getGrossRoyaltyAmount());
                crd2.setTaxRate(wbi.getTaxRate());
                crd2.setTaxAmount(wbi.getTaxAmount());
                crd2.setCommissionAmount(wbi.getCommissionAmount());
                crd2.setSourceSocCode(wbi.getSourceSociety() + "");
                crd2.setSourceSocTaxAmount(wbi.getSourceSocietyTaxAmount());
                crd2.setSourceSocComminssionAmount(wbi.getSourceSocietyCommissionAmount());
                crd2.setRemitRoyaltyAmount(wbi.getGrossRoyaltyAmount());
                crd2.setFileLineNo(wbi.getLine());
                crd2.setFileLine(wbi.toString());
                crd2.init();
                crd2MwnDtlWbiMapper.insert(crd2);
            } else if(crdRecord instanceof WorkExploitationUnidentifiedPerformanceInformation){
                WorkExploitationUnidentifiedPerformanceInformation wui = (WorkExploitationUnidentifiedPerformanceInformation)crdRecord;
                Crd2MwnDtlWui crd2 = new Crd2MwnDtlWui();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(wui.getRecordType());
                crd2.setTransactionSeq(wui.getTransactionSequenceNo());
                crd2.setRecordSeq(wui.getRecordSequenceNo());
                crd2.setFileLineNo(wui.getLine());
                crd2.setFileLine(wui.toString());
                crd2.init();
                crd2MwnDtlWuiMapper.insert(crd2);
            } else if(crdRecord instanceof Adjustment){
                Adjustment adj = (Adjustment)crdRecord;
                Crd2MwnDtlAdj crd2 = new Crd2MwnDtlAdj();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(adj.getRecordType());
                crd2.setTransactionSeq(adj.getTransactionSequenceNo());
                crd2.setRecordSeq(adj.getRecordSequenceNo());
                crd2.setRefId(adj.getReferenceIdentifier());
                crd2.setOrigRefId(adj.getOriginalReferenceIdentifier());
                crd2.setOrigDistId(adj.getOriginalDistributionIdentifier());
                crd2.setOrigIpNo(adj.getOriginalInterestedPartyNumber());
                crd2.setOrigIpName(adj.getOriginalIpName());
                crd2.setOrigIpFirstName(adj.getOriginalIpFirstName());
                crd2.setIncorrectShareNumerator(adj.getIncorrectShareNumerator() + "");
                crd2.setIncorrectShareDenominator(adj.getIncorrectShareDenominator() + "");
                crd2.setIncorrectSharePercent(adj.getIncorrectSharePercentage() + "");
                crd2.setCorrectShareNumerator(adj.getCorrectShareNumerator() + "");
                crd2.setCorrectShareDenominator(adj.getCorrectShareDenominator() + "");
                crd2.setCorrectSharePercent(adj.getCorrectSharePercentage() + "");
                crd2.setAdjustReasonCode(adj.getAdjustmentReasonCode());
                crd2.setAdjustReasonCommentary(adj.getAdjustmentReasonCommentary());
                crd2.setAdjustRefNo("");
                crd2.setAdjustAmount(adj.getAdjustmentAmount());
                crd2.setAdjustAmountSign(adj.getAdjustmentAmountSign());
                crd2.setOrigDistCategory(adj.getOriginalDistributionCategoryType());
                crd2.setOrigContainerId(adj.getOriginalContainerIdentifier());
                crd2.setAdjustmentCurrency("");
                crd2.setFileLineNo(adj.getLine());
                crd2.setFileLine(adj.toString());
                crd2.init();
                crd2MwnDtlAdjMapper.insert(crd2);
            } else if(crdRecord instanceof OtherInterestedParty){
                OtherInterestedParty oip = (OtherInterestedParty)crdRecord;
                Crd2MwnDtlOip crd2 = new Crd2MwnDtlOip();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(oip.getRecordType());
                crd2.setTransactionSeq(oip.getTransactionSequenceNo());
                crd2.setRecordSeq(oip.getRecordSequenceNo());
                crd2.setOtherIpDesc(oip.getOtherIpDescription());
                crd2.setOtherIpFirstName(oip.getOtherIpFirstName());
                crd2.setOtherIpName(oip.getOtherIpName());
                crd2.setFileLineNo(oip.getLine());
                crd2.setFileLine(oip.toString());
                crd2.init();
                crd2MwnDtlOipMapper.insert(crd2);
            } else if(crdRecord instanceof ReturnedRoyaltyPayment){
                ReturnedRoyaltyPayment rrp = (ReturnedRoyaltyPayment)crdRecord;
                Crd2MwnDtlRrp crd2 = new Crd2MwnDtlRrp();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(rrp.getRecordType());
                crd2.setTransactionSeq(rrp.getTransactionSequenceNo());
                crd2.setRecordSeq(rrp.getRecordSequenceNo());
                crd2.setRefId(rrp.getReferenceIdentifier());
                crd2.setReturnReasonCode(rrp.getReturnReasonCode());
                crd2.setReceivedRoyaltySource(rrp.getReceivedRoyaltySource());
                crd2.setOrigRefId(rrp.getOriginalReferenceIdentifier());
                crd2.setOrigDistId(rrp.getOriginalDistributionIdentifier());
                crd2.setOrigAdjustIndicator(rrp.getOriginalAdjustmentIndicator());
                crd2.setOrigAmount(rrp.getOriginalAmount());
                crd2.setOrigAmountSign(rrp.getOriginalAmountSign());
                crd2.setReturnAmount(rrp.getReturnAmount());
                crd2.setReturnAmountSign(rrp.getReturnAmountSign());
                crd2.setOrigSocWorkId(rrp.getOriginalWorkTitle());
                crd2.setOrigWorkTitle(rrp.getOriginalWorkTitle());
                crd2.setOrigSocAvWorkId(rrp.getOriginalSocietyAvWorkIdentifier());
                crd2.setOrigAvWorkTitle(rrp.getOriginalAvWorkTitle());
                crd2.setOrigIpNo(rrp.getOriginalInterestedPartyNumber());
                crd2.setOrigIpName(rrp.getOriginalIpName());
                crd2.setOrigIpFirstName(rrp.getOriginalIpFirstName());
                crd2.setOrigShareNumerator(rrp.getOriginalShareNumerator() + "");
                crd2.setOrigShareDenominator(rrp.getOriginalShareDenominator() + "");
                crd2.setOrigSharePercent(rrp.getOriginalSharePercentage());
                crd2.setOrigIpRole(rrp.getOriginalIpRole());
                crd2.setResolvedIpSoc(rrp.getResolvedIpSociety() + "");
                crd2.setResolvedIpNo(rrp.getResolvedInterestedPartyNumber());
                crd2.setResolvedIpNameNo(rrp.getResolvedInterestedPartyNumber());
                crd2.setResolvedIpName(rrp.getResolvedIpName());
                crd2.setResolvedIpFirstName(rrp.getResolvedIpFirstName());
                crd2.setResolvedIpRole(rrp.getResolvedIpRole());
                crd2.setResolvedSocAvWorkId(rrp.getResolvedSocietyAvWorkIdentifier());
                crd2.setResolvedShareNumerator(rrp.getResolvedShareNumerator() + "");
                crd2.setResolvedShareDenominator(rrp.getResolvedShareDenominator() + "");
                crd2.setResolvedSharePercent(rrp.getResolvedSharePercentage());
                crd2.setCurrency("");
                crd2.setFileLineNo(rrp.getLine());
                crd2.setFileLine(rrp.toString());
                crd2MwnDtlRrpMapper.insert(crd2);
            } else if(crdRecord instanceof AdHocPayment){
                AdHocPayment ahp = (AdHocPayment)crdRecord;
                Crd2AhpHdr crd2 = new Crd2AhpHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(ahp.getRecordType());
                crd2.setTransactionSeq(ahp.getTransactionSequenceNo());
                crd2.setRecordSeq(ahp.getRecordSequenceNo());
                crd2.setIpNo(ahp.getInterestedPartyNumber());
                crd2.setIpSoc(ahp.getSociety() + "");
                crd2.setCurrency(ahp.getCurrency());
                crd2.setPaymentAmount(ahp.getPaymentAmount());
                crd2.setPaymentSign(ahp.getPaymentSign());
                crd2.setTaxRate(ahp.getTaxRtae());
                crd2.setTaxAmount(ahp.getTaxAmount());
                crd2.setCommissionAmount(ahp.getCommissionAmoun());
                crd2.setRemitRoyaltyAmount(ahp.getRemittedRoyaltyAmount());
                crd2.setComments(ahp.getComments());
                crd2.setFileLineNo(ahp.getLine());
                crd2.setFileLine(ahp.toString());
                crd2.init();
                crd2AhpHdrMapper.insert(crd2);
            } else if(crdRecord instanceof SocietySpecificInformation){
                SocietySpecificInformation sid = (SocietySpecificInformation)crdRecord;
                Crd2SinDtlSid crd2 = new Crd2SinDtlSid();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(sid.getRecordType());
                crd2.setTransactionSeq(sid.getTransactionSequenceNo());
                crd2.setRecordSeq(sid.getRecordSequenceNo());
                crd2.setSidType(sid.getSidType());
                crd2.setRefTransactionSeq(sid.getReferenceTransactionSequence());
                crd2.setRefRecordType(sid.getReferenceRecordSequence());
                crd2.setFileLineNo(sid.getLine());
                crd2.setFileLine(sid.toString());
                crd2.init();
                crd2SinDtlSidMapper.insert(crd2);
            } else if(crdRecord instanceof SocietySpecificInformationNotificationTransactionHeader){
                SocietySpecificInformationNotificationTransactionHeader sin = (SocietySpecificInformationNotificationTransactionHeader)crdRecord;
                Crd2SinHdr crd2 = new Crd2SinHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(sin.getRecordType());
                crd2.setTransactionSeq(sin.getTransactionSequenceNo());
                crd2.setRecordSeq(sin.getRecordSequenceNo());
                crd2.setSidType(sin.getSidType());
                crd2.setFileLineNo(sin.getLine());
                crd2.setFileLine(sin.toString());
                crd2.init();
                crd2SinHdrMapper.insert(crd2);
            } else if(crdRecord instanceof RoyaltyGrandTotalsTransactionHeader){
                RoyaltyGrandTotalsTransactionHeader rgt = (RoyaltyGrandTotalsTransactionHeader)crdRecord;
                Crd3RgtHdr crd3 = new Crd3RgtHdr();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(rgt.getRecordType());
                crd3.setTransactionSeq(rgt.getTransactionSequenceNo());
                crd3.setRecordSeq(rgt.getRecordSequenceNo());
                crd3.setWexRemitRoyaltyAmount(rgt.getWexRemittedRoyaltyAmount());
                crd3.setIccSourceSocContingencyAmount(rgt.getIccSourceSocietyContingencyAmount());
                crd3.setIccSourceSocContingencyAmountSign(rgt.getIccSourceSocietyContingencyAmountSign());
                crd3.setIccRemitSocContingencyAmount(rgt.getIccRemittingSocietyContingencyAmount());
                crd3.setIccRemitSocContingencyAmountSign(rgt.getIccRemittingSocietyContingencyAmountSign());
                crd3.setAdjAdjustmentAmount(rgt.getAdjAdjustmentAmount());
                crd3.setAdjAdjustmentAmountSign(rgt.getAdjAdjustmentAmountSign());
                crd3.setFeoReturnAmount(rgt.getFeoReturnAmount());
                crd3.setFeoReturnAmountSign(rgt.getFeoReturnAmountSign());
                crd3.setWepGrossRoyaltyAmount(rgt.getWepGrossRoyaltyAmount());
                crd3.setWeaGrossRoyaltyAmount(rgt.getWeaGrossRoyaltyAmount());
                crd3.setWerGrossRoyaltyAmount(rgt.getWerGrossRoyaltyAmount());
                /*crd3.setAhpGrossAmount(0L);
                crd3.setAhpGrossAmountSign("+");*/
//                crd3.setWbiGrossRoyaltyAmount(0L);
                crd3.setIccGrossContAmount(rgt.getIccSourceSocietyContingencyAmount());
                crd3.setWepRemitRoyaltyAmount(rgt.getWepGrossRoyaltyAmount());
                crd3.setWeaRemitRoyaltyAmount(rgt.getWeaGrossRoyaltyAmount());
                crd3.setWerRemitRoyaltyAmount(rgt.getWerGrossRoyaltyAmount());
//                crd3.setWbiRemitRoyaltyAmount(0L);
//                crd3.setAhpRemitRoyaltyAmount(0L);
                crd3.setAhpRemitRoyaltyAmountSign("+");
                crd3.setFileLineNo(rgt.getLine());
                crd3.setFileLine(rgt.toString());
                crd3RgtHdrMapper.insert(crd3);
            } else if(crdRecord instanceof AcknowledgementTransactionHeader){
                AcknowledgementTransactionHeader ack = (AcknowledgementTransactionHeader)crdRecord;
                Crd3AckHdr crd3 = new Crd3AckHdr();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(ack.getRecordType());
                crd3.setTransactionSeq(ack.getTransactionSequenceNo());
                crd3.setRecordSeq(ack.getRecordSequenceNo());
                crd3.setRemitSocDistId(ack.getRemittingSocietyDistributionIdentifier());
                crd3.setAccountPeriodEnd(ack.getAccountingPeriodEnd());
                crd3.setAccountPeriodStart(ack.getAccountingPeriodStart());
                crd3.setFileLineNo(ack.getLine());
                crd3.setFileLine(ack.toString());
                crd3.init();
                crd3AckHdrMapper.insert(crd3);
            } else if(crdRecord instanceof SuccessfulProcessWork){
                SuccessfulProcessWork spw = (SuccessfulProcessWork)crdRecord;
                Crd3AckDtlSpw crd3 = new Crd3AckDtlSpw();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(spw.getRecordType());
                crd3.setTransactionSeq(spw.getTransactionSequenceNo());
                crd3.setRecordSeq(spw.getRecordSequenceNo());
                crd3.setFileLineNo(spw.getLine());
                crd3.setSocWorkId(spw.getSocietyWorkIdentifier());
                crd3.setIswc(spw.getIswc() + "");
                crd3.setFileLine(spw.toString());
                crd3.init();
                crd3AckDtlSpwMapper.insert(crd3);
            } else if(crdRecord instanceof UnsuccessfulProcessAdvice){
                UnsuccessfulProcessAdvice upa = (UnsuccessfulProcessAdvice)crdRecord;
                Crd3AckDtlUpa crd3 = new Crd3AckDtlUpa();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(upa.getRecordType());
                crd3.setTransactionSeq(upa.getTransactionSequenceNo());
                crd3.setRecordSeq(upa.getRecordSequenceNo());
                crd3.setFileLineNo(upa.getLine());
                crd3.setSocWorkId(upa.getSocietyWorkIdentifier());
                crd3.setOriginalRecordSeq(upa.getOriginalRecordSequence());
                crd3.setOriginalRecordType(upa.getOriginalRecordType());
                crd3.setOriginalRefId(upa.getOriginalReferenceIdentifier());
                crd3.setOriginalTransactionSeq(upa.getOriginalTransactionSequence());
                crd3.setErrorCode(upa.getErrorCode());
                crd3.setErrorDesc(upa.getErrorDescription());
                crd3.setFileLine(upa.toString());
                crd3.init();
                crd3AckDtlUpaMapper.insert(crd3);
            }else if (crdRecord instanceof FeesInErrorNotificationToOriginalSociet2){
                FeesInErrorNotificationToOriginalSociet2 feo = (FeesInErrorNotificationToOriginalSociet2)crdRecord;
                Crd3MwnDtlFeo crd3MwnDtlFeo = new Crd3MwnDtlFeo();
                crd3MwnDtlFeo.setFileId(listOverseasFileBase.getId());
                crd3MwnDtlFeo.setRecordType(feo.getRecordType());
                crd3MwnDtlFeo.setFileLineNo(feo.getLine());
                crd3MwnDtlFeo.setFileLine(feo.toString());
                crd3MwnDtlFeo.setGroupId(0);
                crd3MwnDtlFeo.setCurrency(feo.getCurrency());
                crd3MwnDtlFeo.setErrorAmount(feo.getFeeInErrorAmount());
                crd3MwnDtlFeo.setErrorAmountSign(feo.getFeeInErrorAmountSign());
                crd3MwnDtlFeo.setErrorCode(feo.getFeeInErrorReasonCode());
                crd3MwnDtlFeo.setFullShareIndicator(feo.getFullShareIndicator());
                crd3MwnDtlFeo.setIsrc(feo.getIsrc());
                // crd3 中多增加的一个字段
                crd3MwnDtlFeo.setFiller(feo.getFiller());

                crd3MwnDtlFeo.setIswc(feo.getIswc());
                crd3MwnDtlFeo.setOrigAdjustIndicator(feo.getOriginalAdjustmentIndicator());
                crd3MwnDtlFeo.setOrigAmount(feo.getOriginalAmount());
                crd3MwnDtlFeo.setOrigAmountSign(feo.getOriginalAmountSign());
                crd3MwnDtlFeo.setOrigAvWorkTitle(feo.getOriginalAvWorkTitle());
                crd3MwnDtlFeo.setOrigDistId(feo.getOriginalSocietyAvWorkId());
                crd3MwnDtlFeo.setOrigIpFirstName(feo.getOriginalIpFirstName());
                crd3MwnDtlFeo.setOrigIpName(feo.getOriginalIpName());
                crd3MwnDtlFeo.setOrigIpRole(feo.getOriginalIpRole());
                crd3MwnDtlFeo.setOrigShareDenominator(feo.getOriginalShareDenominator().toString());
                crd3MwnDtlFeo.setOrigShareNumerator(feo.getOriginalShareNumerator().toString());
                crd3MwnDtlFeo.setOrigSharePercent(feo.getOriginalSharePercentage());
                crd3MwnDtlFeo.setOrigSocAvWorkId(feo.getOriginalSocietyAvWorkId());
                crd3MwnDtlFeo.setOrigSocWorkId(feo.getOriginalSocietyWorkIdentifier());
                crd3MwnDtlFeo.setOrigRefId(feo.getOriginalReferenceIdentifier());
                crd3MwnDtlFeo.setOrigWorkTitle(feo.getOriginalWorkTitle());
                crd3MwnDtlFeo.setRecordSeq(feo.getRecordSequenceNo());
                crd3MwnDtlFeo.setRefId(feo.getReferenceIdentifier());
                crd3MwnDtlFeo.setResolvedIpFirstName(feo.getResolvedIpFirstName());
                crd3MwnDtlFeo.setResolvedIpName(feo.getResolvedIpName());
                crd3MwnDtlFeo.setResolvedIpNameNo(feo.getResolvedIpName());
                crd3MwnDtlFeo.setResolvedIpSoc(feo.getResolvedIpSociety().toString());
                crd3MwnDtlFeo.setReturnShareDenominator(feo.getReturnedShareDenominator().toString());
                crd3MwnDtlFeo.setReturnShareNumerator(feo.getReturnedShareNumerator().toString());
                crd3MwnDtlFeo.setReturnSharePercent(feo.getReturnedSharePercentage());
                crd3MwnDtlFeo.setRightType(feo.getTypeOfRightCategory());
                crd3MwnDtlFeo.setTransactionSeq(feo.getTransactionSequenceNo());
                crd3MwnDtlFeo.init();
                crd3MwnDtlFeoMapper.insert(crd3MwnDtlFeo);
            }
        }

    }

    public void convertV3(List<CrdRecord> crdRecordList, ListOverseasFileBase listOverseasFileBase){

        for(CrdRecord crdRecord : crdRecordList){
            if( crdRecord instanceof TransmissionHeader){
                TransmissionHeader transmissionHeader = (TransmissionHeader) crdRecord;
                Crd2CtlHdr crd = new Crd2CtlHdr();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setFileLineNo(transmissionHeader.getLine());
                crd.setFileLine(transmissionHeader.toString());
                crd.setFileCreationTime(transmissionHeader.getCreationTime());
                crd.setRecordType(transmissionHeader.getRecordType());
                crd.setSenderType("SO");
                crd.setSenderId(transmissionHeader.getSenderId());
                crd.setSenderName(transmissionHeader.getSenderName());
                crd.setVersion(transmissionHeader.getStandardVersionNumber());
                crd.setTransmissionDate(transmissionHeader.getTransmissionDate());
                crd.init();
                crd2CtlHdrMapper.insert(crd);
            } else if(crdRecord instanceof TransmissionTrailer){
                TransmissionTrailer transmissionTrailer = (TransmissionTrailer) crdRecord;
                Crd2CtlTrl crd = new Crd2CtlTrl();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setRecordType(transmissionTrailer.getRecordType());
                crd.setFileLineNo(transmissionTrailer.getLine());
                crd.setFileLine(transmissionTrailer.toString());
                crd.setTransCountNum(transmissionTrailer.getTransactionCount());
                crd.setTransactionCount(transmissionTrailer.getTransactionCount());
                crd.setGroupCount(transmissionTrailer.getGroupCount());
                crd.setRecCountNum(transmissionTrailer.getRecordCount());
                crd.setRecordCount(transmissionTrailer.getRecordCount());
                crd.init();
                crd2CtlTrlMapper.insert(crd);
            } else if(crdRecord instanceof GroupHeader){
                GroupHeader groupHeader = (GroupHeader)crdRecord;
                Crd2CtlGrh crd = new Crd2CtlGrh();
                crd.setRecordType(groupHeader.getRecordType());
                crd.setFileLineNo(groupHeader.getLine());
                crd.setFileLine(groupHeader.toString());
                crd.setFileId(listOverseasFileBase.getId());
                crd.setGroupId(groupHeader.getGroupId());
                crd.setTransactionType(groupHeader.getTransactionType());
                crd.setSmDistType(groupHeader.getSubmissionType());
                crd.setBatchRequest(groupHeader.getBatchRequest());
                crd.setBatchRequestNum(groupHeader.getBatchRequest());
                crd.setVersionNo(groupHeader.getVersionNumber());
                crd.init();
                crd2CtlGrhMapper.insert(crd);
            } else if(crdRecord instanceof GroupTrailer){
                GroupTrailer groupTrailer = (GroupTrailer)crdRecord;
                Crd2CtlGrt crd = new Crd2CtlGrt();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setRecordType(groupTrailer.getRecordType());
                crd.setFileLineNo(groupTrailer.getLine());
                crd.setFileLine(groupTrailer.toString());
                crd.setGroupId(groupTrailer.getGroupId());
                crd.setAmount(groupTrailer.getTotalMonetaryValue());
                crd.setAmountNum(groupTrailer.getTotalMonetaryValue());
                crd.setCurrencyCode(groupTrailer.getCurrencyIndicator());
                crd.setRecCountNum(groupTrailer.getRecordCount());
                crd.setRecordCount(groupTrailer.getRecordCount());
                crd.setTransactionCount(groupTrailer.getTransactionCount());
                crd.setTransCountNum(groupTrailer.getTransactionCount());
                crd.init();
                crd2CtlGrtMapper.insert(crd);
            } else if(crdRecord instanceof SocietyDistributionNotificationTransactionHeader){
                SocietyDistributionNotificationTransactionHeader sdn = (SocietyDistributionNotificationTransactionHeader)crdRecord;
                Crd2SdnHdr crd2 = new Crd2SdnHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setRecordType(sdn.getRecordType());
                crd2.setFileLineNo(sdn.getLine());
                crd2.setFileLine(sdn.toString());
                crd2.setGroupId(0);
                crd2.setTransactionSeq(sdn.getTransactionSequenceNo());
                crd2.setRecordSeq(sdn.getRecordSequenceNo());
                crd2.setRemitSocDistId(sdn.getRemittingSocietyDistributionIdentifier());
                crd2.setRightType(sdn.getTypeOfRightCategory());
                crd2.setAccountPeriodStart(sdn.getAccountingPeriodStart());
                crd2.setAccountPeriodEnd(sdn.getAccountingPeriodEnd());
                crd2.setRecipientCode(sdn.getRecipientSocietyPublisherCode());
                crd2.setRecipientName(sdn.getNameOfRecipient());
                crd2.setBankPayDate(sdn.getBankPaymentDate());
                crd2.setAmountDecimalPlace(sdn.getAmountDecimalPlaces());
                crd2.setPercentDecimalPlace(sdn.getPercentageDecimalPlaces());
                crd2.setVersionNo(sdn.getVersionNumber());
                crd2.setRemitCurrency(sdn.getRemittanceCurrency());
                crd2.init();
                crd2SdnHdrMapper.insert(crd2);
            } else if(crdRecord instanceof ExploitationSourceInformation){
                ExploitationSourceInformation esi = (ExploitationSourceInformation)crdRecord;
                Crd2SdnDtlEsi crd2 = new Crd2SdnDtlEsi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(esi.getRecordType());
                crd2.setTransactionSeq(esi.getTransactionSequenceNo());
                crd2.setRecordSeq(esi.getRecordSequenceNo());
                crd2.setExploitSourceId(esi.getExploitationSourceIdentifier());
                crd2.setExploitSourceName(esi.getExploitationSourceName());
                crd2.setExploitSourceType(esi.getExploitationSourceType());
                crd2.setExploitTerritoryCode(esi.getExploitationTerritoryCode() + "");
                crd2.setExploitTerritoryCodeValidFrom(esi.getExploitationTerritoryCodeValidFromDate());
                crd2.setExploitTerritoryName(esi.getExploitationTerritoryAbbreviatedName());
                crd2.setExploitTerritoryNameValidFrom(esi.getExploitationTerritoryAbbreviatedNameValidFromDate());
                crd2.setFileLineNo(esi.getLine());
                crd2.setFileLine(esi.toString());
                crd2.init();
                crd2SdnDtlEsiMapper.insert(crd2);
            } else if(crdRecord instanceof InterestedPartyInformation){
                InterestedPartyInformation ipi = (InterestedPartyInformation)crdRecord;
                Crd2SdnDtlIpi crd2 = new Crd2SdnDtlIpi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(ipi.getRecordType());
                crd2.setTransactionSeq(ipi.getTransactionSequenceNo());
                crd2.setRecordSeq(ipi.getRecordSequenceNo());
                crd2.setIpNo(ipi.getInterestedPartyNumber());
                crd2.setIpType(ipi.getGenericTypeOfIp());
                crd2.setIpNameNo(ipi.getInterestedPartyNumber());
                crd2.setIpName(ipi.getIpName());
                crd2.setIpFirstName(ipi.getIpFirstName());
                crd2.setFileLineNo(ipi.getLine());
                crd2.setFileLine(ipi.toString());
                crd2.init();
                crd2SdnDtlIpiMapper.insert(crd2);
            } else if(crdRecord instanceof AudioVisualProgramInformationV3){
                AudioVisualProgramInformationV3 api = (AudioVisualProgramInformationV3)crdRecord;
                Crd3SdnDtlApi crd3 = new Crd3SdnDtlApi();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(api.getRecordType());
                crd3.setTransactionSeq(api.getTransactionSequenceNo());
                crd3.setRecordSeq(api.getRecordSequenceNo());
                crd3.setSocAvWorkId(api.getSocietyAvWorkIdentifier());
//                crd2.setIsan();
//                crd3.setAvIndexNo(api.getAvIndexNumber());
                crd3.setType(api.getType());
                crd3.setAvWorkCategory(api.getAvWorkCategory());
                crd3.setAvWorkTitle(api.getAvWorkTitle());
                crd3.setLocalAvWorkTitle(api.getLocalAvWorkTitle());
                crd3.setOriginalEpisodeTitle(api.getOriginalEpisodeTitle());
                crd3.setLocalEpisodeTitle(api.getLocalEpisodeTitle());
                crd3.setOrigianlEpisodeNo(api.getLocalEpisodeNumber());
                crd3.setLocalEpisodeNo(api.getLocalEpisodeNumber());
                crd3.setProducerName(api.getProducerName());
                crd3.setDirectorName(api.getDirectorName());
                crd3.setActorName(api.getActorName());
                crd3.setTotalDuration(api.getTotalDuration());
                crd3.setMusicDuration(api.getMusicDuration());
                crd3.setProtectedMusicDuration(api.getProtectedMusicDuration());
                crd3.setCountryTerritoryCode(api.getCountryOfOriginTerritoryCode());
                crd3.setCountryTerritoryCodeValidFrom(api.getCountryOfOriginTerritoryCodeValidFromDate());
                crd3.setCountryTerritoryName(api.getCountryOfOriginTerritoryAbbreviatedName());
                crd3.setCountryTerritoyrNameValidFrom(api.getCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd3.setYearOfProduction(api.getYearOfProduction());
                crd3.setFileLineNo(api.getLine());
                crd3.setFileLine(api.toString());
                crd3.init();
                crd3SdnDtlApiMapper.insert(crd3);
            } else if(crdRecord instanceof CurrencyConversionRate){
                CurrencyConversionRate ccr = (CurrencyConversionRate)crdRecord;
                Crd2SdnDtlCcr crd2 = new Crd2SdnDtlCcr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(ccr.getRecordType());
                crd2.setTransactionSeq(ccr.getTransactionSequenceNo());
                crd2.setRecordSeq(ccr.getRecordSequenceNo());
                crd2.setCurrencyCode(ccr.getCurrencyCode());
                crd2.setRate(ccr.getRate());
                crd2.setFileLineNo(ccr.getLine());
                crd2.setFileLine(ccr.toString());
                crd2.init();
                crd2SdnDtlCcrMapper.insert(crd2);
            } else if(crdRecord instanceof RecordedProductInformationV3){
                RecordedProductInformationV3 rpi = (RecordedProductInformationV3)crdRecord;
                Crd3SdnDtlRpi crd3 = new Crd3SdnDtlRpi();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(rpi.getRecordType());
                crd3.setTransactionSeq(rpi.getTransactionSequenceNo());
                crd3.setRecordSeq(rpi.getRecordSequenceNo());
                crd3.setRecordedProductId(rpi.getRecordedProductIdentifier());
                crd3.setEanUpcCode(rpi.getEanUpcCode());
                crd3.setProductCatalogueNo(rpi.getProductCatalogueNumber());
                crd3.setScType(rpi.getSoundCarrierType());
                crd3.setScTitle(rpi.getSoundCarrierTitle());
                crd3.setScCountryTerritoryCode(rpi.getSoundCarrierCountryOfOriginTerritoryCode());
                crd3.setScCountryTerritoryCodeFrom(rpi.getSoundCarrierCountryOfOriginTerritoryCodeValidFromDate());
                crd3.setScCountryTerritoryName(rpi.getSoundCarrierCountryOfOriginTerritoryAbbreviatedName());
                crd3.setScCountryTerritoryNameFrom(rpi.getSoundCarrierCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd3.setFiller("");
                crd3.setProductTracks(rpi.getProductTracks());
                crd3.setPackQuanity(rpi.getPackQuantity());
                crd3.setItemTracks(rpi.getItemTracks());
                crd3.setTrackProrateBasis(rpi.getTrackProrateBasis());
                crd3.setProductTotalDuration(rpi.getProductTotalDuration());
                crd3.setProductMusicDuration(rpi.getProductMusicDuration());
                crd3.setReleaseDate(rpi.getReleaseDate());
                crd3.setFileLineNo(rpi.getLine());
                crd3.setFileLine(rpi.toString());
                crd3.init();
                crd3SdnDtlRpiMapper.insert(crd3);
            } else if(crdRecord instanceof TerritorySpecificDistributionInformation){
                TerritorySpecificDistributionInformation tdi = (TerritorySpecificDistributionInformation)crdRecord;
                Crd2SdnDtlTdi crd2 = new Crd2SdnDtlTdi();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(tdi.getRecordType());
                crd2.setTransactionSeq(tdi.getTransactionSequenceNo());
                crd2.setRecordSeq(tdi.getRecordSequenceNo());
                crd2.setAttributeName(tdi.getAttributeName());
                crd2.setAttributeValue(tdi.getAttributeValue());
                crd2.setFileLineNo(tdi.getLine());
                crd2.setFileLine(tdi.toString());
                crd2.init();
                crd2SdnDtlTdiMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkNotificationTransactionHeader){
                MusicalWorkNotificationTransactionHeader mwn = (MusicalWorkNotificationTransactionHeader)crdRecord;
                Crd2MwnHdr crd2 = new Crd2MwnHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mwn.getRecordType());
                crd2.setTransactionSeq(mwn.getTransactionSequenceNo());
                crd2.setRecordSeq(mwn.getRecordSequenceNo());
                crd2.setSocWorkId(mwn.getSocietyWorkIdentifier());
                crd2.setOriginalWorkTitle(mwn.getOriginalWorkTitle());
                crd2.setLocalWorkTitle(mwn.getLocalWorkTitle());
                crd2.setIswc(mwn.getIswc());
                crd2.setCompositeWorkIndicator(mwn.getCompositeWorkIndicator());
                crd2.setCompositeWorkId(mwn.getCompositeWorkIdentifier());
                crd2.setCompositeWorkTitle(mwn.getCompositeWorkTitle());
                crd2.setWorkPercentageOfComposite(mwn.getWorkPercentageOfComposite());
                crd2.setInstrumentalVocalUse(mwn.getInstrumentalOrVocalUse());
                crd2.setMusicalWorkDistCategory(mwn.getMusicalWorkDistributionCategory());
                crd2.setOpusNo(mwn.getOpusNumber());
                crd2.setSongCodeOfPublisher(mwn.getSongCodeOfPublisher());
                crd2.setFileLineNo(mwn.getLine());
                crd2.setFileLine(mwn.toString());
                crd2.init();
                crd2MwnHdrMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatus){
                MusicalWorkDistributionStatus mds = (MusicalWorkDistributionStatus)crdRecord;
                Crd2MwnDtlMds crd2 = new Crd2MwnDtlMds();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mds.getRecordType());
                crd2.setTransactionSeq(mds.getTransactionSequenceNo());
                crd2.setRecordSeq(mds.getRecordSequenceNo());
                crd2.setMdsId(mds.getMdsIdentifier() + "");
                crd2.setShareType(mds.getTypeOfShareStructure());
                crd2.setPayRuleIndicator(mds.getPaymentRuleIndicator());
                crd2.setWorkStatusStart(mds.getWorkStatusStartDate());
                crd2.setWorkStatusEnd(mds.getWorkStatusEndDate());
                crd2.setFileLineNo(mds.getLine());
                crd2.setFileLine(mds.toString());
                crd2.init();
                crd2MwnDtlMdsMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatusRight){
                MusicalWorkDistributionStatusRight mdr = (MusicalWorkDistributionStatusRight)crdRecord;
                Crd2MwnDtlMdr crd2 = new Crd2MwnDtlMdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mdr.getRecordType());
                crd2.setTransactionSeq(mdr.getTransactionSequenceNo());
                crd2.setRecordSeq(mdr.getRecordSequenceNo());
                crd2.setRightType(mdr.getTypeOfRightCategory());
                crd2.setRightCode(mdr.getRightCode());
                crd2.setFileLineNo(mdr.getLine());
                crd2.setFileLine(mdr.toString());
                crd2.init();
                crd2MwnDtlMdrMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkDistributionStatusTerritory){
                MusicalWorkDistributionStatusTerritory mdt = (MusicalWorkDistributionStatusTerritory)crdRecord;
                Crd2MwnDtlMdt crd2 = new Crd2MwnDtlMdt();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mdt.getRecordType());
                crd2.setTransactionSeq(mdt.getTransactionSequenceNo());
                crd2.setRecordSeq(mdt.getRecordSequenceNo());
                crd2.setTerritoryCode(mdt.getInterestTerritoryTisn() + "");
                crd2.setTerritoryCodeFrom(mdt.getInterestTerritoryTisnValidFromDate());
                crd2.setTerritoryTisan(mdt.getInterestTerritoryTisan());
                crd2.setTerritoryTisanFrom(mdt.getInterestTerritoryTisanValidFromDate());
                crd2.setIndicator(""); //TODO
                crd2.setFileLineNo(mdt.getLine());
                crd2.setFileLine(mdt.toString());
                crd2.init();
                crd2MwnDtlMdtMapper.insert(crd2);
            } else if(crdRecord instanceof MusicalWorkInterestedParty){
                MusicalWorkInterestedParty mip = (MusicalWorkInterestedParty)crdRecord;
                Crd2MwnDtlMip crd2 = new Crd2MwnDtlMip();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(mip.getRecordType());
                crd2.setTransactionSeq(mip.getTransactionSequenceNo());
                crd2.setRecordSeq(mip.getRecordSequenceNo());
                crd2.setIpNo(mip.getInterestedPartyNumber());
                crd2.setIpSoc(mip.getIpSociety() + "");
                crd2.setIpRole(mip.getIpRole());
                crd2.setShareNumerator(mip.getShareNumerator());
                crd2.setShareDenominator(mip.getShareDenominator());
                crd2.setSharePercentage(mip.getSharePercentage());
                crd2.setNationalAgreementNo(mip.getNationalAgreementNumber());
                crd2.setNationalAdminNo(mip.getNationalAgreementNumber());
                crd2.setContractIndicator(mip.getContractIndicator());
                crd2.setFileLineNo(mip.getLine());
                crd2.setFileLine(mip.toString());
                crd2.init();
                crd2MwnDtlMipMapper.insert(crd2);
            } else if(crdRecord instanceof InterestedPartyCategorizedContingencyPaymentsV3){
                InterestedPartyCategorizedContingencyPaymentsV3 icc = (InterestedPartyCategorizedContingencyPaymentsV3)crdRecord;
                Crd3MwnDtlIcc crd3 = new Crd3MwnDtlIcc();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(icc.getRecordType());
                crd3.setTransactionSeq(icc.getTransactionSequenceNo());
                crd3.setRecordSeq(icc.getRecordSequenceNo());
                crd3.setRefId(icc.getReferenceIdentifier());
                crd3.setExploitSourceId(icc.getExploitationSourceIdentifier());
                crd3.setCurrencyCode(icc.getCurrency());
                crd3.setSourceSocConAmt(icc.getSourceSocietyContingencyAmount());
                crd3.setSourceSocConAmtSign(icc.getSourceSocietyContingencyAmountSign());
                crd3.setRemitSocConAmt(icc.getRemittingSocietyContingencyAmount());
                crd3.setRemitSocConAmtSign(icc.getRemittingSocietyContingencyAmountSign());
                crd3.setContingencyRate(icc.getContingencyRate());
                crd3.setDistCategory(icc.getDistributionCategory());
                crd3.setIpNo(icc.getInterestedPartyNumber());
                crd3.setFileLineNo(icc.getLine());
                crd3.setFileLine(icc.toString());
                crd3.init();
                crd3MwnDtlIccMapper.insert(crd3);
            } else if(crdRecord instanceof WorkExploitationPerformanceV3){
                WorkExploitationPerformanceV3 wep = (WorkExploitationPerformanceV3)crdRecord;
                Crd3MwnDtlWep crd = new Crd3MwnDtlWep();
                crd.setFileId(listOverseasFileBase.getId());
                crd.setGroupId(0);
                crd.setRecordType(wep.getRecordType());
                crd.setTransactionSeq(wep.getTransactionSequenceNo());
                crd.setRecordSeq(wep.getRecordSequenceNo());
                crd.setRefId(wep.getReferenceIdentifier());
                crd.setDistCategory(wep.getDistributionCategory());
                crd.setExploitSourceId(wep.getExploitationSourceIdentifier());
                crd.setQuantity(wep.getQuantity());
                crd.setAggregationPeriod(wep.getAggregationPeriodType());
                crd.setExploitDate(wep.getExploitationDate());
                crd.setExploitTime(wep.getExploitationTime());
                crd.setIsrc(wep.getIsrc());
                crd.setPerformingArtistLastName(wep.getPerformingArtistLastName());
                crd.setPerformingArtistFirstName(wep.getPerformingArtistFirstName());
                crd.setSurveyType(wep.getSurveyType());
                crd.setDayOfWeekCode(wep.getDayOfWeekCode());
                crd.setBasisOfRateCal(wep.getBasisOfRateCalculation());
                crd.setMinSecPointValue(wep.getMinuteSecondPointValue() + "");
                crd.setBonusLevelPerformanceInd(wep.getBonusLevelPerformanceIndicator());
                crd.setNationalGroupingId(wep.getNationalGroupingIdentifier());
                crd.setCurrency(wep.getCurrency());
                crd.setGrossRoyaltyAmount(wep.getGrossRoyaltyAmount());
                crd.setTaxRate(wep.getTaxRate());
                crd.setTaxAmount(wep.getTaxAmount());
                crd.setCommissionAmount(wep.getCommissionAmount());
                crd.setSourceSoc(wep.getExploitationSourceIdentifier());
                crd.setSourceSocTaxAmount(wep.getSourceSocietyTaxAmount());
                crd.setSourceSocCommissionAmount(wep.getSourceSocietyCommissionAmount());
                crd.setRemitRoyaltyAmount(wep.getGrossRoyaltyAmount());
                crd.setFileLineNo(wep.getLine());
                crd.setFileLine(wep.toString());
                crd.init();
                crd3MwnDtlWepMapper.insert(crd);
            } else if(crdRecord instanceof WorkExploitationAudiovisualProgramPerformanceV3){
                WorkExploitationAudiovisualProgramPerformanceV3 wea = (WorkExploitationAudiovisualProgramPerformanceV3)crdRecord;
                Crd3MwnDtlWea crd3 = new Crd3MwnDtlWea();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(wea.getRecordType());
                crd3.setTransactionSeq(wea.getTransactionSequenceNo());
                crd3.setRecordSeq(wea.getRecordSequenceNo());
                crd3.setRefId(wea.getReferenceIdentifier());
                crd3.setDistCategory(wea.getDistributionCategory());
                crd3.setExploitSourceId(wea.getExploitationSourceIdentifier());
                crd3.setQuantity(wea.getQuantity());
                crd3.setAggregationPeriodType(wea.getAggregationPeriodType());
                crd3.setExploitDate(wea.getExploitationDate());
                crd3.setExploitTime(wea.getExploitationTime());
                crd3.setSocAvWorkId(wea.getSocietyAvWorkIdentifier());
                crd3.setDayOfWeekCode(wea.getDayOfWeekCode());
                crd3.setOrigin(wea.getOrigin());
                crd3.setCueSequence(wea.getCueSequence() + "");
                crd3.setDuration(wea.getDuration());
                crd3.setProductCatalogueNo(wea.getProductCatalogueNumber());
                crd3.setSurveyType(wea.getSurveyType());
                crd3.setBasisOfRateCal(wea.getBasisOfRateCalculation());
                crd3.setNationalGroupingId(wea.getNationalGroupingIdentifier());
                crd3.setCurrency(wea.getCurrency());
                crd3.setGrossRoyaltyAmount(wea.getGrossRoyaltyAmount() );
                crd3.setTaxRate(wea.getTaxRate() );
                crd3.setTaxAmount(wea.getTaxAmount());
                crd3.setCommissionAmount(wea.getCommissionAmount() );
                crd3.setSourceSoc(wea.getSourceSociety() + "");
                crd3.setSourceSocTaxAmount(wea.getSourceSocietyTaxAmount() );
                crd3.setSourceSocCommissionAmount(wea.getSourceSocietyCommissionAmount() );
                crd3.setRemitRoyaltyAmount(wea.getGrossRoyaltyAmount());
                crd3.setFileLineNo(wea.getLine());
                crd3.setFileLine(wea.toString());
                crd3.init();
                crd3MwnDtlWeaMapper.insert(crd3);
            } else if(crdRecord instanceof WorkExploitationRecordingV3){
                WorkExploitationRecordingV3 wer = new WorkExploitationRecordingV3();
                Crd3MwnDtlWer crd3 = new Crd3MwnDtlWer();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(wer.getRecordType());
                crd3.setTransactionSeq(wer.getTransactionSequenceNo());
                crd3.setRecordSeq(wer.getRecordSequenceNo());
                crd3.setRefId(wer.getReferenceIdentifier());
                crd3.setDistCategory(wer.getDistributionCategory());
                crd3.setExploitSourceId(wer.getExploitationSourceIdentifier());
                crd3.setQuantity(wer.getQuantity());
                crd3.setRecordedProductNo(wer.getRecordedProductNumber());
                crd3.setIsrc(wer.getIsrc());
                crd3.setCountryLicensingTerritoryCode(wer.getCountryOfLicensingTerritoryCode() + "");
                crd3.setCountryLicensingTerritoryCodeFrom(wer.getCountryOfLicensingTerritoryCodeValidFromDate());
                crd3.setCountryLicensingTerritoryName(wer.getCountryOfLicensingTerritoryAbbreviatedName());
                crd3.setCountryLicensingTerritoryNameFrom(wer.getCountryOfLicensingTerritoryAbbreviatedNameValidFromDate());
                crd3.setBasePriceType(wer.getBasePriceType());
                crd3.setSaleType(wer.getSaleType());
                crd3.setSalesPeriodStart(wer.getSalesPeriodStart());
                crd3.setSalesPeriodEnd(wer.getSalesPeriodEnd());
                crd3.setRoyaltyBasisPrice(wer.getRoyaltyBasisPrice());
                crd3.setTrackCountryTerritoryCode(wer.getTrackCountryOfOriginTerritoryCode() + "");
                crd3.setTrackCountryTerritoryCodeFrom(wer.getTrackCountryOfOriginTerritoryCodeValidFromDate());
                crd3.setTrackCountryTerritoryName(wer.getTrackCountryOfOriginTerritoryAbbreviatedName());
                crd3.setTrackCountryTerritoryNameFrom(wer.getTrackCountryOfOriginTerritoryAbbreviatedNameValidFromDate());
                crd3.setDuration(wer.getDuration());
                crd3.setTrackSharePercentage(wer.getTrackSharePercentage());
                crd3.setCommissionRate(wer.getCommissionRate());
                crd3.setNationalGroupingId(wer.getNationalGroupingIdentifier());
                crd3.setCurrency(wer.getCurrency());
                crd3.setGrossRoyaltyAmount(wer.getGrossRoyaltyAmount());
                crd3.setTaxRate(wer.getTaxRate());
                crd3.setTaxAmount(wer.getTaxAmount());
                crd3.setCommissionAmount(wer.getCommissionAmount() );
                crd3.setSourceSoc(wer.getSourceSociety() + "");
                crd3.setSourceSocTaxAmount(wer.getSourceSocietyTaxAmount());
                crd3.setSourceSocCommissionAmount(wer.getSourceSocietyCommissionAmount());
                crd3.setRemitRoyaltyAmount(wer.getRemittedRoyaltyAmount());
                crd3.setAggregationPeriodType(wer.getAggregationPeriodType() + "");
                crd3.setFileLineNo(wer.getLine());
                crd3.setFileLine(wer.toString());
                crd3.init();
                crd3MwnDtlWerMapper.insert(crd3);
            } else if(crdRecord instanceof WorkExploitationPerformanceBonusIndicationsV3){
                WorkExploitationPerformanceBonusIndicationsV3 wbi = (WorkExploitationPerformanceBonusIndicationsV3)crdRecord;
                Crd3MwnDtlWbi crd3 = new Crd3MwnDtlWbi();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(wbi.getRecordType());
                crd3.setTransactionSeq(wbi.getTransactionSequenceNo());
                crd3.setRecordSeq(wbi.getRecordSequenceNo());
                crd3.setWorkExploitRefId(wbi.getWorkExploitationReferenceIdentifier());
                crd3.setRefId(wbi.getReferenceIdentifier());
                crd3.setBonusLevelPerformanceIndicator(wbi.getBonusLevelPerformanceIndicator());
                crd3.setNationalGroupingId(wbi.getNationalGroupingIdentifier());
                crd3.setCurrency(wbi.getCurrency());
                crd3.setGrossRoyaltyAmount(wbi.getGrossRoyaltyAmount());
                crd3.setTaxRate(wbi.getTaxRate());
                crd3.setTaxAmount(wbi.getTaxAmount());
                crd3.setCommissionAmount(wbi.getCommissionAmount());
                crd3.setSourceSocCode(wbi.getSourceSociety() + "");
                crd3.setSourceSocTaxAmount(wbi.getSourceSocietyTaxAmount());
                crd3.setSourceSocComminssionAmount(wbi.getSourceSocietyCommissionAmount());
                crd3.setRemitRoyaltyAmount(wbi.getGrossRoyaltyAmount());
                crd3.setFileLineNo(wbi.getLine());
                crd3.setFileLine(wbi.toString());
                crd3.init();
                crd3MwnDtlWbiMapper.insert(crd3);
            } else if(crdRecord instanceof WorkExploitationUnidentifiedPerformanceInformation){
                WorkExploitationUnidentifiedPerformanceInformation wui = (WorkExploitationUnidentifiedPerformanceInformation)crdRecord;
                Crd2MwnDtlWui crd2 = new Crd2MwnDtlWui();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(wui.getRecordType());
                crd2.setTransactionSeq(wui.getTransactionSequenceNo());
                crd2.setRecordSeq(wui.getRecordSequenceNo());
                crd2.setFileLineNo(wui.getLine());
                crd2.setFileLine(wui.toString());
                crd2.init();
                crd2MwnDtlWuiMapper.insert(crd2);
            } else if(crdRecord instanceof AdjustmentV3){
                AdjustmentV3 adj = (AdjustmentV3)crdRecord;
                Crd3MwnDtlAdj crd3 = new Crd3MwnDtlAdj();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(adj.getRecordType());
                crd3.setTransactionSeq(adj.getTransactionSequenceNo());
                crd3.setRecordSeq(adj.getRecordSequenceNo());
                crd3.setRefId(adj.getReferenceIdentifier());
                crd3.setOrigRefId(adj.getOriginalReferenceIdentifier());
                crd3.setOrigDistId(adj.getOriginalDistributionIdentifier());
                crd3.setOrigIpNo(adj.getOriginalInterestedPartyNumber());
                crd3.setOrigIpName(adj.getOriginalIpName());
                crd3.setOrigIpFirstName(adj.getOriginalIpFirstName());
                crd3.setIncorrectShareNumerator(adj.getIncorrectShareNumerator() + "");
                crd3.setIncorrectShareDenominator(adj.getIncorrectShareDenominator() + "");
                crd3.setIncorrectSharePercent(adj.getIncorrectSharePercentage() + "");
                crd3.setCorrectShareNumerator(adj.getCorrectShareNumerator() + "");
                crd3.setCorrectShareDenominator(adj.getCorrectShareDenominator() + "");
                crd3.setCorrectSharePercent(adj.getCorrectSharePercentage() + "");
                crd3.setAdjustReasonCode(adj.getAdjustmentReasonCode());
                crd3.setAdjustReasonCommentary(adj.getAdjustmentReasonCommentary());
                crd3.setAdjustRefNo("");
                crd3.setAdjustAmount(adj.getAdjustmentAmount());
                crd3.setAdjustAmountSign(adj.getAdjustmentAmountSign());
                crd3.setOrigDistCategory(adj.getOriginalDistributionCategoryType());
                crd3.setOrigContainerId(adj.getOriginalContainerIdentifier());
                crd3.setAdjustmentCurrency("");
                crd3.setFileLineNo(adj.getLine());
                crd3.setFileLine(adj.toString());
                crd3.init();
                crd3MwnDtlAdjMapper.insert(crd3);
            } else if(crdRecord instanceof OtherInterestedParty){
                OtherInterestedParty oip = (OtherInterestedParty)crdRecord;
                Crd2MwnDtlOip crd2 = new Crd2MwnDtlOip();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(oip.getRecordType());
                crd2.setTransactionSeq(oip.getTransactionSequenceNo());
                crd2.setRecordSeq(oip.getRecordSequenceNo());
                crd2.setOtherIpDesc(oip.getOtherIpDescription());
                crd2.setOtherIpFirstName(oip.getOtherIpFirstName());
                crd2.setOtherIpName(oip.getOtherIpName());
                crd2.setFileLineNo(oip.getLine());
                crd2.setFileLine(oip.toString());
                crd2.init();
                crd2MwnDtlOipMapper.insert(crd2);
            } else if(crdRecord instanceof AdHocPaymentV3){
                AdHocPaymentV3 ahp = (AdHocPaymentV3)crdRecord;
                Crd3AhpHdr crd3 = new Crd3AhpHdr();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(ahp.getRecordType());
                crd3.setTransactionSeq(ahp.getTransactionSequenceNo());
                crd3.setRecordSeq(ahp.getRecordSequenceNo());
                crd3.setIpNo(ahp.getInterestedPartyNumber());
                crd3.setIpSoc(ahp.getSociety() + "");
                crd3.setCurrency(ahp.getCurrency());
                crd3.setPaymentAmount(ahp.getPaymentAmount());
                crd3.setPaymentSign(ahp.getPaymentSign());
                crd3.setTaxRate(ahp.getTaxRtae() );
                crd3.setTaxAmount(ahp.getTaxAmount());
                crd3.setCommissionAmount(ahp.getCommissionAmoun());
                crd3.setRemitRoyaltyAmount(ahp.getRemittedRoyaltyAmount());
                crd3.setComments(ahp.getComments());
                crd3.setFileLineNo(ahp.getLine());
                crd3.setFileLine(ahp.toString());
                crd3.init();
                crd3AhpHdrMapper.insert(crd3);
            } else if(crdRecord instanceof SocietySpecificInformation){
                SocietySpecificInformation sid = (SocietySpecificInformation)crdRecord;
                Crd2SinDtlSid crd2 = new Crd2SinDtlSid();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(sid.getRecordType());
                crd2.setTransactionSeq(sid.getTransactionSequenceNo());
                crd2.setRecordSeq(sid.getRecordSequenceNo());
                crd2.setSidType(sid.getSidType());
                crd2.setRefTransactionSeq(sid.getReferenceTransactionSequence());
                crd2.setRefRecordType(sid.getReferenceRecordSequence());
                crd2.setFileLineNo(sid.getLine());
                crd2.setFileLine(sid.toString());
                crd2.init();
                crd2SinDtlSidMapper.insert(crd2);
            } else if(crdRecord instanceof SocietySpecificInformationNotificationTransactionHeader){
                SocietySpecificInformationNotificationTransactionHeader sin = (SocietySpecificInformationNotificationTransactionHeader)crdRecord;
                Crd2SinHdr crd2 = new Crd2SinHdr();
                crd2.setFileId(listOverseasFileBase.getId());
                crd2.setGroupId(0);
                crd2.setRecordType(sin.getRecordType());
                crd2.setTransactionSeq(sin.getTransactionSequenceNo());
                crd2.setRecordSeq(sin.getRecordSequenceNo());
                crd2.setSidType(sin.getSidType());
                crd2.setFileLineNo(sin.getLine());
                crd2.setFileLine(sin.toString());
                crd2.init();
                crd2SinHdrMapper.insert(crd2);
            } else if(crdRecord instanceof RoyaltyGrandTotalsTransactionHeader){
                RoyaltyGrandTotalsTransactionHeaderV3 rgt = (RoyaltyGrandTotalsTransactionHeaderV3)crdRecord;
                Crd3RgtHdr crd3 = new Crd3RgtHdr();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(rgt.getRecordType());
                crd3.setTransactionSeq(rgt.getTransactionSequenceNo());
                crd3.setRecordSeq(rgt.getRecordSequenceNo());
                crd3.setWexRemitRoyaltyAmount(rgt.getWexRemittedRoyaltyAmount());
                crd3.setIccSourceSocContingencyAmount(rgt.getIccSourceSocietyContingencyAmount());
                crd3.setIccSourceSocContingencyAmountSign(rgt.getIccSourceSocietyContingencyAmountSign());
                crd3.setIccRemitSocContingencyAmount(rgt.getIccRemittingSocietyContingencyAmount());
                crd3.setIccRemitSocContingencyAmountSign(rgt.getIccRemittingSocietyContingencyAmountSign());
                crd3.setAdjAdjustmentAmount(rgt.getAdjAdjustmentAmount());
                crd3.setAdjAdjustmentAmountSign(rgt.getAdjAdjustmentAmountSign());
                crd3.setFeoReturnAmount(rgt.getFeoReturnAmount());
                crd3.setFeoReturnAmountSign(rgt.getFeoReturnAmountSign());
                crd3.setWepGrossRoyaltyAmount(rgt.getWepGrossRoyaltyAmount());
                crd3.setWeaGrossRoyaltyAmount(rgt.getWeaGrossRoyaltyAmount());
                crd3.setWerGrossRoyaltyAmount(rgt.getWerGrossRoyaltyAmount());
                /*crd3.setAhpGrossAmount(0L);
                crd3.setAhpGrossAmountSign("+");*/
//                crd3.setWbiGrossRoyaltyAmount(0L);
                crd3.setIccGrossContAmount(rgt.getIccSourceSocietyContingencyAmount());
                crd3.setWepRemitRoyaltyAmount(rgt.getWepGrossRoyaltyAmount());
                crd3.setWeaRemitRoyaltyAmount(rgt.getWeaGrossRoyaltyAmount());
                crd3.setWerRemitRoyaltyAmount(rgt.getWerGrossRoyaltyAmount());
//                crd3.setWbiRemitRoyaltyAmount(0L);
//                crd3.setAhpRemitRoyaltyAmount(0L);
                crd3.setAhpRemitRoyaltyAmountSign("+");
                crd3.setFileLineNo(rgt.getLine());
                crd3.setFileLine(rgt.toString());
                crd3RgtHdrMapper.insert(crd3);
            } else if(crdRecord instanceof AcknowledgementTransactionHeader){
                AcknowledgementTransactionHeader ack = (AcknowledgementTransactionHeader)crdRecord;
                Crd3AckHdr crd3 = new Crd3AckHdr();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(ack.getRecordType());
                crd3.setTransactionSeq(ack.getTransactionSequenceNo());
                crd3.setRecordSeq(ack.getRecordSequenceNo());
                crd3.setRemitSocDistId(ack.getRemittingSocietyDistributionIdentifier());
                crd3.setAccountPeriodEnd(ack.getAccountingPeriodEnd());
                crd3.setAccountPeriodStart(ack.getAccountingPeriodStart());
                crd3.setFileLineNo(ack.getLine());
                crd3.setFileLine(ack.toString());
                crd3.init();
                crd3AckHdrMapper.insert(crd3);
            } else if(crdRecord instanceof SuccessfulProcessWork){
                SuccessfulProcessWork spw = (SuccessfulProcessWork)crdRecord;
                Crd3AckDtlSpw crd3 = new Crd3AckDtlSpw();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(spw.getRecordType());
                crd3.setTransactionSeq(spw.getTransactionSequenceNo());
                crd3.setRecordSeq(spw.getRecordSequenceNo());
                crd3.setFileLineNo(spw.getLine());
                crd3.setSocWorkId(spw.getSocietyWorkIdentifier());
                crd3.setIswc(spw.getIswc() + "");
                crd3.setFileLine(spw.toString());
                crd3.init();
                crd3AckDtlSpwMapper.insert(crd3);
            } else if(crdRecord instanceof UnsuccessfulProcessAdvice){
                UnsuccessfulProcessAdvice upa = (UnsuccessfulProcessAdvice)crdRecord;
                Crd3AckDtlUpa crd3 = new Crd3AckDtlUpa();
                crd3.setFileId(listOverseasFileBase.getId());
                crd3.setGroupId(0);
                crd3.setRecordType(upa.getRecordType());
                crd3.setTransactionSeq(upa.getTransactionSequenceNo());
                crd3.setRecordSeq(upa.getRecordSequenceNo());
                crd3.setFileLineNo(upa.getLine());
                crd3.setSocWorkId(upa.getSocietyWorkIdentifier());
                crd3.setOriginalRecordSeq(upa.getOriginalRecordSequence());
                crd3.setOriginalRecordType(upa.getOriginalRecordType());
                crd3.setOriginalRefId(upa.getOriginalReferenceIdentifier());
                crd3.setOriginalTransactionSeq(upa.getOriginalTransactionSequence());
                crd3.setErrorCode(upa.getErrorCode());
                crd3.setErrorDesc(upa.getErrorDescription());
                crd3.setFileLine(upa.toString());
                crd3.init();
                crd3AckDtlUpaMapper.insert(crd3);
            }
        }

    }
}
