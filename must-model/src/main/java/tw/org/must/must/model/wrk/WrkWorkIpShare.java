package tw.org.must.must.model.wrk;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


import tw.org.must.must.common.base.BaseEntity;
import tw.org.must.must.model.util.UniqUtils;

@Table(name = "`wrk_work_ip_share`")
public class WrkWorkIpShare extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 685015252362190088L;

	/**
     * 
     */
    @Column(name = "work_id")
    @NotNull(message="WrkWorkIpShare中的workId不能为空！")
    private Long workId;

    /**
     * 
     */
    @Column(name = "work_society_code")
    @NotNull(message="WrkWorkIpShare中的workSocietyCode不能为空！")
    private Integer workSocietyCode;

    /**
     * 
     */
    @Column(name = "ip_name_no")
    @NotBlank(message="WrkWorkIpShare中ip_name_no不能为空！")
    private String ipNameNo;
    public String getIpNameNo() { return ipNameNo; }
    public void setIpNameNo(String ipNameNo) { this.ipNameNo = UniqUtils.uniqIpNameNoO(ipNameNo); }

    /**
     * 
     */
    @Column(name = "ip_base_no")
    @NotBlank(message="WrkWorkIpShare中ip_base_no不能为空！")
    private String ipBaseNo;
    public String getIpBaseNo() { return ipBaseNo; }
    public void setIpBaseNo(String ipBaseNo) { this.ipBaseNo = UniqUtils.uniqIpBaseNoO(ipBaseNo); }

    /**
     * PER=performance; MEC=mechanical;ZYN=synchronization;OD;DB
     */
    @Column(name = "right_type")
    @NotBlank(message="rightType不能为空！")
    private String rightType;
    public String getRightType() { return rightType; }
    public void setRightType(String rightType) { this.rightType = UniqUtils.uniqRightTypeO(rightType); }

    /**
     * 
     */
    @Column(name = "ip_share")
    @NotNull(message="ipShare不能为空！")
    private BigDecimal ipShare;
    public BigDecimal getIpShare() { return ipShare; }
    public void setIpShare(BigDecimal ipShare) { this.ipShare = UniqUtils.uniqShareO(ipShare); }

    /**
     * 
     */
    @Column(name = "work_ip_role")
    @NotBlank(message="workIpRole不能为空！")
    private String workIpRole;
    public String getWorkIpRole() { return workIpRole; }
    public void setWorkIpRole(String workIpRole) { this.workIpRole = UniqUtils.uniqWorkIpRoleO(workIpRole); }

    /**
     * 标记是否有争议，N,Y
     */
    @Column(name = "sd")
    private String sd;
    public String getSd() { return sd; }
    public void setSd(String sd) { this.sd = UniqUtils.uniqYnO(sd); }

    /**
     * 权利分类标识，从1开始
     */
    @Column(name = "group_indicator")
    @NotBlank(message="WrkWorkIpShare中group_indicator不能为空！")
    private String groupIndicator;
    public String getGroupIndicator() { return groupIndicator; }
    public void setGroupIndicator(String groupIndicator) { this.groupIndicator = UniqUtils.uniqGroupIndO(groupIndicator); }

    /**
     * 
     */
    @Column(name = "sip_link_id")
    private Long sipLinkId;

    /**
     * 
     */
    @Column(name = "dummy_name_roman")
    private String dummyNameRoman;

    /**
     * when IP name no is null, user is allow to input the temperate IP name
     */
    @Column(name = "dummy_name")
    private String dummyName;

    /**
     * 对应合约链id
     */
    @Column(name = "oip_link_id")
    private Long oipLinkId;

    /**
     * N:个人,L:团体
     */
    @Column(name = "ip_type")
    @NotBlank(message="ipType不能为空！")
    private String ipType;
    public String getIpType() { return ipType; }
    public void setIpType(String ipType) { this.ipType = UniqUtils.uniqIpTypeO(ipType); }

    /**
     * 
     */
    @Column(name = "org_writer_share")
    private BigDecimal orgWriterShare;
    public BigDecimal getOrgWriterShare() { return orgWriterShare; }
    public void setOrgWriterShare(BigDecimal orgWriterShare) { this.orgWriterShare = UniqUtils.uniqShareO(orgWriterShare); }

    /**
     * 
     */
    @Column(name = "agr_no")
    private String agrNo;
    public String getAgrNo() { return agrNo; }
    public void setAgrNo(String agrNo) { this.agrNo = UniqUtils.uniqAgrNoO(agrNo); }
    
    @Column(name = "work_unique_key")
    private String workUniqueKey;
	public String getWorkUniqueKey() { return workUniqueKey; }
	public void setWorkUniqueKey(String workUniqueKey) { this.workUniqueKey = UniqUtils.uniqWorkUniqKeyO(workUniqueKey); }

    @Column(name = "ref_indicator")
    private String refIndicator;
    public String getRefIndicator() { return refIndicator; }
    public void setRefIndicator(String refIndicator) { this.refIndicator = UniqUtils.uniqYnO(refIndicator); }

    @Transient
    private String name;
	public String getName() { return name; }
	public void setName(String name) { this.name = UniqUtils.uniqIpNameO(name); }
    
    @Transient 
    private String ipSocietyCode;
	public String getIpSocietyCode() { return ipSocietyCode; }
	public void setIpSocietyCode(String ipSocietyCode) { this.ipSocietyCode = UniqUtils.uniqSocCodeSO(ipSocietyCode); }
    
    @Transient 
    private String contentAgrNo;
	public String getContentAgrNo() { return contentAgrNo; }
	public void setContentAgrNo(String contentAgrNo) { this.contentAgrNo = UniqUtils.uniqAgrNoO(contentAgrNo); }

    //合约时间
    @Transient 
    private Date validFr;
	public Date getValidFr() { return validFr; }
	public void setValidFr(Date validFr) { this.validFr = UniqUtils.uniqAgrDateO(validFr); }
    
    @Transient 
    private Date validTo;
	public Date getValidTo() { return validTo; }
	public void setValidTo(Date validTo) { this.validTo = UniqUtils.uniqAgrDateO(validTo); }

    @Transient 
    private String autoExtensionInd;
	public String getAutoExtensionInd() { return autoExtensionInd; }
	public void setAutoExtensionInd(String autoExtensionInd) { this.autoExtensionInd = UniqUtils.uniqYnO(autoExtensionInd); }

    // 是否自动计算  A :自动计算 ；  M:非自动计算
    @Transient
    private String shareType;

    // 是否分配计算 0：不分配；1：分配
    @Transient
    private Integer Distributable;

    @Transient
    private String chineseName;
    public String getChineseName() { return chineseName; }
	public void setChineseName(String chineseName) { this.chineseName = UniqUtils.uniqIpNameO(chineseName); }
    
	@Transient
	private Boolean isNew;
    
	@Transient
	private BigDecimal preIpShare;
	public BigDecimal getPreIpShare() { return preIpShare; }
	public void setPreIpShare(BigDecimal preIpShare) { this.preIpShare = UniqUtils.uniqShareO(preIpShare); }

	// 用于记录回归后的比例即oipLinkId为0的比例
	@Transient
	private BigDecimal orignalWrkIpShare;
    public BigDecimal getOrignalWrkIpShare() { return orignalWrkIpShare; }
    public void setOrignalWrkIpShare(BigDecimal orignalWrkIpShare) { this.orignalWrkIpShare = UniqUtils.uniqShareO(orignalWrkIpShare); }

	// 用于记录 有合约且workIpRole为E的数据
    @Transient
	private boolean writerOp;

	// 用于记录 SE的数据方便前端取数据
    @Transient
	private boolean subPublisher;

    // 计算ipshare时的由A分配给SA的权重
    @Transient
    private BigDecimal ipShareWeighta ;

    // 计算ipshare时的由C分配给SA的权重
    @Transient
    private BigDecimal ipShareWeightc ;

    //会员有多个协会时，计算时保存比例分配。计算完以后，按比例拆分
    @Transient
    Map<Integer,BigDecimal> membershipShareMap;

    @Transient
    private BigDecimal ipsharem ;

    @Transient
    private Boolean isDist;

    @Transient
    private String associatedAgrNo;
    public String getAssociatedAgrNo() { return associatedAgrNo; }
    public void setAssociatedAgrNo(String associatedAgrNo) { this.associatedAgrNo = UniqUtils.uniqAgrNoO(associatedAgrNo); }

    public BigDecimal getIpShareWeighta() {
        return ipShareWeighta;
    }

    public void setIpShareWeighta(BigDecimal ipShareWeighta) {
        this.ipShareWeighta = ipShareWeighta;
    }

    public BigDecimal getIpShareWeightc() {
        return ipShareWeightc;
    }

    public void setIpShareWeightc(BigDecimal ipShareWeightc) {
        this.ipShareWeightc = ipShareWeightc;
    }


	public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }




    public Long getSipLinkId() {
        return sipLinkId;
    }

    public void setSipLinkId(Long sipLinkId) {
        this.sipLinkId = sipLinkId;
    }

    public String getDummyNameRoman() {
        return dummyNameRoman;
    }

    public void setDummyNameRoman(String dummyNameRoman) {
        this.dummyNameRoman = dummyNameRoman;
    }

    public String getDummyName() {
        return dummyName;
    }

    public void setDummyName(String dummyName) {
        this.dummyName = dummyName;
    }

    public Long getOipLinkId() {
        return oipLinkId;
    }

    public void setOipLinkId(Long oipLinkId) {
        this.oipLinkId = oipLinkId;
    }

    public String  getWorkUniqueNO() {
		return (this.workId==null?"0":workId.toString())+this.getWorkSocietyCode();
	}

	public Boolean getIsNew() {
		return isNew;
	}

	public void setIsNew(Boolean isNew) {
		this.isNew = isNew;
	}

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public Integer getDistributable() {
        return Distributable;
    }

    public void setDistributable(Integer distributable) {
        Distributable = distributable;
    }


    public boolean isWriterOp() {
        return writerOp;
    }

    public void setWriterOp(boolean writerOp) {
        this.writerOp = writerOp;
    }

    public boolean isSubPublisher() {
        return subPublisher;
    }

    public void setSubPublisher(boolean subPublisher) {
        this.subPublisher = subPublisher;
    }

    public Map<Integer, BigDecimal> getMembershipShareMap() {
        return membershipShareMap;
    }

    public void setMembershipShareMap(Map<Integer, BigDecimal> membershipShareMap) {
        this.membershipShareMap = membershipShareMap;
    }

    public BigDecimal getIpsharem() {
        return ipsharem;
    }

    public void setIpsharem(BigDecimal ipsharem) {
        this.ipsharem = ipsharem;
    }


    public Boolean getDist() {
        return isDist;
    }

    public void setDist(Boolean dist) {
        isDist = dist;
    }

}