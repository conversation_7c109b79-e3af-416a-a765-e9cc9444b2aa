package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;


import tw.org.must.must.core.parse.youtube.tsv.entity.v1_3.AS0102;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_3.MW0102;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_3.RU0102;

import java.util.List;

public class TsvCommonObject {

	private As0101 as0101;

	private AS0102 as0102;
	
	private Mw0101 mw0101;

	private MW0102 mw0102;
	
	private List<Ru0101> ru0101List;
	private List<RU0102> ru0102List;
	
	private List<Su0302> su0302List;

	public boolean isAVOD;

	public As0101 getAs0101() {
		return as0101;
	}

	public void setAs0101(As0101 as0101) {
		this.as0101 = as0101;
	}

	public List<Ru0101> getRu0101List() {
		return ru0101List;
	}

	public void setRu0101List(List<Ru0101> ru0101List) {
		this.ru0101List = ru0101List;
	}

	public List<Su0302> getSu0302List() {
		return su0302List;
	}

	public void setSu0302List(List<Su0302> su0302List) {
		this.su0302List = su0302List;
	}

	public Mw0101 getMw0101() {
		return mw0101;
	}

	public void setMw0101(Mw0101 mw0101) {
		this.mw0101 = mw0101;
	}

	public boolean isAVOD() {
		return isAVOD;
	}

	public void setAVOD(boolean AVOD) {
		isAVOD = AVOD;
	}

	public AS0102 getAs0102() {
		return as0102;
	}

	public void setAs0102(AS0102 as0102) {
		this.as0102 = as0102;
	}

	public List<RU0102> getRu0102List() {
		return ru0102List;
	}

	public void setRu0102List(List<RU0102> ru0102List) {
		this.ru0102List = ru0102List;
	}

	public MW0102 getMw0102() {
		return mw0102;
	}

	public void setMw0102(MW0102 mw0102) {
		this.mw0102 = mw0102;
	}
}
