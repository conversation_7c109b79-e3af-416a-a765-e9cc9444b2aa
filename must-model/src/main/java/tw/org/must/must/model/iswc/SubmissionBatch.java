package tw.org.must.must.model.iswc;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: SubmissionBatch
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/5/19 10:15
 */
@Data
public class SubmissionBatch implements Serializable {
    private static final long serialVersionUID = -145592685768666272L;
    private Long submissionId;
    private Submission submission;
    private Rejection rejection;
}
