package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Table(name = "`wrk_work_title`")
public class WrkWorkTitle extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@Column(name = "work_id")
	@NotNull(message = "WrkWorkTitle中的workId不能为空！")
	private Long workId;

	/**
	 * 
	 */
	@Column(name = "work_society_code")
	@NotNull(message = "WrkWorkTitle中的workSocietyCode不能为空！")
	private Integer workSocietyCode;

	/**
	 * 
	 */
	@Column(name = "sub_title_id")
	private Long subTitleId;

	/**
	 * 英文标题
	 */
	@Column(name = "title")
	@NotBlank(message = "作品主title必填！")
	private String title;

	/**
	 * 标题类别
	 */
	@Column(name = "title_type")
	private String titleType;

	/**
	 * 曲目风格
	 */
	@Column(name = "genre_code")
	private String genreCode;

	/**
	 * 语言编码
	 */
	@Column(name = "language_code")
	private String languageCode;

	/**
	 * 
	 */
	@Column(name = "duration_m")
	private Integer durationM;

	/**
	 * 
	 */
	@Column(name = "duration_s")
	private Integer durationS;

	/**
	 * 
	 */
	@Column(name = "gener_dtl_flag")
	private String generDtlFlag;

	/**
	 * Y or N
	 */
	@Column(name = "transfer")
	private String transfer;

	/**
	 * 填写协会
	 */
	@Column(name = "input_soc")
	private Integer inputSoc;

	/**
	 * 剧集编号
	 */
	@Column(name = "episode_no")
	private String episodeNo;

	/**
	 * 
	 */
	@Column(name = "user_id")
	private Long userId;

	/**
	 * 
	 */
	@Column(name = "user_name")
	private String userName;

	@Column(name = "title_en")
	private String titleEn;
	@Column(name = "work_unique_key")
	private String workUniqueKey;

	@Transient
	private String workType;

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

	public Integer getWorkSocietyCode() {
		return workSocietyCode;
	}

	public void setWorkSocietyCode(Integer workSocietyCode) {
		this.workSocietyCode = workSocietyCode;
	}

	public Long getSubTitleId() {
		return subTitleId;
	}

	public void setSubTitleId(Long subTitleId) {
		this.subTitleId = subTitleId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTitleType() {
		return titleType;
	}

	public void setTitleType(String titleType) {
		this.titleType = titleType;
	}

	public String getGenreCode() {
		return genreCode;
	}

	public void setGenreCode(String genreCode) {
		this.genreCode = genreCode;
	}

	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	public Integer getDurationM() {
		return durationM;
	}

	public void setDurationM(Integer durationM) {
		this.durationM = durationM;
	}

	public Integer getDurationS() {
		return durationS;
	}

	public void setDurationS(Integer durationS) {
		this.durationS = durationS;
	}

	public String getGenerDtlFlag() {
		return generDtlFlag;
	}

	public void setGenerDtlFlag(String generDtlFlag) {
		this.generDtlFlag = generDtlFlag;
	}

	public String getTransfer() {
		return transfer;
	}

	public void setTransfer(String transfer) {
		this.transfer = transfer;
	}

	public Integer getInputSoc() {
		return inputSoc;
	}

	public void setInputSoc(Integer inputSoc) {
		this.inputSoc = inputSoc;
	}

	public String getEpisodeNo() {
		return episodeNo;
	}

	public void setEpisodeNo(String episodeNo) {
		this.episodeNo = episodeNo;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getTitleEn() {
		return titleEn;
	}

	public void setTitleEn(String titleEn) {
		this.titleEn = titleEn;
	}

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}
}