package tw.org.must.must.model.dist.vo.v800.mem;

import lombok.Data;
import tw.org.must.must.common.util.LocalCommonMethodUtils;
import tw.org.must.must.model.dist.vo.v800.Data800;

import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2021/5/31 11:45
 * @Version:1.0
 * @Description:
 */
@Data
public class DataMem800 extends Data800 {
    private String handlingCharges;
    private BasesMem800 bases;

    public String getHandlingCharges() {
        return LocalCommonMethodUtils.formateNoScale(handlingCharges);
    }
}