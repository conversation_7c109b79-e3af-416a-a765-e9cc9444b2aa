package tw.org.must.must.model.dist.vo.v750;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: hanDa
 * @Date: 2021/5/20 16:05
 * @Version:1.0
 * @Description:
 */
@Data
public class AccountDetail750 {
    private String distNo;
    private String balance;
    private String bankAccountName;

    public String getBalance() {
        return "$".concat(balance);
    }
}