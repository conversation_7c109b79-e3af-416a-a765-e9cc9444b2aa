package tw.org.must.must.model.iswc;

import lombok.Data;

import java.util.Objects;

/**
 * @ClassName: WorkIdAndSocietyCode
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/5/19 10:53
 */
@Data
public class WorkIdAndSocietyCode {
    private Integer workSocietyCode;
    private Long workId;

    public WorkIdAndSocietyCode(Integer workSocietyCode, Long workId) {
        this.workSocietyCode = workSocietyCode;
        this.workId = workId;
    }

    public WorkIdAndSocietyCode() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WorkIdAndSocietyCode that = (WorkIdAndSocietyCode) o;
        return workSocietyCode.equals(that.workSocietyCode) &&
                workId.equals(that.workId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workSocietyCode, workId);
    }
}
