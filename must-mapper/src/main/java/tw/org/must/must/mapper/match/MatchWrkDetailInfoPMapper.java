package tw.org.must.must.mapper.match;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWrkDetailInfoP;
import tw.org.must.must.model.match.vo.MatchWrkDetailInfoPVo;

import java.util.List;

@Repository
public interface MatchWrkDetailInfoPMapper extends BaseMapper<MatchWrkDetailInfoP> {

	Integer insertOrUpdateByList(List<MatchWrkDetailInfoP> list);

	List<MatchWrkDetailInfoPVo> getMatchWrkDetailInfoPByUniqueKeyMd5(String uniqueKeyMd5);


}