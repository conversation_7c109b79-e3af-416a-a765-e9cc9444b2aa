package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_mechanical_sc`")
public class DistMechanicalSc extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`producer_id`")
    private Long producerId;

    /**
     * 产品名称
     */
    @Column(name = "`producer_name`")
    private String producerName;

    /**
     * 收款批次号
     */
    @Column(name = "`batch_no`")
    private Integer batchNo;

    /**
     * 货币
     */
    @Column(name = "`currency`")
    private String currency;

    /**
     * 金额
     */
    @Column(name = "`amount`")
    private String amount;

    /**
     * 转换为本地货币金额
     */
    @Column(name = "`local_amount`")
    private String localAmount;

    /**
     * 汇率
     */
    @Column(name = "`exchange_rate`")
    private String exchangeRate;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public Long getProducerId() {
        return producerId;
    }

    public void setProducerId(Long producerId) {
        this.producerId = producerId;
    }

    public String getProducerName() {
        return producerName;
    }

    public void setProducerName(String producerName) {
        this.producerName = producerName;
    }

    public Integer getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(Integer batchNo) {
        this.batchNo = batchNo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getLocalAmount() {
        return localAmount;
    }

    public void setLocalAmount(String localAmount) {
        this.localAmount = localAmount;
    }

    public String getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(String exchangeRate) {
        this.exchangeRate = exchangeRate;
    }



}