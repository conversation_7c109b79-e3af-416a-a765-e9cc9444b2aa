package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "`dist_overseas_ip_share`")
public class DistOverseasIpShare extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    /**
     *
     */
    @Column(name = "`ip_base_no`")
    private String ipBaseNo;

    /**
     * 
     */
    @Column(name = "`ip_name_no`")
    private String ipNameNo;

    /**
     * 作品ipshare，根据合约实际计算
     */
    @Column(name = "`work_ip_share`")
    private BigDecimal workIpShare;

    /**
     * 作品ipshare，根据合约实际计算
     */
    @Column(name = "`work_ip_role`")
    private BigDecimal workIpRole;

    /**
     *
     */
    @Column(name = "`ip_name`")
    private String ipName;


    /**
     * 
     */
    @Column(name = "`ip_soc`")
    private String ipSoc;


    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getIpSoc() {
        return ipSoc;
    }

    public void setIpSoc(String ipSoc) {
        this.ipSoc = ipSoc;
    }

    public BigDecimal getWorkIpShare() {
		return workIpShare;
	}

	public void setWorkIpShare(BigDecimal workIpShare) {
		this.workIpShare = workIpShare;
	}


    public String getIpName() {
        return ipName;
    }

    public void setIpName(String ipName) {
        this.ipName = ipName;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public BigDecimal getWorkIpRole() {
        return workIpRole;
    }

    public void setWorkIpRole(BigDecimal workIpRole) {
        this.workIpRole = workIpRole;
    }
}