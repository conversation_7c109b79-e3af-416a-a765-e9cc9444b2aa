<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="DistAutoPay700_childTwo" pageWidth="800" pageHeight="842" columnWidth="800" leftMargin="0"
              rightMargin="0" topMargin="0" bottomMargin="0" uuid="389ae5dd-4967-4d52-af33-9091dcd636da">
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="firstDescribe" class="java.lang.String"/>
    <field name="secondDescribe" class="java.lang.String"/>
    <field name="thirdDescribe" class="java.lang.String"/>
    <field name="performRoyalties" class="java.lang.String"/>
    <field name="salesTaxAmount" class="java.lang.String"/>
    <field name="royaltyAmount" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <detail>
        <band height="88" splitType="Stretch">
            <textField>
                <reportElement x="20" y="0" width="410" height="20" uuid="2d32741c-3387-44bb-bd60-f9efd9d9a6f5"/>
                <textFieldExpression><![CDATA[$F{firstDescribe}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="523" y="20" width="100" height="20" uuid="76a482d1-9edd-440b-b8e3-99a28315b276"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{salesTaxAmount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="523" y="50" width="100" height="20" uuid="4df41b28-95e5-4e6e-99f1-67e63524fe0b"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{royaltyAmount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="523" y="0" width="100" height="20" uuid="737fcb54-64a7-4f48-8958-01ddd6bb714d"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{performRoyalties}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="20" y="20" width="410" height="20" uuid="7164dbf7-1f43-46de-8f4b-cffccc28a134"/>
                <textFieldExpression><![CDATA[$F{secondDescribe}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="20" y="50" width="410" height="20" uuid="108eb7aa-833c-4cce-9d20-654e049b79a6"/>
                <textFieldExpression><![CDATA[$F{thirdDescribe}]]></textFieldExpression>
            </textField>
            <line>
                <reportElement x="521" y="44" width="101" height="1" uuid="5dcdeefe-8a34-4590-87d2-70022156e99e"/>
            </line>
        </band>
    </detail>
</jasperReport>
