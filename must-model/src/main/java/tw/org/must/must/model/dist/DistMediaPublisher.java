package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`dist_media_publisher`")
public class DistMediaPublisher extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`pa_name_no`")
    private String paNameNo;

    /**
     * 
     */
    @Column(name = "`pa_name`")
    private String paName;

    /**
     * 
     */
    @Column(name = "`ip_base_no`")
    private String ipBaseNo;

    /**
     * ip权利类别固定DB
     */
    @Column(name = "`right_code`")
    private String rightCode;

    /**
     * 开始有效期
     */
    @Column(name = "`valid_from`")
    private Date validFrom;

    /**
     * 结束有效期
     */
    @Column(name = "`valid_to`")
    private Date validTo;

    /**
     * Y or N
     */
    @Column(name = "`is_include`")
    private String isInclude;

    /**
     * dist_media_publisher.id
     */
    @Column(name = "`version_id`")
    private Long versionId;

    /**
     * 0（禁用），1（启用）
     */
    @Column(name = "`status`")
    private Integer status;

    public String getPaNameNo() {
        return paNameNo;
    }

    public void setPaNameNo(String paNameNo) {
        this.paNameNo = paNameNo;
    }

    public String getPaName() {
        return paName;
    }

    public void setPaName(String paName) {
        this.paName = paName;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getRightCode() {
        return rightCode;
    }

    public void setRightCode(String rightCode) {
        this.rightCode = rightCode;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

 

    public String getIsInclude() {
		return isInclude;
	}

	public void setIsInclude(String isInclude) {
		this.isInclude = isInclude;
	}

	public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }



}