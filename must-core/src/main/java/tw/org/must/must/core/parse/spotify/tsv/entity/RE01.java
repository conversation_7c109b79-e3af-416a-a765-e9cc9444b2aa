package tw.org.must.must.core.parse.spotify.tsv.entity;

public class RE01 extends BaseTsvEntity{

    private String BlockId;
    private String ReleaseReference;
    private String DspReleaseId;
    private String ProprietaryReleaseId;
    private String CatalogNumber;
    private String ICPN;
    private String DisplayArtistName;
    private String DisplayArtistPartyId;
    private String Title;
    private String SubTitle;
    private String ReleaseType;
    private String Label;
    private String PLine;
    private String DataProvider;

    public String getBlockId() {
        return BlockId;
    }

    public void setBlockId(String blockId) {
        BlockId = blockId;
    }

    public String getReleaseReference() {
        return ReleaseReference;
    }

    public void setReleaseReference(String releaseReference) {
        ReleaseReference = releaseReference;
    }

    public String getDspReleaseId() {
        return DspReleaseId;
    }

    public void setDspReleaseId(String dspReleaseId) {
        DspReleaseId = dspReleaseId;
    }

    public String getProprietaryReleaseId() {
        return ProprietaryReleaseId;
    }

    public void setProprietaryReleaseId(String proprietaryReleaseId) {
        ProprietaryReleaseId = proprietaryReleaseId;
    }

    public String getCatalogNumber() {
        return CatalogNumber;
    }

    public void setCatalogNumber(String catalogNumber) {
        CatalogNumber = catalogNumber;
    }

    public String getICPN() {
        return ICPN;
    }

    public void setICPN(String ICPN) {
        this.ICPN = ICPN;
    }

    public String getDisplayArtistName() {
        return DisplayArtistName;
    }

    public void setDisplayArtistName(String displayArtistName) {
        DisplayArtistName = displayArtistName;
    }

    public String getDisplayArtistPartyId() {
        return DisplayArtistPartyId;
    }

    public void setDisplayArtistPartyId(String displayArtistPartyId) {
        DisplayArtistPartyId = displayArtistPartyId;
    }

    public String getTitle() {
        return Title;
    }

    public void setTitle(String title) {
        Title = title;
    }

    public String getSubTitle() {
        return SubTitle;
    }

    public void setSubTitle(String subTitle) {
        SubTitle = subTitle;
    }

    public String getReleaseType() {
        return ReleaseType;
    }

    public void setReleaseType(String releaseType) {
        ReleaseType = releaseType;
    }

    public String getLabel() {
        return Label;
    }

    public void setLabel(String label) {
        Label = label;
    }

    public String getPLine() {
        return PLine;
    }

    public void setPLine(String PLine) {
        this.PLine = PLine;
    }

    public String getDataProvider() {
        return DataProvider;
    }

    public void setDataProvider(String dataProvider) {
        DataProvider = dataProvider;
    }
}
