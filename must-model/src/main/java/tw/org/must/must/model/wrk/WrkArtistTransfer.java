package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`wrk_artist_transfer`")
public class WrkArtistTransfer extends BaseEntity {

    /**
     * 
     */
    @Column(name = "src_artist_id")
    private Long srcArtistId;

    /**
     * 
     */
    @Column(name = "dest_artist_id")
    private Long destArtistId;

    /**
     * 处理时间
     */
    @Column(name = "process_date")
    private Date processDate;

    /**
     * T=Transfered, W=Waiting
     */
    @Column(name = "status")
    private String status;

    /**
     * 
     */
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "user_name")
    private String userName;

    /**
     * 转换时间
     */
    @Column(name = "transfer_time")
    private Date transferTime;

    /**
     * 
     */
    @Column(name = "task_id")
    private Integer taskId;

    /**
     * 
     */
    @Column(name = "is_ref_tfr")
    private Boolean reftfr;

    private WrkArtist from ;

    private WrkArtist to;

    public Long getSrcArtistId() {
        return srcArtistId;
    }

    public void setSrcArtistId(Long srcArtistId) {
        this.srcArtistId = srcArtistId;
    }

    public Long getDestArtistId() {
        return destArtistId;
    }

    public void setDestArtistId(Long destArtistId) {
        this.destArtistId = destArtistId;
    }

    public Date getProcessDate() {
        return processDate;
    }

    public void setProcessDate(Date processDate) {
        this.processDate = processDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getTransferTime() {
        return transferTime;
    }

    public void setTransferTime(Date transferTime) {
        this.transferTime = transferTime;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Boolean getReftfr() {
        return reftfr;
    }

    public void setReftfr(Boolean reftfr) {
        this.reftfr = reftfr;
    }

    public WrkArtist getFrom() {
        return from;
    }

    public void setFrom(WrkArtist from) {
        this.from = from;
    }

    public WrkArtist getTo() {
        return to;
    }

    public void setTo(WrkArtist to) {
        this.to = to;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}