package tw.org.must.must.core.parse.youtube.tsv.handle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;


import java.io.File;
import java.io.IOException;
import java.util.*;


@Slf4j
public abstract class YoutubeTsvRowReadHandleAbstract {

    //解析头部
    Map<String, String> headerMap = new HashMap<>();

    /**
     * Youtube  解析tsv类型文件
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常
     */
    public void parse(String filepath) {
        LineIterator lineIterator = null;

        List<String> lines = new ArrayList<>();
        String result = "";
        boolean isBreak = false;
        //标志位，用于标识每一个AS01.01
        Integer flag = 0;
        int i = 0;
        try {
           lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            while (lineIterator.hasNext()) {
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                log.info("========处理第{}行, line: {}", i, line);



            }
            // 最後剩餘數據入庫


        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public  void dealWithString(String line){
        if (line.startsWith("#")) {
            //解析标题，全部解析完再去解析内容
            String[] split = StringUtils.splitPreserveAllTokens(line, "\t");
            split[0] = split[0].replace("#", "");
            headerMap.put(split[0], line);
        } else {
            //数据行

            dealLine(line);
        }
    }
    /**
     * @param line      当前解析的内容（数据）
     * @return
     */
    protected int dealLine(String line) {
        line = line.replace("\\\\\t", "/").replace("\\\t", "\\").replace("\\", "/");
        String[] lineSplit = StringUtils.splitPreserveAllTokens(line, "\t");
        String header = headerMap.get(lineSplit[0]);
        String[] headerSplit = StringUtils.splitPreserveAllTokens(header, "\t");
        if (lineSplit.length != headerSplit.length) {
            //数据与标题个数不一致，则忽略该行后面的，提示下
            log.info("=================数据与标题匹配不上======================");
            log.info("head: " + header);
            log.info("line: " + line);
        }
        try {
            Map<String, String> map = this.convert2Map(headerSplit, lineSplit);
            return this.parseData(map);
        } catch (Exception e) {
            log.error("convert error,the error is " + e.getStackTrace());
        }
        return 1;
    }

    protected abstract int parseData(Map<String, String> map);

    private Map<String, String> convert2Map(String[] headerSplit, String[] lineSplit) {
        Map<String, String> map = new HashMap<>();
        headerSplit[0] = headerSplit[0].replace("#", "");
        for (int i = 0; i < headerSplit.length; i++) {
            String value = null;
            // 当标题与数据的长度不一致，忽略缺失数据（多余数据）
            if (i < lineSplit.length) {
                value = lineSplit[i];
            }
            map.put(headerSplit[i], value);
        }
        return map;
    }

}
