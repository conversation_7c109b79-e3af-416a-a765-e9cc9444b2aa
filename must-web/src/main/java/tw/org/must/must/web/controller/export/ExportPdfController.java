package tw.org.must.must.web.controller.export;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.common.vcp.CommonUtil;
import tw.org.must.must.core.report.SingleSessionReport;
import tw.org.must.must.core.service.agr.AgrAgreementTerritoryService;
import tw.org.must.must.core.service.agr.AgrContentService;
import tw.org.must.must.core.service.calcIpShare.CalcIpShareService;
import tw.org.must.must.core.service.export.ExportPdfService;
import tw.org.must.must.core.service.list.ListBasicFileBaseService;
import tw.org.must.must.core.service.list.ListCategoryService;
import tw.org.must.must.core.service.list.ListFilePathChangeService;
import tw.org.must.must.core.service.ref.RefGenreDtlService;
import tw.org.must.must.core.service.ref.RefSocietyService;
import tw.org.must.must.core.service.ref.RefTerritoryRelationService;
import tw.org.must.must.core.service.wrk.*;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.dto.mbr.AutoPayMemberInfoVo;
import tw.org.must.must.dto.wrk.FicheExportParam;
import tw.org.must.must.dto.wrk.WrkWorkDto;
import tw.org.must.must.model.agr.AgrAgreementTerritory;
import tw.org.must.must.model.agr.AgrContent;
import tw.org.must.must.model.list.ListBasicFileBase;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.ref.RefTerritoryRelation;
import tw.org.must.must.model.report.JasperParam;
import tw.org.must.must.model.report.SingleCategory;
import tw.org.must.must.model.report.SingleListNo;
import tw.org.must.must.model.report.SingleSessionBase;
import tw.org.must.must.model.wrk.*;
import tw.org.must.report.export.PdfReport;
import tw.org.must.report.export.model.autoPay.AutoPayMemberPdf;
import tw.org.must.report.export.model.fiche.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "pdf")
@RestController
@RequestMapping("/export/pdf")
public class ExportPdfController {

    private static final Logger logger = LoggerFactory.getLogger(ExportPdfController.class);

    @Autowired
    private SingleSessionReport singleSessionReport;
    @Autowired
    private ListCategoryService listCategoryService;
    @Autowired
    private ListFilePathChangeService listFilePathChangeService;
    @Autowired
    private ExportPdfService exportPdfService;

    @Autowired
    private ListBasicFileBaseService listBasicFileBaseService;

    @Autowired
    private RefTerritoryRelationService refTerritoryRelationService;

    @ApiOperation(value = "单场次pdf")
    @PostMapping("/singleSession")
    public Result<Void> singleSession(HttpServletResponse response, @RequestBody SingleListNo listNo) {

        if (null == listNo) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數不能爲空！");
        }
        if (null == listNo.getType()) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數Type不能爲空！");
        }
        if (listNo.getType() == 1) {
            for (SingleCategory category : listNo.getCategories()) {
                if (StringUtils.isEmpty(category.getCategoryCode())) {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), "categoryCode不能爲空！");
                }

                List<ListBasicFileBase> listBasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(category.getCategoryCode(), null, null);
                listBasicFileBaseList = listBasicFileBaseList.stream().filter(x -> StringUtils.equals("S", x.getBatchType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(listBasicFileBaseList)) {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("不存在該categoryCode【%s】", category.getCategoryCode()));
                }
            }
        } else if (listNo.getType() == 2) {
            if (StringUtils.isEmpty(listNo.getStartCategroyCode())) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "From Category Code 不能爲空！");
            }
            if (StringUtils.isEmpty(listNo.getEndCategoryCode())) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "To Category Code 不能爲空！");
            }
            /*if (!listCategoryService.checkListCategoryExistOrNot(listNo.getStartCategroyCode()) && !listCategoryService.checkListCategoryExistOrNot(listNo.getEndCategoryCode())) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("From Category Code【%s】 和 To Category Code【%s】 都不存在！單場次解析生成的第一次categoryCode，需要先Dist【is_dist = Y】",
                        listNo.getStartCategroyCode(), listNo.getEndCategoryCode()));
            }*/
        } else if (listNo.getType() == 3) {
            // 時間段校驗
        }

        try {
            List<String> urls = singleSessionReport.singleSessionPdf(listNo);
            if (urls.isEmpty()) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "該條件内沒有查詢到對應導出的數據！");
            }
            if (CollectionUtils.isNotEmpty(urls)) {
                String url = urls.get(0);
                File file = new File(url);
                String path = listFilePathChangeService.getShareFilePath(file.getParent());
                return new Result<>(HttpStatus.OK.value(), String.format("導出成功，請到目錄【%s】下查看！", path));
            }
        } catch (Exception e) {
            logger.error("下载错误：", e);
            logger.error("response.isCommitted() = " + response.isCommitted());
            if (!response.isCommitted()) {
                response.reset();
            }
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
        return new Result<>(HttpStatus.OK.value(), null);
    }

    @ApiOperation(value = "test")
    @PostMapping("/test")
    public ResponseEntity test(@RequestBody SingleListNo listNo) throws Exception {

        String JRXML_PATH = "/ireport";

        Long userId = LoginUtil.getUserId();

        String path = "/";
        List<JasperParam> jaspers = new ArrayList();
        List<SingleSessionBase> bases = new ArrayList<>();
        Map param = new HashMap();
        param.put("categoryCode", "TEST");
        param.put("exportDate", "aaaa");
        param.put("reportId", "1111");
        param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
        jaspers.add(new JasperParam(param, JSON.toJSONString(bases).getBytes()));
        String outFile = path + "/singer-report/" + DateParse.format(new Date(), "yyyyMMdd") + "/"
                + userId + "/category-" + "aaaa.pdf";
        PdfReport.exprtPdf(outFile, jaspers, JRXML_PATH + "/jrxml/SingleSession_main.jrxml");

        return new ResponseEntity(HttpStatus.OK);
    }

    @Autowired
    private WrkWorkService workService;
    @Autowired
    private WrkWorkTitleService workTitleService;
    @Autowired
    private WrkWorkArtistMergeService wrkWorkArtistMergeService;
    @Autowired
    private WrkArtistService wrkArtistService;
    @Autowired
    private CalcIpShareService calcIpShareService;
    @Autowired
    private AgrContentService agrContentService;
    @Autowired
    private AgrAgreementTerritoryService agrAgreementTerritoryService;
    @Autowired
    private RefSocietyService refSocietyService;
    @Autowired
    private WrkWorkComponentService wrkWorkComponentService;
    @Autowired
    private RefGenreDtlService refGenreDtlService;

    @Value("${list.file.uploadTempPath}")
    private String uploadTempPath;

    @ApiOperation(value = "work导出")
    @GetMapping("/work")
    public void exportWork(HttpServletResponse response, @RequestParam(value = "workId") Long workId, @RequestParam(value = "workSocietyCode") Integer workSocietyCode,
                           @RequestParam(value = "year") String year, @RequestParam(value = "countryCode") String countryCode) throws IOException {
        if (null == workId || null == workSocietyCode) {
            return;
        }
        WrkWork work = workService.getWrkWorkByWorkId(workId, workSocietyCode);
        if (null == work) {
            return;
        }
        String fileName = "";
        if (StringUtils.equals("AV", work.getWorkType())) {
            //TODO 另一个PDF
            LargeCueData largeCueData = generateLargeCueData(workId, workSocietyCode, work, year,null,null,null);
            List<JasperParam> jaspers = new ArrayList();
            jaspers.add(new JasperParam(JSON.parseObject(JSON.toJSONString(largeCueData), Map.class), JSON.toJSONString(largeCueData.getFieldList()).getBytes()));
            fileName = uploadTempPath + File.separator + "large_cue_sheets_" + System.currentTimeMillis() + ".pdf";
            File parentFile = new File(fileName).getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            try {
                PdfReport.exprtPdf(fileName, jaspers, "/ireport/jrxml/large_cue_sheets1.jrxml");
            } catch (Exception e) {
                logger.error(String.format("work【%s】导出失败，msg: ", work.getWorkType()), e);
            }

        } else {
            LargeFicheRptData ficheRptData = generateLargeFicheRptData(workId, workSocietyCode, work, year, countryCode,null,null,null);
            List<JasperParam> jaspers = new ArrayList();
            jaspers.add(new JasperParam(new HashMap<>(), JSON.toJSONString(ficheRptData).getBytes()));
            fileName = uploadTempPath + File.separator + "large_fiche_rpt_" + System.currentTimeMillis() + ".pdf";
            File parentFile = new File(fileName).getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            try {
                PdfReport.exprtPdf(fileName, jaspers, "/ireport/jrxml/large_fiche_rpt.jrxml");
            } catch (Exception e) {
                logger.error(String.format("work【%s】导出失败，msg: ", work.getWorkType()), e);
            }
        }
        File f = null;
        try {
            f = new File(fileName);
            // f.deleteOnExit();
            String outPutFileName = URLEncoder.encode(fileName.substring(fileName.lastIndexOf(File.separator) + 1), "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/pdf");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(new String(outPutFileName.getBytes("utf-8")), "UTF-8"));
            response.setCharacterEncoding("utf-8");
            FileInputStream in = new FileInputStream(f);
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[1024];
            while ((in.read(b)) != -1) {
                out.write(b);
            }
            out.flush();
            in.close();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != f && f.exists()) {
                f.deleteOnExit();
            }
        }

    }

    @ApiOperation(value = "work导出（多个）")
    @PostMapping("/workList")
    public void exportWorkList(HttpServletResponse response, @RequestBody FicheExportParam ficheExportParam) throws IOException {
        List<WrkWorkDto> workDtoList = ficheExportParam.getWorkDtoList();
        if (CollectionUtils.isEmpty(workDtoList)) {
            return;
        }
        Date date = new Date();
        String fileUrl = uploadTempPath + File.separator + DateParse.format(date, DateParse.patternDate) + File.separator + DateParse.format(date, "HHmmss");


        List<LargeFicheRptData> ficheRptDataList = new ArrayList<>(workDtoList.size());
        List<LargeCueData> largeCueDataList = new ArrayList<>(workDtoList.size());

        List<WrkWork> wrkWorkList = workService.getWrkWorkByWorkUniqueKeys(workDtoList.stream().map(t -> CommonUtil.getWorkUniqueKeyByWrkSocAndNum(t.getSoc(), t.getWorkId())).collect(Collectors.toList()));
        long count = wrkWorkList.stream().filter(t -> "AV".equals(t.getWorkType())).count();
        if (count > 0 && count < (long) workDtoList.size()) {
            return; // 需要同一类型的才能导出一个pdf
        }
        Map<String, WrkWork> wrkWorkMap = wrkWorkList.stream().collect(Collectors.toMap(WrkWork::getWorkUniqueKey, Function.identity(), (a, b) -> a));
        int i = 0;
        for (WrkWorkDto wrkWorkDto : workDtoList) {
            Long workId = wrkWorkDto.getWorkId();
            Integer workSocietyCode = wrkWorkDto.getSoc();
            String year = wrkWorkDto.getYear();

            WrkWork work = wrkWorkMap.get(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(workSocietyCode, workId));
            if (null == work) {
                return;
            }
            if (StringUtils.equals("AV", work.getWorkType())) {
                LargeCueData largeCueData = generateLargeCueData(workId, workSocietyCode, work, year,ficheExportParam.getAtFlag(),ficheExportParam.getRightType(),ficheExportParam.getLanguage());
                largeCueDataList.add(largeCueData);
            } else {
                LargeFicheRptData ficheRptData = generateLargeFicheRptData(workId, workSocietyCode, work, year, ficheExportParam.getCountryCode(),ficheExportParam.getAtFlag(),ficheExportParam.getRightType(),ficheExportParam.getLanguage());
                String remark = String.format("No.%s", StringUtils.leftPad(String.valueOf(++i), 3, '0'));
                ficheRptData.setNo(remark);
                ficheRptDataList.add(ficheRptData);
            }
        }
        String fileName = null;
        if (!ficheRptDataList.isEmpty()) {
            List<JasperParam> jaspers = new ArrayList();
            for (LargeFicheRptData ficheRptData : ficheRptDataList) {
                jaspers.add(new JasperParam(new HashMap<>(), JSON.toJSONString(ficheRptData).getBytes()));
            }
            fileName = fileUrl + File.separator + "large_fiche_rpt_" + System.currentTimeMillis() + ".pdf";
            File parentFile = new File(fileName).getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            try {
                PdfReport.exprtPdf(fileName, jaspers, "/ireport/jrxml/large_fiche_rpt.jrxml");
            } catch (Exception e) {
                logger.error("work【非AV】导出失败，msg: ", e);
            }
        } else if (!largeCueDataList.isEmpty()) {
            List<JasperParam> jaspers = new ArrayList();
            for (LargeCueData largeCueData : largeCueDataList) {
                jaspers.add(new JasperParam(JSON.parseObject(JSON.toJSONString(largeCueData), Map.class), JSON.toJSONString(largeCueData.getFieldList()).getBytes()));
            }
            fileName = fileUrl + File.separator + "large_cue_sheets_" + System.currentTimeMillis() + ".pdf";
            File parentFile = new File(fileName).getParentFile();
            if (!parentFile.exists()) {
                parentFile.mkdirs();
            }
            try {
                PdfReport.exprtPdf(fileName, jaspers, "/ireport/jrxml/large_cue_sheets1.jrxml");
            } catch (Exception e) {
                logger.error("work【AV】导出失败，msg: ", e);
            }
        }
        if (StringUtils.isBlank(fileName)) {
            return;
        }
        File f = new File(fileName);
        try {
            // f.deleteOnExit();
            String outPutFileName = URLEncoder.encode(fileName.substring(fileName.lastIndexOf(File.separator) + 1), "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/pdf");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(new String(outPutFileName.getBytes("utf-8")), "UTF-8"));
            response.setCharacterEncoding("utf-8");
            FileInputStream in = new FileInputStream(f);
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[1024];
            while ((in.read(b)) != -1) {
                out.write(b);
            }
            out.flush();
            in.close();
            out.close();
        } catch (Exception e) {
            logger.error("work導出失败，msg: ", e);
        } finally {
            if (null != f && f.exists()) {
                f.deleteOnExit();
            }
        }
    }

    public LargeCueData generateLargeCueData(Long workId, Integer workSocietyCode, WrkWork work, String year,Boolean atFlag,String rightType,String language) {
        LargeCueData largeCueData = new LargeCueData();
        largeCueData.setLogo("ireport/logo.png");
        largeCueData.setDate(DateParse.format(new Date(), "yyyyMMdd HHmm"));
        largeCueData.setWorkNo(String.format("%d (%d)", workId, workSocietyCode));
        largeCueData.setIsan(null == work.getIsan() ? "" : String.valueOf(work.getIsan()));
        RefSociety society = refSocietyService.getSocietyBySocietyCode(workSocietyCode);
        if (null != society) {
            largeCueData.setSocCountry(society.getCountryCode());
        }
        List<WrkWorkTitle> workTitleList = workTitleService.getWrkWorkTitleByWrkId(workId, workSocietyCode);
        WrkWorkTitle workTitle = workTitleList.stream().filter(x -> StringUtils.equals("OT", x.getTitleType())).findFirst().orElse(null);
        if (null == workTitle) {
            throw new MustException(String.format("work導出失敗，該作品沒有主標題 workId:%d, workSocietyCode:%d", workId, workSocietyCode));
        }
        if (null != workTitle.getDurationM() && null != workTitle.getDurationS()) {
            String durationM = null != workTitle.getDurationM() ? StringUtils.leftPad(String.valueOf(workTitle.getDurationM()), 2, '0') : "00";
            String durationS = null != workTitle.getDurationS() ? StringUtils.leftPad(String.valueOf(workTitle.getDurationS()), 2, '0') : "00";
            largeCueData.setMusicalDuration(String.format("%s'%s\"", durationM, durationS));
        }
        largeCueData.setTitle(getNameOrTitleByLanguage(language,workTitle.getTitle(),workTitle.getTitleEn()));
        if(atFlag){
            List<String> atList = new ArrayList<>(workTitleList.size());
            ;
            workTitleList.stream().filter(x -> !StringUtils.equals("AT", x.getTitleType())).forEach(t -> {
                atList.add(getNameOrTitleByLanguage(language,t.getTitle(),t.getTitleEn()));
            });
            largeCueData.setAt(String.join("\\", atList));

        } else {
            largeCueData.setHiddenAt(true);
        }

        List<WrkWorkArtistMerge> wwamList = wrkWorkArtistMergeService.getWrkWorkArtistMergeByWrkId(workId,
                workSocietyCode);
        if (null != wwamList && !wwamList.isEmpty()) {
            List<WrkArtist> artistList = wrkArtistService.getWrkArtistByUniqueKeyMd5List(wwamList.stream().map(WrkWorkArtistMerge::getUniqueKeyMd5).collect(Collectors.toList()));
            List<String> arts = new ArrayList<>(wwamList.size());
            for (WrkArtist wa : artistList) {
                /*if (StringUtils.isBlank(wa.getName())) {
                    wa.setName(wa.getRomanName());
                }
                arts.add(StringUtils.isBlank(wa.getChineseName()) ? wa.getName() : String.format("%s（%s）", wa.getChineseName(), wa.getName()));*/
                arts.add(getNameOrTitleByLanguage(language,wa.getChineseName(),wa.getName()));

            }
            largeCueData.setPrincipalActors(String.join(" / ", arts));
        }

        largeCueData.setProducer(work.getProductCompany());
        largeCueData.setProductionYear(null == work.getProductionYear() ? "" : String.valueOf(work.getProductionYear()));

        largeCueData.setEpisodeNumber(workTitle.getEpisodeNo());
        largeCueData.setCategory(refGenreDtlService.getDescriptionByGenerCode(workTitle.getGenreCode()));
        largeCueData.setDirector(work.getDirector());

        largeCueData.setCreationYear("");
        largeCueData.setDistributer(""); //TODO
        largeCueData.setDestination("");
        largeCueData.setFirstPresentation("");
        largeCueData.setTotalDuration("");
        largeCueData.setLength("");
        largeCueData.setPerformingRights("");
        largeCueData.setPMandate("");
        largeCueData.setMechanicalRights("");
        largeCueData.setMMandate("");

        List<WrkWorkComponent> workComponentList = wrkWorkComponentService.getWrkWorkComponentByWrkId(workId, workSocietyCode);
        List<LargeCueField> largeCueFieldList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workComponentList)) {
            List<String> comWorkUniqueKeys = workComponentList.stream().filter(x -> null != x.getComponentWorkId() || null != x.getComWorkSociety())
                    .map(y -> CommonUtil.getWorkUniqueKeyByWrkSocAndNum(y.getComWorkSociety(), y.getComponentWorkId())).collect(Collectors.toList());
            Map<String, WrkWork> comWorkMap = workService.getWrkWorkByWorkUniqueKeys(comWorkUniqueKeys).stream().collect(Collectors.toMap(WrkWork::getWorkUniqueKey, Function.identity(), (a, b) -> a));
            Map<String, WrkWorkTitle> comWorkTitleMap = workTitleService.getTitleMapByWorkUniqueKeyListMapDefaultOT(comWorkUniqueKeys);
            Map<String, List<WrkWorkTitle>> comOtherWorkTitleMap = workTitleService.getOtherTitleMapByWorkUniqueKeyList(comWorkUniqueKeys);
            for (WrkWorkComponent wrkWorkComponent : workComponentList) {
                Long componentWorkId = wrkWorkComponent.getComponentWorkId();
                Integer comWorkSociety = wrkWorkComponent.getComWorkSociety();
                if (null == componentWorkId || null == comWorkSociety) {
                    continue;
                }
                LargeCueField largeCueField = new LargeCueField();
                String comWorkUniqueKey = CommonUtil.getWorkUniqueKeyByWrkSocAndNum(comWorkSociety, componentWorkId);
                WrkWork comWork = comWorkMap.get(comWorkUniqueKey);
                WrkWorkTitle comWorkTitle = comWorkTitleMap.get(comWorkUniqueKey);
                List<WrkWorkTitle> otherWorkTitleList = comOtherWorkTitleMap.get(comWorkUniqueKey);
                String comDurationM = null != wrkWorkComponent.getDurationM() ? StringUtils.leftPad(String.valueOf(wrkWorkComponent.getDurationM()), 2, '0') : "00";
                String comDurationS = null != wrkWorkComponent.getDurationS() ? StringUtils.leftPad(String.valueOf(wrkWorkComponent.getDurationS()), 2, '0') : "00";
                largeCueField.setDuration(String.format("%s'%s\"", comDurationM, comDurationS));
                if (null != comWork && null != comWorkTitle) {
                    largeCueField.setTitle(StringUtils.isNotBlank(comWorkTitle.getTitle()) ? String.format("%s (%s)", comWorkTitle.getTitle(), comWorkTitle.getTitleEn()) : comWorkTitle.getTitleEn());
                    if (null != otherWorkTitleList) {
                        List<String> otherTitles = new ArrayList<>(otherWorkTitleList.size());
                        for (WrkWorkTitle compOtherWorkTitle : otherWorkTitleList) {
                            otherTitles.add(String.format("%s:%s", compOtherWorkTitle.getTitleType(), StringUtils.isBlank(compOtherWorkTitle.getTitle()) ? compOtherWorkTitle.getTitleEn() : compOtherWorkTitle.getTitle()));
                        }
                        largeCueField.setOtherTitle(String.join("/ ", otherTitles));
                    }
                    largeCueField.setUsage(wrkWorkComponent.getUsageType());
                    List<String> rightTypes = new ArrayList<>() ;
                    if(StringUtils.isEmpty(rightType)){
                        rightTypes = Arrays.asList("PER", "MEC");
                    } else {
                        rightTypes.add(rightType) ;
                    }
                    List<WrkWorkIpShare> wrkWorkIpShares = calcIpShareService.calcWorkIpShare(comWork, year, rightTypes);
                    Map<String, List<WrkWorkIpShare>> ipShareMap = wrkWorkIpShares.stream().collect(Collectors.groupingBy(WrkWorkIpShare::getRightType));
                    List<WrkWorkIpShare> wrkWorkIpSharePER = ipShareMap.getOrDefault("PER", new ArrayList<>());
                    Set<String> dealtGroupKeySet = new HashSet<>() ;
                    Map<String, WrkWorkIpShare> MECMap = ipShareMap.getOrDefault("MEC", new ArrayList<>()).stream().collect(Collectors.toMap(i -> i.getGroupIndicator() + i.getIpNameNo() + i.getWorkIpRole(), Function.identity(), (a, b) -> a));
                    Set<String> mecIdSet = new HashSet<>(); // ipshare里的id有为空的情况
                    List<DetailDTO> detailDTOList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(wrkWorkIpSharePER)) {
                        for (WrkWorkIpShare workIpShare : wrkWorkIpSharePER) {
                            if (null == workIpShare.getIpShare()) {
                                workIpShare.setIpShare(new BigDecimal("0"));
                            }
                            DetailDTO detailDTO = new DetailDTO();
                            detailDTO.setType(workIpShare.getWorkIpRole());
                            detailDTO.setSurnames(getNameOrTitleByLanguage(language,workIpShare.getChineseName(),workIpShare.getName()));
                            detailDTO.setIPNameNo(workIpShare.getIpNameNo());
                            detailDTO.setPerformingSociety(workIpShare.getIpSocietyCode());
                            detailDTO.setPartShare1(String.format("%s%%", workIpShare.getIpShare().setScale(2, RoundingMode.HALF_UP).toString()));
                            String groupKey = workIpShare.getGroupIndicator() + workIpShare.getIpNameNo() + workIpShare.getWorkIpRole();
                            if (MECMap.containsKey(groupKey)) {
                                dealtGroupKeySet.add(groupKey);
                                WrkWorkIpShare mecIpShare = MECMap.get(groupKey);

                                if (null == mecIpShare.getIpShare()) {
                                    mecIpShare.setIpShare(new BigDecimal("0"));
                                }
                                if (null != mecIpShare.getIpShare() && mecIdSet.add(String.format("%s%s%s", mecIpShare.getGroupIndicator(), mecIpShare.getWorkIpRole(), mecIpShare.getIpShare().toString()))) {
                                    // 取出来只能用一次
                                    detailDTO.setMechanical(mecIpShare.getIpSocietyCode());
                                    detailDTO.setPartShare2(String.format("%s%%", mecIpShare.getIpShare().setScale(2, RoundingMode.HALF_UP).toString()));
                                }

                            }
                            detailDTOList.add(detailDTO);
                        }
                    }

                    if(dealtGroupKeySet.size() < MECMap.keySet().size()){
                        for(Map.Entry<String,WrkWorkIpShare> entry : MECMap.entrySet()){
                            if(dealtGroupKeySet.contains(entry.getKey())){
                                continue;
                            }
                            WrkWorkIpShare mecIpShare = entry.getValue() ;
                            DetailDTO detailDTO = new DetailDTO();
                            if (null == mecIpShare.getIpShare()) {
                                mecIpShare.setIpShare(new BigDecimal("0"));
                            }
                            if (null != mecIpShare.getIpShare() && mecIdSet.add(String.format("%s%s%s", mecIpShare.getGroupIndicator(), mecIpShare.getWorkIpRole(), mecIpShare.getIpShare().toString()))) {
                                // 取出来只能用一次
                                detailDTO.setMechanical(mecIpShare.getIpSocietyCode());
                                detailDTO.setPartShare2(String.format("%s%%", mecIpShare.getIpShare().setScale(2, RoundingMode.HALF_UP).toString()));
                            }
                            detailDTO.setType(mecIpShare.getWorkIpRole());
                            detailDTO.setSurnames(getNameOrTitleByLanguage(language,mecIpShare.getChineseName(),mecIpShare.getName()));
                            detailDTO.setIPNameNo(mecIpShare.getIpNameNo());
                            detailDTO.setPartShare1("0.000000%");

                            detailDTOList.add(detailDTO);
                        }
                    }

                    largeCueField.setDetail(detailDTOList);

                    largeCueFieldList.add(largeCueField);
                }
            }
        }
        if (largeCueFieldList.isEmpty()) {
            largeCueFieldList.add(new LargeCueField());
        }
        largeCueData.setFieldList(largeCueFieldList);
        return largeCueData;
    }


    public LargeFicheRptData generateLargeFicheRptData(Long workId, Integer workSocietyCode, WrkWork work, String year, String countryCode,Boolean atFlag,String rightType,String language ) {
        LargeFicheRptData ficheRptData = new LargeFicheRptData();
        ficheRptData.setNo("No.001");
        ficheRptData.setWorkNo(workId);
        ficheRptData.setIswc(work.getISWC());
        ficheRptData.setCreateTime(DateParse.format(new Date(), "yyyy/MM/dd"));
        List<WrkWorkTitle> workTitleList = workTitleService.getWrkWorkTitleByWrkId(workId, workSocietyCode);
        WrkWorkTitle workTitle = workTitleList.stream().filter(x -> StringUtils.equals("OT", x.getTitleType())).findFirst().orElse(null);
        if (null == workTitle) {
            throw new MustException(String.format("work导出失败，该作品没有主标题 workId:%d, workSocietyCode:%d", workId, workSocietyCode));
        }

        ficheRptData.setTitle(getNameOrTitleByLanguage(language,workTitle.getTitle(),workTitle.getTitleEn()));
        ficheRptData.setGi(workTitle.getGenreCode());
        ficheRptData.setDuration(String.format("%d'%s\"", null == workTitle.getDurationM() ? 0 : workTitle.getDurationM(),
                null == workTitle.getDurationS() ? "00" : StringUtils.leftPad(String.valueOf(workTitle.getDurationS()), 2, "0")));
        if (StringUtils.containsAny(work.getWorkType(), "ADP", "ARR")) {
            WrkWorkTitle otWorkTitle = workTitleService.getOTWrkWorkTitleByWrkId(work.getRefWorkSociety(), work.getRefWorkId());
            if (null != otWorkTitle) {
                ficheRptData.setOt(getNameOrTitleByLanguage(language,otWorkTitle.getTitle(),otWorkTitle.getTitleEn()));
            }
        }
        List<OtherTitleData> otherTitleList = new ArrayList<>(workTitleList.size());
        workTitleList.stream().filter(x -> !StringUtils.equals("OT", x.getTitleType())).forEach(t -> {
            OtherTitleData otherTitleData = new OtherTitleData();
            otherTitleData.setOtherTitle(getNameOrTitleByLanguage(language,t.getTitle(),t.getTitleEn()));
            otherTitleList.add(otherTitleData);
        });
        if(atFlag){
            List<String> atList = new ArrayList<>(workTitleList.size());
            ;
            workTitleList.stream().filter(x -> !StringUtils.equals("AT", x.getTitleType())).forEach(t -> {
                atList.add(getNameOrTitleByLanguage(language,t.getTitle(),t.getTitleEn()));
            });
            ficheRptData.setAt(String.join("\\", atList));

        } else {
            ficheRptData.setHiddenAt(true);
        }
        ficheRptData.setOtherTitleList(otherTitleList);
        ficheRptData.setWorkNo(work.getWorkId());

        ficheRptData.setSocCountry(StringUtils.isBlank(countryCode) ? "TW" : countryCode);

        List<WrkWorkArtistMerge> wwamList = wrkWorkArtistMergeService.getWrkWorkArtistMergeByWrkId(workId,
                workSocietyCode);
        if (null != wwamList && !wwamList.isEmpty()) {
            List<WrkArtist> artistList = wrkArtistService.getWrkArtistByUniqueKeyMd5List(wwamList.stream().map(WrkWorkArtistMerge::getUniqueKeyMd5).collect(Collectors.toList()));
            List<String> arts = new ArrayList<>(wwamList.size());
            for (WrkArtist wa : artistList) {
                wa.setName(String.format("%s %s", null == wa.getLastName() ? "" : wa.getLastName(), null == wa.getFirstName() ? "" : wa.getFirstName()).trim());
                if (StringUtils.isBlank(wa.getName())) {
                    wa.setName(wa.getRomanName());
                }
                arts.add(getNameOrTitleByLanguage(language,wa.getChineseName(),wa.getName()));
            }
            ficheRptData.setArt(String.join(" / ", arts));
        }
        if (StringUtils.isBlank(year)) {
            year = new SimpleDateFormat("yyyy").format(new Date());
        }

        List<String> rightTypes = new ArrayList<>() ;
        if(StringUtils.isEmpty(rightType)){
            rightTypes = Arrays.asList("PER", "MEC");
        } else {
            rightTypes.add(rightType) ;
        }

        List<WrkWorkIpShare> wrkWorkIpShareByYear = calcIpShareService.calcWorkIpShare(work, year, rightTypes);
        if (CollectionUtils.isNotEmpty(wrkWorkIpShareByYear)) {
            wrkWorkIpShareByYear.stream().forEach(wrkWorkIpShare -> {
                if (StringUtils.isBlank(wrkWorkIpShare.getName())) {
                    wrkWorkIpShare.setName(wrkWorkIpShare.getDummyNameRoman());
                }
            });
            Map<String, List<WrkWorkIpShare>> ipShareMap = wrkWorkIpShareByYear.stream().collect(Collectors.groupingBy(WrkWorkIpShare::getRightType));

            List<WrkWorkIpShare> wrkWorkIpShares = ipShareMap.get("PER");
            Map<String, String> contractMap = new TreeMap<>();
            Set<String> mecIdSet = new HashSet<>(); // ipshare里的id有为空的情况
            Set<String> dealtGroupKeySet = new HashSet<>() ;
            List<IpShareData> ipShareDataList = new ArrayList<>();
            Map<String, WrkWorkIpShare> MECMap = ipShareMap.getOrDefault("MEC", new ArrayList<>()).stream().collect(Collectors.toMap(i -> i.getGroupIndicator() + i.getIpNameNo() + i.getWorkIpRole(), Function.identity(), (a, b) -> a));
            if (CollectionUtils.isNotEmpty(wrkWorkIpShares)) {
                wrkWorkIpShares.forEach(x -> {
                    if (StringUtils.isNotBlank(x.getAgrNo())) {
                        contractMap.put(x.getGroupIndicator() + "_" + x.getAgrNo(),x.getGroupIndicator());
                    }
                    IpShareData ipShareData = new IpShareData();
                    ipShareData.setIpName(getNameOrTitleByLanguage(language,x.getChineseName(),x.getName()));
                    ipShareData.setIpNameNo(x.getIpNameNo());
                    ipShareData.setIpRole(String.format("%s%s", x.getWorkIpRole(), x.getGroupIndicator()));
                    ipShareData.setSoc1(x.getIpSocietyCode());
                    ipShareData.setPart1(x.getIpShare());
                    String groupKey = x.getGroupIndicator() + x.getIpNameNo() + x.getWorkIpRole();
                    if (MECMap.containsKey(groupKey)) {
                        dealtGroupKeySet.add(groupKey);
                        WrkWorkIpShare mecIpShare = MECMap.get(groupKey);
                        if (mecIdSet.add(String.format("%s%s%s", mecIpShare.getGroupIndicator(), mecIpShare.getWorkIpRole(), mecIpShare.getIpShare().toString()))) {
                            // 取出来只能用一次
                            ipShareData.setSoc2(mecIpShare.getIpSocietyCode());
                            ipShareData.setPart2(mecIpShare.getIpShare());
                        }
                    }
                    ipShareDataList.add(ipShareData);
                });
            }

            if(dealtGroupKeySet.size() < MECMap.keySet().size()){
                for(Map.Entry<String,WrkWorkIpShare> entry : MECMap.entrySet()){
                    if(dealtGroupKeySet.contains(entry.getKey())){
                        continue;
                    }
                    WrkWorkIpShare mecIpShare = entry.getValue() ;
                    IpShareData ipShareData = new IpShareData();
                    if (null == mecIpShare.getIpShare()) {
                        mecIpShare.setIpShare(new BigDecimal("0"));
                    }
                    if (null != mecIpShare.getIpShare() && mecIdSet.add(String.format("%s%s%s", mecIpShare.getGroupIndicator(), mecIpShare.getWorkIpRole(), mecIpShare.getIpShare().toString()))) {
                        // 取出来只能用一次
                        ipShareData.setSoc2(mecIpShare.getIpSocietyCode());
                        ipShareData.setPart2(mecIpShare.getIpShare());
                    }
                    ipShareData.setIpRole(String.format("%s%s", mecIpShare.getWorkIpRole(), mecIpShare.getGroupIndicator()));
                    ipShareData.setIpName(getNameOrTitleByLanguage(language,mecIpShare.getChineseName(),mecIpShare.getName()));
                    ipShareData.setIpNameNo(mecIpShare.getIpNameNo());
                    ipShareData.setPart1(BigDecimal.ZERO);

                    ipShareDataList.add(ipShareData);
                }
            }

            ficheRptData.setIpShareList(ipShareDataList);

            if (!contractMap.isEmpty()) {
                List<ContractData> contractList = new ArrayList<>(contractMap.size());
                List<String> contractNoList = contractMap.keySet().stream().map(a -> a.substring(2)).collect(Collectors.toList());
                Map<String, AgrContent> agrContentMap = agrContentService.getAgreementByAgrNoList(contractNoList).stream().collect(Collectors.toMap(AgrContent::getAgrNo, Function.identity(), (a, b) -> a));
                contractMap.forEach((gp_agrNo, gp) -> {
                    String agrNo = gp_agrNo.substring(2);
                    AgrContent agrContent = agrContentMap.get(agrNo) ;
                    if(agrContent.getAutoExtensionInd().equals("Y")){
                        agrContent.setAgrEdate(DateParse.parseDate("9999/12/31", "yyyy/MM/dd"));
                    }
                    if (null != agrContent.getAgrEdate() && agrContent.getAgrEdate().before(new Date())) {
                        return;
                    }
                    ContractData contractData = new ContractData();
                    String sDate = null != agrContent.getAgrSdate() ? DateParse.format(agrContent.getAgrSdate(), "yyyy/MM/dd") : "0000/00/00";
                    String eDate = null != agrContent.getAgrEdate() ? DateParse.format(agrContent.getAgrEdate(), "yyyy/MM/dd") : "9999/12/31";
                    List<AgrAgreementTerritory> agreementTerritories = agrAgreementTerritoryService.getTerritoryByAgrNo(agrNo);
                    List<RefTerritoryRelation> refTerritoryRelationList = refTerritoryRelationService.selectTerrByRelTisA(countryCode);
                    Set<String> tisASet = refTerritoryRelationList.stream().map(RefTerritoryRelation::getTisA).collect(Collectors.toSet());
                    StringBuilder sb = new StringBuilder();
                    agreementTerritories.forEach(x -> {
                        if(StringUtils.isEmpty(countryCode)){
                            sb.append(x.getIndicator()).append(x.getTisA());
                        } else if(StringUtils.equals(x.getTisA(),countryCode)){
                            sb.append(x.getIndicator()).append(x.getTisA());
                        } else if(tisASet.contains(x.getTisA())){
                            sb.append(x.getIndicator()).append(x.getTisA());
                        }
                    });
                    contractData.setContract(String.format("%s    %s-%s  %s", contractMap.get(gp_agrNo), sDate, eDate, sb.toString()));
                    contractList.add(contractData);
                });
                ficheRptData.setContractList(contractList);
            }
        }
        return ficheRptData;
    }


    @ApiOperation(value = "支付会员导出")
    @PostMapping("/autoPayMemberInfo")
    public void autoPayMemberInfo(HttpServletResponse response, @RequestBody AutoPayMemberInfoVo autoPayMemberInfoVo) {
        AutoPayMemberPdf payMemberPdf = exportPdfService.getAutoPayMemberPdf(autoPayMemberInfoVo.getPaymentNo(), autoPayMemberInfoVo.getMemberNoA(), autoPayMemberInfoVo.getMemberNoB(),
                autoPayMemberInfoVo.getMemberNo(), autoPayMemberInfoVo.getMemberShip(), autoPayMemberInfoVo.getNameOrder(), autoPayMemberInfoVo.getMemberNoOrder(), autoPayMemberInfoVo.getInputOrder());
        List<JasperParam> jaspers = new ArrayList();
        jaspers.add(new JasperParam(new HashMap<>(), JSON.toJSONString(payMemberPdf).getBytes()));
        String fileName = uploadTempPath + File.separator + "memberInfo_" + System.currentTimeMillis() + ".pdf";
        File parentFile = new File(fileName).getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        try {
            PdfReport.exprtPdf(fileName, jaspers, "/ireport/jrxml/autoPay/memberInfo_main.jrxml");
        } catch (Exception e) {
            logger.error(String.format("支付会员导出【%s】导出失败，msg: ", JSON.toJSONString(autoPayMemberInfoVo)), e);
        }
        File f = null;
        try {
            f = new File(fileName);
            String outPutFileName = URLEncoder.encode(fileName.substring(fileName.lastIndexOf(File.separator) + 1), "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/pdf");
            if(Integer.valueOf(1).equals(autoPayMemberInfoVo.getType())) {
                response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(outPutFileName.getBytes("utf-8")), "UTF-8"));
            }
            response.setCharacterEncoding("utf-8");
            FileInputStream in = new FileInputStream(f);
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[1024];
            while ((in.read(b)) != -1) {
                out.write(b);
            }
            out.flush();
            in.close();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != f && f.exists()) {
                f.deleteOnExit();
            }
        }
    }

    public String getNameOrTitleByLanguage(String language, String chineseStr, String englishStr){
        if(StringUtils.equals(language,"ZHO")){
            return StringUtils.isNotBlank(chineseStr) ? chineseStr : englishStr ;
        } else if(StringUtils.equals(language,"ENG")){
            return englishStr;
        } else {
            if(englishStr == null){
                englishStr = "";
            }
            return StringUtils.isNotBlank(chineseStr) ? String.format("%s (%s)", chineseStr, englishStr) : englishStr;
        }
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
        String fileName = "D:\\Test\\PDF\\test.pdf";
        String outPutFileName = URLEncoder.encode(fileName.substring(fileName.lastIndexOf(File.separator) + 1), "UTF-8").replaceAll("\\+", "%20");
        System.out.println(outPutFileName);
    }
}
