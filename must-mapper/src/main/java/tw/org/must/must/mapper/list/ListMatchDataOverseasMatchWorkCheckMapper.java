package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataOverseasMatchWorkCheck;

import java.util.List;

@Repository
public interface ListMatchDataOverseasMatchWorkCheckMapper extends BaseMapper<ListMatchDataOverseasMatchWorkCheck> {
    Boolean checkIPWhetherReviewedOfAll(@Param("workUniqueKey") String workUniqueKey);

    List<ListMatchDataOverseasMatchWorkCheck> listListMatchDataOverseasMatchWorkCheckWithPage(@Param("workNo") Long workNo,@Param("ipSocietyCode") Integer ipSocietyCode,
                                                                                              @Param("status") Integer status,@Param("sourceWorkCode") String sourceWorkCode,
                                                                                              @Param("remitSociety") Integer remitSociety,@Param("fIds") List<Long> fIds);

    List<ListMatchDataOverseasMatchWorkCheck> listListMatchDataOverseasMatchWorkCheck(@Param("matchWorkId") Long matchWorkId,@Param("matchWorkSoc") Integer matchWorkSoc,
                                                                                      @Param("matchWorkTitle") String matchWorkTitle,@Param("distributedIp") String distributedIp,
                                                                                      @Param("distributedIpName") String distributedIpName, @Param("matchIpSoc") Integer matchIpSoc,
                                                                                      @Param("remitSoc") Integer remitSoc,@Param("remitIpName") String remitIpName,
                                                                                      @Param("remitWorkTitle") String remitWorkTitle,@Param("dataUniqueKey") String dataUniqueKey,
                                                                                      @Param("batch") Long batch,@Param("fIds") List<Long> fIds,@Param("recordType") Integer recordType);

}