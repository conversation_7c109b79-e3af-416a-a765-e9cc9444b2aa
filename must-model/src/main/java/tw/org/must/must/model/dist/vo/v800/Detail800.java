package tw.org.must.must.model.dist.vo.v800;

import lombok.Data;
import tw.org.must.must.common.util.LocalCommonMethodUtils;

import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2021/5/28 14:29
 * @Version:1.0
 * @Description:
 */
@Data
public class Detail800 {
    private String payMethod;
    private String societyName;
    private String royalties;
    private String payAbleAmount;
    private String netConvert;
    private String bankAccounName;
    private String bankAccountNo;
    private String bankName;
    private String countryCode;
    private String dranOnCountryCode;
    private String bankAddress;
    private String swiftCode;
    private String ibanCode;
    private List<DistNoDetail800> distNoLists;

    public String getRoyalties() {
        return LocalCommonMethodUtils.formateNoScale(royalties);
    }

    public String getPayAbleAmount() {
        return LocalCommonMethodUtils.formateNoScale(payAbleAmount);
    }
}