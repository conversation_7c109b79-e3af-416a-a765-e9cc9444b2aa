package tw.org.must.must.mapper.list;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataBasic;

@Repository
public interface ListMatchDataBasicMapper extends BaseMapper<ListMatchDataBasic> {
  List<Map<String, Object>> getCountGroupByFileBaseId();

  List<Map<String, Object>> getBasicCountGroupByStatus(Long baseId);

//  @Select("SELECT basic.* FROM list_match_data_basic basic inner join list_basic_file_base base " +
//          "on basic.file_base_id = base.id WHERE unique_key_md5 = #{uniqueKeyMd5} AND base.is_dist = 'N'")

  @Select("SELECT basic.* FROM list_match_data_basic basic WHERE unique_key_md5 = #{uniqueKeyMd5} and match_work_id = #{workId} and match_work_society_code = #{workSocietyCode}")
  List<ListMatchDataBasic> getListMatchDataBasicDist(@Param(value = "uniqueKeyMd5") String uniqueKeyMd5,@Param(value = "workSocietyCode") Integer workSocietyCode, @Param(value = "workId")Long workId);

  Integer updateListMatchDataBasicFromHistory(@Param("listMatchDataBasic") ListMatchDataBasic listMatchDataBasic, @Param("list") List<Long> list) ;

  void insertDuplicate(List<ListMatchDataBasic>list);
}