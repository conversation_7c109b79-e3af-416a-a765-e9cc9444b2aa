package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_match_work`")
public class DistMatchWork extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;
    
    @Column(name = "`list_type`")
    private String listType;

    /**
     * 
     */
    @Column(name = "`list_parent_md5`")
    private String listParentMd5;

    /**
     * 
     */
    @Column(name = "`list_file_data_id`")
    private Long listFileDataId;
    
    @Column(name = "`list_file_id`")
    private Long listFileId;

    /**
     * 
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * 清单标题
     */
    @Column(name = "`list_title`")
    private String listTitle;

    /**
     * 匹配标题
     */
    @Column(name = "`match_title`")
    private String matchTitle;

    /**
     * 
     */
    @Column(name = "`duration_m`")
    private Integer durationM;

    /**
     * 
     */
    @Column(name = "`duration_s`")
    private Integer durationS;

    /**
     * 
     */
    @Column(name = "`click_number`")
    private Long clickNumber;

    /**
     * 
     */
    @Column(name = "`episode_no`")
    private Integer episodeNo;

    /**
     * 
     */
    @Column(name = "`tv_name`")
    private String tvName;

    /**
     * 
     */
    @Column(name = "`channel_name`")
    private String channelName;

    /**
     * 歌曲占比
     */
    @Column(name = "`music_share`")
    private String musicShare;
    
    @Column(name = "`music_usage`")
    private String musicUsage;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getListParentMd5() {
        return listParentMd5;
    }

    public void setListParentMd5(String listParentMd5) {
        this.listParentMd5 = listParentMd5;
    }

    public Long getListFileDataId() {
        return listFileDataId;
    }

    public void setListFileDataId(Long listFileDataId) {
        this.listFileDataId = listFileDataId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getListTitle() {
        return listTitle;
    }

    public void setListTitle(String listTitle) {
        this.listTitle = listTitle;
    }

    public String getMatchTitle() {
        return matchTitle;
    }

    public void setMatchTitle(String matchTitle) {
        this.matchTitle = matchTitle;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public Long getClickNumber() {
        return clickNumber;
    }

    public void setClickNumber(Long clickNumber) {
        this.clickNumber = clickNumber;
    }

    public Integer getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(Integer episodeNo) {
        this.episodeNo = episodeNo;
    }

    public String getTvName() {
        return tvName;
    }

    public void setTvName(String tvName) {
        this.tvName = tvName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getMusicShare() {
        return musicShare;
    }

    public void setMusicShare(String musicShare) {
        this.musicShare = musicShare;
    }

	public String getListType() {
		return listType;
	}

	public void setListType(String listType) {
		this.listType = listType;
	}

	public String getMusicUsage() {
		return musicUsage;
	}

	public void setMusicUsage(String musicUsage) {
		this.musicUsage = musicUsage;
	}

	public Long getListFileId() {
		return listFileId;
	}

	public void setListFileId(Long listFileId) {
		this.listFileId = listFileId;
	}



}