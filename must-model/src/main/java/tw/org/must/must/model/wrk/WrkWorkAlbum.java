package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`wrk_work_album`")
public class WrkWorkAlbum extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 作品唯一標識，work_id+'_'+work_society_code
     */
    @Column(name = "`work_unique_key`")
    private String workUniqueKey;

    /**
     * 專輯編號
     */
    @Column(name = "`album_no`")
    private String albumNo;

    /**
     * 專輯名稱
     */
    @Column(name = "`album_name`")
    private String albumName;

    /**
     * 專輯發佈日期
     */
    @Column(name = "`album_pub_date`")
    private String albumPubDate;

    /**
     * Y or N
     */
    @Column(name = "`first_publish`")
    private String firstPublish;

    /**
     * 创建用户
     */
    @Column(name = "`create_by`")
    private String createBy;

    /**
     * 修改用户
     */
    @Column(name = "`amend_by`")
    private String amendBy;

    /***
    * @Description:  ADD, DEL, UPDATE
    *
    */
    @Transient
    private String action;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getWorkUniqueKey() {
        return workUniqueKey;
    }

    public void setWorkUniqueKey(String workUniqueKey) {
        this.workUniqueKey = workUniqueKey;
    }

    public String getAlbumNo() {
        return albumNo;
    }

    public void setAlbumNo(String albumNo) {
        this.albumNo = albumNo;
    }

    public String getAlbumName() {
        return albumName;
    }

    public void setAlbumName(String albumName) {
        this.albumName = albumName;
    }

    public String getAlbumPubDate() {
        return albumPubDate;
    }

    public void setAlbumPubDate(String albumPubDate) {
        this.albumPubDate = albumPubDate;
    }

    public String getFirstPublish() {
        return firstPublish;
    }

    public void setFirstPublish(String firstPublish) {
        this.firstPublish = firstPublish;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getAmendBy() {
        return amendBy;
    }

    public void setAmendBy(String amendBy) {
        this.amendBy = amendBy;
    }



}