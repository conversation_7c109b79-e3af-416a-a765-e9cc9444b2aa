package tw.org.must.must.core.parse.spotify.tsv.entity;

import java.util.List;

public class TsvCommonObject {

	private List<AS0202> as0202List;

	private RE01 re01;
	
	private List<SU02> su02List;

	private Integer resourceReference;

	public RE01 getRe01() {
		return re01;
	}

	public void setRe01(RE01 re01) {
		this.re01 = re01;
	}

	public List<SU02> getSu02List() {
		return su02List;
	}

	public void setSu02List(List<SU02> su02List) {
		this.su02List = su02List;
	}

	public List<AS0202> getAs0202List() {
		return as0202List;
	}

	public void setAs0202List(List<AS0202> as0202List) {
		this.as0202List = as0202List;
	}

	public Integer getResourceReference() {
		return resourceReference;
	}

	public void setResourceReference(Integer resourceReference) {
		this.resourceReference = resourceReference;
	}
}
