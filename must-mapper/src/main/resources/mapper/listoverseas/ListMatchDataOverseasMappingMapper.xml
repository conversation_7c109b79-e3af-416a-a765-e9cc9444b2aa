<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.listoverseas.ListMatchDataOverseasMappingMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping">

    </resultMap>
    <update id="updateByFileMappingId" parameterType="tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping">
        update list_match_data_overseas_mapping set
            ip_name_no = #{ipNameNo},
            ip_name = #{ipName},
            work_ip_role = #{workIpRole},
            share_ratio = #{shareRatio},
            amount = #{amount},
            status = #{status}
        where file_mapping_id = #{fileMappingId}
    </update>
    <select id="getIds" parameterType="java.lang.String" resultType="java.lang.Long">
        select id from list_match_data_overseas_mapping where data_unique_key = #{dataUniqueKey}
    </select>
    <select id="selectCountByIpNameNoAndUniqueK" resultType="java.lang.Integer">
        select count(1) from list_match_data_overseas_mapping
        where data_unique_key = #{dataUniqueKey} and  ip_name_no = #{ipNameNo}
    </select>

<!--    <select id="getCountByFidAndDataUniqueKey" resultType="java.lang.Integer">
        select count(1) from list_match_data_overseas_mapping
        where file_base_id = #{fileBaseId} and data_unique_key  = #{dataUniqueKey}
    </select>-->

    <select id="getListMatchDataOverseasMappingListByFidsAndRejectCode" resultType="tw.org.must.must.model.report.OverseasCheckingIpNotInWork">
        SELECT tc.dist_no as distNo,
               ta.ip_name_no as ipNameNo,
               ta.match_pa_name_no as matchPaNameNo,
               ta.match_ip_name as matchIpName,
               ta.work_ip_role as workIpRole,
               ta.match_ip_soc as societyCode,
               ta.share_ratio as ipShare,
               '' as fieCode,
               '' as distIpShare,
               '' as amountSeqNo,
               '' as distinctWorknum,
               ta.match_work_id as matchWorkId,
               ta.match_work_society_code as matchWorknumSocietyCode,
               tb.source_work_code as sourceWorkCode,
               tb.original_title as originalTitle,
               tc.dist_order_number as sourceWorkCode

        FROM list_match_data_overseas_mapping ta
        LEFT JOIN list_overseas_file_work_mapping tb on ta.file_mapping_id =tb.id
        LEFT JOIN list_overseas_receipt_details tc on tb.receipt_details_id = tc.id
        WHERE ta.file_base_id in
        <foreach collection="list" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        AND ta.reject_code = #{rejectCode}
    </select>

    <select id="getListMatchDataOverseasMappingListByFidsAndNotInWork" resultType="tw.org.must.must.model.report.OverseasCheckingIpNotInWork">
        SELECT td.dist_no as distNo,
        ta.file_base_id as adjNo,
        ta.match_ip_name_no as ipNameNo,
        ta.match_pa_name_no as matchPaNameNo,
        ta.match_ip_name as matchIpName,
        ta.match_ip_role as workIpRole,
        ta.match_ip_soc as societyCode,
        ta.share_ratio as ipShare,
        ta.reject_code as fieCode,
        ta.match_ip_share as distIpShare,
        ta.file_mapping_id as amountSeqNo,
        ta.data_unique_key as distinctWorknum,
        ta.match_work_id as matchWorkId,
        ta.match_work_society_code as matchWorknumSocietyCode,
        tb.source_work_code as sourceWorkCode,
        tb.original_title as originalTitle,
        td.dist_order_number as overseasDistSeq

        FROM list_match_data_overseas_mapping ta
        inner JOIN list_overseas_file_work_mapping tb on ta.file_mapping_id =tb.id
        inner join list_overseas_file_base tc on ta.file_base_id = tc.id
        inner JOIN list_overseas_receipt_details td on tc.receipt_id = td.receipt_id
        WHERE ta.file_base_id in
        <foreach collection="list" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
        AND ta.not_in_work = #{notInWork}
    </select>

    <select id="getListMatchDataOverseasMappingForExport" resultType="tw.org.must.must.model.list.vo.ListMatchDataOverseasMappingExport">
        SELECT
        ta.id as id,
        ta.file_base_id as fileBaseId,
        tb.file_type as `type` ,
        tc.dist_no as mustDistNo,
        tb.remit_society_code as remitsoc,
        td.original_title as remitWorkTitle,
        ta.file_mapping_id as fileMappingId,
        ta.ip_name as remitIpName,
        ta.work_ip_role as remitIpRole,
        ta.ip_name_no as remitIpNameNo,
        ta.ip_society_code as remitIpSoc,
        ta.share_ratio as remitShare,
        td.match_work_title as matchWorkTitle,
        td.match_work_unique_key as matchWorkUniqueKey,
        ta.match_ip_name as matchDistributedIpName,
        ta.match_ip_name_no as matchDistributedIpNameNo,
        ta.match_pa_name_no as matchDistributedIPaNameNo,
        ta.match_ip_role as matchIpRole,
        ta.reject_code as rejectCode,
        ta.status as status

        FROM list_match_data_overseas_mapping ta
        INNER JOIN list_overseas_file_base tb on ta.file_base_id =tb.id
        INNER JOIN list_overseas_receipt tc on tc.id = tb.receipt_id
        INNER JOIN list_match_data_overseas_uniq td on ta.data_unique_key =td.data_unique_key

        where ta.id > #{start} and td.status in(1,3)
        <if test="dataUniqueKey != null and dataUniqueKey != ''">
            and ta.data_unique_key = #{dataUniqueKey}
        </if>
        <if test="null != fId">
            and ta.file_base_id = #{fId}
        </if>
        <if test="matchWorkId != null">
            and ta.match_work_id = #{matchWorkId}
        </if>
        <if test="batch != null">
            and td.batch = #{batch}
        </if>
        <if test="distNo != null and distNo != ''">
            and tc.dist_no = #{distNo}
        </if>
        <if test="matchWorkSoc != null">
            and ta.match_work_society_code = #{matchWorkSoc}
        </if>
        <if test="matchIpSoc != null">
            and ta.match_ip_soc = #{matchIpSoc}
        </if>
        <if test="matchWorkTitle != null and matchWorkTitle != ''">
            and td.match_work_title like CONCAT('%',#{matchWorkTitle},'%')
        </if>
        <if test="distributedIp != null and distributedIp !=''">
            and ta.match_ip_name_no = #{distributedIp}
        </if>
        <if test="distributedIpName != null and distributedIpName !=''">
            and ta.match_ip_name like CONCAT('%',#{distributedIpName},'%')
        </if>
        <if test="remitSoc != null">
            and tb.remit_society_code = #{remitSoc}
        </if>
        <if test="remitWorkTitle != null and remitWorkTitle != ''">
            and td.original_title like CONCAT('%',#{remitWorkTitle},'%')
        </if>
        <if test="remitIpName != null and remitIpName !=''">
            and ta.ip_name like  CONCAT('%',#{remitIpName},'%')
        </if>
        <if test="status != null">
            and ta.status = #{status}
        </if>
        <if test="rejectCode != null">
            <choose>
                <when test="rejectCode = 0">
                    and ta.reject_code is null
                </when>
                <otherwise>
                    and ta.reject_code is not null
                </otherwise>
            </choose>
        </if>

    order by ta.id asc limit 10000
    </select>

    <insert id="insertOnDuplicateKeyUpdate" useGeneratedKeys="true" keyProperty="id" parameterType="tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping">
        insert into list_match_data_overseas_mapping (
            file_base_id,
            file_mapping_id,
            work_code,
            ip_society_code,
            ip_name,
            ip_name_no,
            work_ip_role,
            share_ratio,
            amount,
            territory_code,
            territory_name,
            record_type,
            adjustment_number,
            adjustment_reason_code,
            create_time,
            amend_time,
            reference_number,
            status,
            is_manual,
            description,
            match_ip_name_no,
            match_ip_soc,
            match_ip_name,
            data_unique_key,
            match_work_id,
            match_work_society_code,
            match_work_title_id,
            match_pa_name_no,
            match_name_type,
            match_ip_type,
            reject_code,
            reject_message,
            overseas_id,
            match_info,
            match_work_unique_key,
            match_work_title,
            original_title,
            match_ip_role,
            match_ip_share,
            not_in_work
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.fileBaseId},
            #{item.fileMappingId},
            #{item.workCode},
            #{item.ipSocietyCode},
            #{item.ipName},
            #{item.ipNameNo},
            #{item.workIpRole},
            #{item.shareRatio},
            #{item.amount},
            #{item.territoryCode},
            #{item.territoryName},
            #{item.recordType},
            #{item.adjustmentNumber},
            #{item.adjustmentReasonCode},
            #{item.createTime},
            #{item.amendTime},
            #{item.referenceNumber},
            #{item.status},
            #{item.isManual},
            #{item.description},
            #{item.matchIpNameNo},
            #{item.matchIpSoc},
            #{item.matchIpName},
            #{item.dataUniqueKey},
            #{item.matchWorkId},
            #{item.matchWorkSocietyCode},
            #{item.matchWorkTitleId},
            #{item.matchPaNameNo},
            #{item.matchNameType},
            #{item.matchIpType},
            #{item.rejectCode},
            #{item.rejectMessage},
            #{item.overseasId},
            #{item.matchInfo},
            #{item.matchWorkUniqueKey},
            #{item.matchWorkTitle},
            #{item.originalTitle},
            #{item.matchIpRole},
            #{item.matchIpShare},
            #{item.notInWork}
            )
        </foreach>
            on duplicate key update
            amount = values(amount),
            status = values(status),
            is_manual = values(is_manual),
            match_work_id = values(match_work_id),
            match_work_society_code = values(match_work_society_code),
            match_work_unique_key = values(match_work_unique_key),
            match_ip_name = values(match_ip_name),
            match_ip_name_no = values(match_ip_name_no),
            match_pa_name_no = values(match_pa_name_no),
            match_ip_soc = values(match_ip_soc),
            match_ip_type = values(match_ip_type),
            match_name_type = values(match_name_type),
            match_ip_role = values(match_ip_role),
            match_ip_share = values(match_ip_share),
            match_work_title = values(match_work_title),
            match_work_title_id = values(match_work_title_id),
            match_info = values(match_info),
            overseas_id = values(overseas_id),
            reject_code = values(reject_code),
            reject_message = values(reject_message),
            not_in_work = values(not_in_work),
            amend_time = values(amend_time)

    </insert>

    <select id="getListMatchDataOverseasMappingGroupByFid" resultType="tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping">
        select file_base_id,sum(amount) as amount from list_match_data_overseas_mapping where amount != 0 and file_base_id in
        <foreach item="item" collection="fileBaseIds" index="index" separator="," close=")" open="(">
            #{item}
        </foreach>
        group by file_base_id
    </select>

</mapper>