package tw.org.must.must.core.retry;

import tw.org.must.must.model.orcal.OrcalWrkWork;

import java.util.List;

public interface RetryService {
    String syncWrkWorkData(String lastWrkWorkAmendTime, int start, int end);

    String syncAddAgrData(String lastAgrAmendTime, int start, int end);

    String syncMbrIpName(String lastMbrIpNameAmendTime, int start, int end);

    String syncMbrMemberShip(String lastMbrMemberAmendTime, int start, int end);
}
