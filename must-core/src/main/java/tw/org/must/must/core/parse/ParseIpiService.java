package tw.org.must.must.core.parse;

import com.alibaba.fastjson.JSONObject;
import com.firstbrave.api.ipi.base.Record;
import com.firstbrave.api.ipi.vo.*;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tw.org.must.must.core.handle.RefTerritoryRelationHandle;
import tw.org.must.must.core.service.mbr.*;
import tw.org.must.must.core.service.ref.RefTerritoryRelationService;
import tw.org.must.must.model.mbr.*;
import tw.org.must.must.model.ref.RefTerritoryRelation;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 解析ipi
 *
 * <AUTHOR>
 */
@Service
public class ParseIpiService {

    private static final Logger logger = LoggerFactory.getLogger(ParseIpiService.class);

    @Autowired
    private MbrIpAgreementService mbrIpAgreementService;
    @Autowired
    private MbrIpAgreementTerritoryService mbrIpAgreementTerritoryService;

    @Autowired
    private MbrIpStatusService mbrMbrIpStatusService;

    @Autowired
    private MbrIpNationalityService mbrIpNationalityService;
    @Autowired
    private MbrIpNameUsageService mbrIpNameUsageService;
    @Autowired
    private MbrIpSocietyRemarkService mbrIpSocietyRemarkService;
    @Autowired
    private MbrMemberInfoService memberInfoService;

    @Autowired
    private RefTerritoryRelationService refTerritoryRelationService;

    @Autowired
    private MbrIpNameMergeService mbrIpNameMergeService;
    @Autowired
    private MbrIpNameService mbrIpNameService;

    @Autowired
    private MbrIpService mbrIpService;

    public static void main(String[] args) {
//		ParseIpi parseIpi = new ParseIpi("C:\\var\\ipi\\20200123.IPI") {
        ParseIpi parseIpi = new ParseIpi("C:\\Users\\<USER>\\Desktop\\20220112.IPI") {
            @Override
            protected void parseMA(Transaction transaction, String maoGroup) {
                System.out.println("mau" + net.sf.json.JSONObject.fromObject(transaction));
                System.out.println("mau" + maoGroup);
            }

            @Override
            protected void parseADD(Transaction transaction, List<Record> list) {
                System.out.println(net.sf.json.JSONObject.fromObject(transaction));
                System.out.println(net.sf.json.JSONArray.fromObject(list));
            }
        };
    }

    @Transactional
    public Boolean parse(String file) {
        ParseIpiService parseIpiService = this;
        Path path = Paths.get(file);
        if (!path.toFile().exists()) {
            return false;
        }
        ParseIpi parseIpi = new ParseIpi(file) {

            @Override
            protected void parseMA(Transaction transaction, String json) {
                logger.info("ipbaseno:{}", transaction.getIpBaseNumber());
                parseIpiService.updateDbMAOMAN(transaction, json);
            }

            @Override
            protected void parseADD(Transaction transaction, List<Record> list) {
                logger.info("ipbaseno:{}", transaction.getIpBaseNumber());
                parseIpiService.insertDbAdd(transaction, list);
            }
        };
        return parseIpi.getSuccess();
    }

    /**
     * 插入mbr_ip_agreement表
     * 2、唯一索引 `ip_base_no` `society_code` `creation_class_code
     * `right_code` `role_code` `tis_n`
     *
     * @param transaction
     * @param json
     */
    public void updateDbMAOMAN(Transaction transaction, String json) {

        List<MbrIpAgreement> mbrIpAgreementList = new ArrayList<>();
        List<MbrIpAgreement> deleteMbrIpAgreementList = new ArrayList<>();
        List<MbrIpAgreementTerritory> mbrIpAgreementTerritoryList = new ArrayList<>();
        List<MbrIpAgreementTerritory> deleteMbrIpAgreementTerritoryList = new ArrayList<>();

        // 查询所有区域包含
        Map<Long, Map<Long, RefTerritoryRelation>> territoryRelation = refTerritoryRelationService.getMapTisNKeyMap();


        String[] split = json.split("&MAO&"); // MAA中没有这标签，不影响后续操作
        for (String maoGroup : split) {
            String[] maoSplitList = maoGroup.split("&MAN&"); // MAD中没有这标签，不影响后续操作
            for (int i = 0; i < maoSplitList.length; i++) {
                String[] tmp = maoSplitList[i].split("&TMA&");
                JSONObject maoOrMan = (JSONObject) JSONObject.parse(tmp[0]);
                String recordType = maoOrMan.getString("recordType");
                MbrIpAgreement mbrIpAgreement = BeanToBeanIpi.getMbrIpAgreement(transaction, maoOrMan);
                List<MbrIpAgreementTerritory> mbrIpAgreementTerritoryTmpList = new ArrayList<>();
                for (int j = 1; j < tmp.length; j++) {
                   try{
                       MbrIpAgreementTerritory mbrIpAgreementTerritory = BeanToBeanIpi.getMbrIpAgreementTerritory(transaction,
                               tmp[0], tmp[j]);
                       if(null != mbrIpAgreementTerritory){
                           mbrIpAgreementTerritoryTmpList.add(mbrIpAgreementTerritory);
                       }
                   }catch (Exception e){
                        e.printStackTrace();
                   }
                }

                if (StringUtils.equals("MAO", recordType)) {
                    // 当MAU标签时候，只要解析MAN下的数据，做更新操作，但是由于原先解析逻辑的混乱，导致数据库中脏数据很多，MAO相关的数据也插入了，也有肯能是新的MAN，所以当前不做更新操作；
                    // 只做删除操作，之后再插入
                    deleteMbrIpAgreementList.add(mbrIpAgreement);
                    deleteMbrIpAgreementTerritoryList.addAll(mbrIpAgreementTerritoryTmpList);
                } else if (StringUtils.equals("MAN", recordType)) {
                    // tsn 158 判断台湾有没有权利
                    Integer tisN = RefTerritoryRelationHandle.getTerritory(territoryRelation, 158,
                            mbrIpAgreementTerritoryTmpList);
                    mbrIpAgreement.setTisN(tisN);

                    mbrIpAgreementList.add(mbrIpAgreement);
                    mbrIpAgreementTerritoryList.addAll(mbrIpAgreementTerritoryTmpList);
                }
            }
        }

        if (!deleteMbrIpAgreementList.isEmpty()) {
            mbrIpAgreementService.deleteByIPI(deleteMbrIpAgreementList);
            mbrIpAgreementTerritoryService.deleteByIPI(deleteMbrIpAgreementTerritoryList);
        }
        if (!mbrIpAgreementList.isEmpty()) {
            mbrIpAgreementService.saveSelectiveList(mbrIpAgreementList);
            mbrIpAgreementTerritoryService.saveSelectiveList(mbrIpAgreementTerritoryList);
        }
        // 修改ipi表时间
        // TODO
        String ipBaseNumber = transaction.getIpBaseNumber();
        if (StringUtils.isNotBlank(ipBaseNumber)) {
            logger.debug("ipBaseNumber: {}", ipBaseNumber);
            List<String> ipNameNoList = mbrIpNameMergeService.getIpNameNo(new ArrayList<>(Arrays.asList(ipBaseNumber)));
            List<MbrIpName> ipNameList = mbrIpNameService.getIpNameListByIpNameNoList(ipNameNoList);
            List<Long> idList = ipNameList.stream().map(MbrIpName::getId).collect(Collectors.toList());
            mbrIpService.getSynMbrIpByIdList(idList);
            mbrIpNameMergeService.updateSelectiveTime(ipNameNoList);
        }
    }

    private static ExecutorService pool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors(),
            0L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("ipi-pool-%d").build());

   /* private static ReentrantLock lock_1 = new ReentrantLock(true);
    private static ReentrantLock lock_2 = new ReentrantLock(true);
    private static ReentrantLock lock_3 = new ReentrantLock(true);
    private static ReentrantLock lock_4 = new ReentrantLock(true);
    private static ReentrantLock lock_5 = new ReentrantLock(true);
    private static ReentrantLock lock_6 = new ReentrantLock(true);
    private static ReentrantLock lock_7 = new ReentrantLock(true);*/

    /**
     * IPA 入库
     *
     * @param transaction
     * @param list
     */
    public void insertDbAdd(Transaction transaction, List<Record> list) {
        List<MbrIp> mbrIpList = new ArrayList<>();
        List<MbrMemberInfo> mbrMemberList = new ArrayList<>();
        List<MbrIpStatus> mbrIpStatusList = new ArrayList<>();
        List<MbrIpName> mbrIpNameList = new ArrayList<>();
        List<MbrIpNationality> mbrIpNationalityList = new ArrayList<>();
        List<MbrIpNameUsage> mbrIpNameUsageList = new ArrayList<>();
        List<MbrIpSocietyRemark> mbrIpSocietyRemarkList = new ArrayList<>();
        for (Record record : list) {
            if (record instanceof BaseData) {
                MbrIp mbrIp = BeanToBeanIpi.getMbrIp(transaction, (BaseData) record);
                if (mbrIp.getSocietyCode() == 161) {
                    MbrMemberInfo memberInfo = memberInfoService.getByIpNo(mbrIp.getIpBaseNo());
                    if (memberInfo == null) {
                        memberInfo = new MbrMemberInfo();
                        memberInfo.setIpBaseNo(mbrIp.getIpBaseNo());
                        memberInfo.init();
                        mbrMemberList.add(memberInfo);
                    }
                }
                mbrIpList.add(mbrIp);
            } else if (record instanceof Status) {
                MbrIpStatus mbrIpStatus = BeanToBeanIpi.getMbrIpStatus(transaction, (Status) record);
                mbrIpService.editStatus(mbrIpStatus);
                mbrIpStatusList.add(mbrIpStatus);
            } else if (record instanceof NameSingleIpConnection) {
                MbrIpName mbrIpName = BeanToBeanIpi.getMbrIpName(transaction, (NameSingleIpConnection) record);
                mbrIpNameList.add(mbrIpName);
            } else if (record instanceof Nationality) {
                MbrIpNationality mbrIpNationality = BeanToBeanIpi.getMbrIpNationality(transaction,
                        (Nationality) record);
                mbrIpNationalityList.add(mbrIpNationality);
            } else if (record instanceof NameSingleIpUsage) {
                MbrIpNameUsage mbrIpNameUsage = BeanToBeanIpi.getMbrIpNameUsage(transaction,
                        (NameSingleIpUsage) record);
                mbrIpNameUsageList.add(mbrIpNameUsage);
            } else if (record instanceof NameMultiIpConnection) {
                MbrIpName mbrIpName = BeanToBeanIpi.getMbrIpName(transaction, (NameMultiIpConnection) record);
                mbrIpNameList.add(mbrIpName);
            } else if (record instanceof OtherNameConnection) {
                MbrIpName mbrIpName = BeanToBeanIpi.getMbrIpName(transaction, (OtherNameConnection) record);
                mbrIpNameList.add(mbrIpName);
            } else if (record instanceof InheritedNameSingleIpUsage) {
                MbrIpNameUsage mbrIpNameUsage = BeanToBeanIpi.getMbrIpNameUsage(transaction,
                        (InheritedNameSingleIpUsage) record);
                mbrIpNameUsageList.add(mbrIpNameUsage);
            } else if (record instanceof NameMultiIpUsage) {
                MbrIpNameUsage mbrIpNameUsage = BeanToBeanIpi.getMbrIpNameUsage(transaction, (NameMultiIpUsage) record);
                mbrIpNameUsageList.add(mbrIpNameUsage);
            } else if (record instanceof Remark) {
                MbrIpSocietyRemark mbrIpSocietyRemark = BeanToBeanIpi.getMbrIpSocietyRemark(transaction,
                        (Remark) record);
                mbrIpSocietyRemarkList.add(mbrIpSocietyRemark);
            } else if (record instanceof TransactionStatus) {
                // TODO
                // 没见过的类别
                // {"codeOfRemittingSociety":161,"currentStatusCode":1,"currentStatusCodeRef":1,"detailRecordSequenceNo":"00000000","ipBaseNumber":"I-005231784-5","ipBaseNumberRef":"I-005231784-5","ipNameNumber":389997459,"ipNameNumberRef":389997459,"nameOfRemittingSociety":"MUST","nameType":"PA","nameTypeRef":"PA","recordType":"IPA","transactionDate":"20190620","transactionSequenceNo":"00000000","transactionTime":"083208"}
//				class com.firstbrave.ipi.api.vo.TransactionStatus     {"creationDateRef":"20190620","creationTimeRef":"083208","detailRecordSequenceNo":"00000001","fileNameRef":"IPI01080.080","groupIdRef":1,"nameOfTransactionSubmitter":"IX.CHAN","recordType":"TRS","statusCode":"0000","statusMessage":"IPA: SUCCESSFUL COMPLETION","transactionSequenceNo":"00000000","transactionSequenceNoRef":0}

//				System.out.println(JSONObject.toJSONString(transaction));
//				System.out.println(record.getClass() + "     " + JSONObject.toJSONString(record));
            } else {
                System.out.println(record.getClass() + " --------------------    " + JSONObject.toJSONString(record));
            }
        }

        mbrIpService.saveSelectiveList(mbrIpList);
        memberInfoService.saveMemberInfoListOrIgnore(mbrMemberList);
        mbrMbrIpStatusService.saveSelectiveList(mbrIpStatusList);

        String ipBaseNo = transaction.getIpBaseNumber();
        mbrIpNameService.saveSelectiveList(ipBaseNo, mbrIpNameList);
        List<String> ipNameNoList = mbrIpNameList.stream().map(MbrIpName::getIpNameNo).collect(Collectors.toList());
        List<MbrIpName> ipNameList = mbrIpNameService.getIpNameListByIpNameNoList(ipNameNoList);
        List<Long> idList = ipNameList.stream().map(MbrIpName::getId).collect(Collectors.toList());
        mbrIpService.getSynMbrIpByIdList(idList);

        mbrIpNationalityService.saveSelectiveList(mbrIpNationalityList);

        mbrIpNameUsageService.saveSelectiveList(mbrIpNameUsageList);

        mbrIpSocietyRemarkService.saveSelectiveList(mbrIpSocietyRemarkList);

        // 写入数据库
        /*pool.execute(() -> {
            try {
                lock_1.lock();
                mbrIpService.saveSelectiveList(mbrIpList);
            }finally {
                lock_1.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_2.lock();
                memberInfoService.addList(mbrMemberList);
            }finally {
                lock_2.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_3.lock();
                mbrMbrIpStatusService.saveSelectiveList(mbrIpStatusList);
            }finally {
                lock_3.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_4.lock();
                String ipBaseNo = transaction.getIpBaseNumber();
                mbrIpNameService.saveSelectiveList(ipBaseNo, mbrIpNameList);
                List<String> ipNameNoList = mbrIpNameList.stream().map(MbrIpName::getIpNameNo).collect(Collectors.toList());
                List<MbrIpName> ipNameList = mbrIpNameService.getIpNameListByIpNameNoList(ipNameNoList);
                List<Long> idList = ipNameList.stream().map(MbrIpName::getId).collect(Collectors.toList());
                mbrIpService.getSynMbrIpByIdList(idList);
            } finally {
                lock_4.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_5.lock();
                mbrIpNationalityService.saveSelectiveList(mbrIpNationalityList);
            } finally {
                lock_5.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_6.lock();
                mbrIpNameUsageService.saveSelectiveList(mbrIpNameUsageList);
            } finally {
                lock_6.unlock();
            }
        });
        pool.execute(() -> {
            try {
                lock_7.lock();
                mbrIpSocietyRemarkService.saveSelectiveList(mbrIpSocietyRemarkList);
            } finally {
                lock_7.unlock();
            }
        });*/
    }

}
