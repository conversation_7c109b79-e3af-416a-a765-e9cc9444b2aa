package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class Sy09 extends BaseTsvEntity {


	
	private String SummaryRecordId;
	
	private String CommercialModel;	
	
	private String UseType;	
	
	private String Territory;
	
	private String ServiceDescription;
	
	private String SubscriberType;	
    
	private String RightsController;
	
	private String RightsControllerPartyID;
	
	private String RightsType;
	
	private String TotalUsages;
	
	private String AllocatedUsages;
	
	private String NetRevenue;	
	
	private String IndirectNetRevenue;
	
	private String RightsControllerMarketShare;
	
	private String Currency;
	
	private String ExchangeRateBaseCurrency;	
     
	private String ExchangeRate;
	
	private String RightsTypePercentage;
	
	private String SubPeriodStartDate;
	
	private String SubPeriodEndDate;



	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getCommercialModel() {
		return CommercialModel;
	}

	public void setCommercialModel(String commercialModel) {
		CommercialModel = commercialModel;
	}

	public String getUseType() {
		return UseType;
	}

	public void setUseType(String useType) {
		UseType = useType;
	}

	public String getTerritory() {
		return Territory;
	}

	public void setTerritory(String territory) {
		Territory = territory;
	}

	public String getServiceDescription() {
		return ServiceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		ServiceDescription = serviceDescription;
	}

	public String getSubscriberType() {
		return SubscriberType;
	}

	public void setSubscriberType(String subscriberType) {
		SubscriberType = subscriberType;
	}

	public String getRightsController() {
		return RightsController;
	}

	public void setRightsController(String rightsController) {
		RightsController = rightsController;
	}

	public String getRightsControllerPartyID() {
		return RightsControllerPartyID;
	}

	public void setRightsControllerPartyID(String rightsControllerPartyID) {
		RightsControllerPartyID = rightsControllerPartyID;
	}

	public String getRightsType() {
		return RightsType;
	}

	public void setRightsType(String rightsType) {
		RightsType = rightsType;
	}

	public String getTotalUsages() {
		return TotalUsages;
	}

	public void setTotalUsages(String totalUsages) {
		TotalUsages = totalUsages;
	}

	public String getAllocatedUsages() {
		return AllocatedUsages;
	}

	public void setAllocatedUsages(String allocatedUsages) {
		AllocatedUsages = allocatedUsages;
	}

	public String getNetRevenue() {
		return NetRevenue;
	}

	public void setNetRevenue(String netRevenue) {
		NetRevenue = netRevenue;
	}

	public String getIndirectNetRevenue() {
		return IndirectNetRevenue;
	}

	public void setIndirectNetRevenue(String indirectNetRevenue) {
		IndirectNetRevenue = indirectNetRevenue;
	}

	public String getRightsControllerMarketShare() {
		return RightsControllerMarketShare;
	}

	public void setRightsControllerMarketShare(String rightsControllerMarketShare) {
		RightsControllerMarketShare = rightsControllerMarketShare;
	}

	public String getCurrency() {
		return Currency;
	}

	public void setCurrency(String currency) {
		Currency = currency;
	}

	public String getExchangeRateBaseCurrency() {
		return ExchangeRateBaseCurrency;
	}

	public void setExchangeRateBaseCurrency(String exchangeRateBaseCurrency) {
		ExchangeRateBaseCurrency = exchangeRateBaseCurrency;
	}

	public String getExchangeRate() {
		return ExchangeRate;
	}

	public void setExchangeRate(String exchangeRate) {
		ExchangeRate = exchangeRate;
	}

	public String getRightsTypePercentage() {
		return RightsTypePercentage;
	}

	public void setRightsTypePercentage(String rightsTypePercentage) {
		RightsTypePercentage = rightsTypePercentage;
	}

	public String getSubPeriodStartDate() {
		return SubPeriodStartDate;
	}

	public void setSubPeriodStartDate(String subPeriodStartDate) {
		SubPeriodStartDate = subPeriodStartDate;
	}

	public String getSubPeriodEndDate() {
		return SubPeriodEndDate;
	}

	public void setSubPeriodEndDate(String subPeriodEndDate) {
		SubPeriodEndDate = subPeriodEndDate;
	}
	
}
