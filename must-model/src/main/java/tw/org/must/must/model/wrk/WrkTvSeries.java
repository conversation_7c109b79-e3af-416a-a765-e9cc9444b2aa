package tw.org.must.must.model.wrk;

import javax.persistence.Column;
import javax.persistence.Table;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`wrk_tv_series`")
public class WrkTvSeries extends BaseEntity{

   /**
     * 
     */
	@Column(name="worknum")
    private Long worknum;

    /**
     * 
     */
	@Column(name="worknum_society")
    private Integer worknumSociety;

    /**
     * 
     */
    @Column(name = "`sub_title_id`")
    private String subTitleId;

    /**
     * 
     */
    @Column(name = "`e_esp_no`")
    private String eEspNo;

    /**
     * 
     */
    @Column(name = "`c_esp_no`")
    private String cEspNo;

    public Integer getWorknumSociety() {
        return worknumSociety;
    }

    public void setWorknumSociety(Integer worknumSociety) {
        this.worknumSociety = worknumSociety;
    }

    public String getSubTitleId() {
        return subTitleId;
    }

    public void setSubTitleId(String subTitleId) {
        this.subTitleId = subTitleId;
    }

    public String getEEspNo() {
        return eEspNo;
    }

    public void setEEspNo(String eEspNo) {
        this.eEspNo = eEspNo;
    }

    public String getCEspNo() {
        return cEspNo;
    }

    public void setCEspNo(String cEspNo) {
        this.cEspNo = cEspNo;
    }

	public Long getWorknum() {
		return worknum;
	}

	public void setWorknum(Long worknum) {
		this.worknum = worknum;
	}



}