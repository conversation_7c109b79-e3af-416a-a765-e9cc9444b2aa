package tw.org.must.must.core.shiro;

import com.alibaba.fastjson.JSON;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tw.org.must.must.model.sys.SysUser;

public class LoginUtil {

    private static final Logger logger = LoggerFactory.getLogger(LoginUtil.class);

    public static Long getUserId() {
        Long result = 0L;
        try {
            Subject subject = SecurityUtils.getSubject();
            SysUser user = (SysUser) subject.getPrincipal();
            if (null != user) {
                result = user.getId();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String getUserName() {
        String result = "";
        try {
            Subject subject = SecurityUtils.getSubject();
            SysUser user = (SysUser) subject.getPrincipal();
            if (null != user) {
                result = user.getAccount();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
