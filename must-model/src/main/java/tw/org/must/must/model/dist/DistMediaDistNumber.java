package tw.org.must.must.model.dist;

import javax.persistence.Column;
import javax.persistence.Table;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`dist_media_dist_number`")
public class DistMediaDistNumber extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 分配描述
     */
    @Column(name = "`description`")
    private String description;

    /**
     * 
     */
    @Column(name = "`remark`")
    private String remark;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }



}