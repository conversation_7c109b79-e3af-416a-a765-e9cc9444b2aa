package tw.org.must.must.core.parse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.firstbrave.api.crd.vo.menu.DistributionCategoryMenu;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.firstbrave.api.base.F2Record;
import com.firstbrave.api.f2.vo.F2Header;
import com.firstbrave.api.f2.vo.F2RoyaltyDistribution;
import com.firstbrave.api.f2.vo.F2Trailer;
import com.firstbrave.api.parser.F2FileDecoder;

import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.LocalCommonMethodUtils;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileBaseService;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileWorkMappingCheckService;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileWorkMappingService;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;
import tw.org.must.must.model.listoverseas.ListOverseasFileWorkMapping;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_1000;

@Service
public class ParseF2Service {

	@Autowired
	private ListOverseasFileBaseService listOverseasFileBaseService;
	
	@Autowired
	private ListOverseasFileWorkMappingService listOverseasFileWorkMappingService;

	@Autowired
	private ListOverseasFileWorkMappingCheckService listOverseasFileWorkMappingCheckService;

//    public void parse(String file) {
//        Path path = Paths.get(file);
//        if (!path.toFile().exists()) {
//            return;
//        }
//        try {
//            List<String> lines = Files.readAllLines(path);
//            LinkedList<String> linkedList = new LinkedList<>(lines);
//            if(linkedList.peekLast() != null && linkedList.peekLast().startsWith("@@@")){
//                linkedList.pollLast();
//            }
//            List<F2Record> f2Records = F2FileDecoder.decode(linkedList);
//            if (!f2Records.isEmpty()) {
//                insertDb(path, f2Records);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

//    @Transactional(rollbackFor = Exception.class)
//    public void insertDb(Path path, List<F2Record> f2Records) {
//        DisOInventory oInventory = null;
//        List<DisOInventoryDetail> details = new ArrayList<>();
//        List<DisORecord> records = new ArrayList<>();
//        Set<String> workCodes = new HashSet<>();
//        Long fid = 0L;
//        Date date = new Date();
//        int amountDecimalDigits = 0;
//        for (F2Record f2Record : f2Records) {
//            if (f2Record instanceof F2Header) {
//                F2Header f2Header = (F2Header) f2Record;
//                amountDecimalDigits = f2Header.getNumberOfDecimalPlacesInAmountFields();
//                fid = NoGener.getIncrement("dis_o");
//                oInventory = BeanToBeanDistribution.getDisOInventory(fid, path.toFile().getName(), path.toFile().getAbsolutePath(), date, f2Header);
//            }else if(f2Record instanceof F2RoyaltyDistribution){
//                F2RoyaltyDistribution distribution = (F2RoyaltyDistribution) f2Record;
//                if (!workCodes.contains(distribution.getWorkCode())) {
//                    details.add(BeanToBeanDistribution.getDisOInventoryDetail(fid, date, distribution));
//                    workCodes.add(distribution.getWorkCode());
//                }
//                records.add(BeanToBeanDistribution.getDisORecord(fid, date, amountDecimalDigits, distribution));
//            }else if(f2Record instanceof F2Trailer){
//
//            }
//        }
//        disOInventoryService.add(oInventory);
//        disOInventoryDetailService.addList(details);
//        disORecordService.addList(records);
//    }

	@Transactional(rollbackFor = RuntimeException.class)
	public void parse(String filePath, ListOverseasFileBase listOverseasFileBase) {
		Path path = Paths.get(filePath);
        if (!path.toFile().exists()) {
            return;
        }
        try {
//			List<String> lines =  LocalCommonMethodUtils.readAllLinesSkippingBom(path, StandardCharsets.UTF_8);
            List<String> lines = Files.readAllLines(path);
			byte[] bytes = Files.readAllBytes(path);
            LinkedList<String> linkedList = new LinkedList<>(lines);
            if(linkedList.peekLast() != null && linkedList.peekLast().startsWith("@@@")){
                linkedList.pollLast();
            }
			StringBuffer errorMsg=new StringBuffer();
            List<F2Record> f2Records = F2FileDecoder.decode(errorMsg,linkedList);
			if (errorMsg.length() > 0){
				throw new MustException(
						ResultCode.Unknown_Exception.getCode(),
						errorMsg.toString());
			}
            if (!f2Records.isEmpty()) {
                insertDbNew(path, f2Records,listOverseasFileBase);
            }
        } catch (Exception e) {
        	e.printStackTrace();
        	if(e instanceof MustException){
        		throw (MustException)e;
			}
            throw new RuntimeException(e);
        }
	}
	
	void insertDbNew(Path path, List<F2Record> f2Records, ListOverseasFileBase listOverseasFileBase) {
        List<ListOverseasFileWorkMapping> listOverseasFileWorkMappingList = new ArrayList<ListOverseasFileWorkMapping>();
		AtomicInteger currentLine = new AtomicInteger(1);
		Map<String,List<String>> authorComposerMap = new HashMap<>();
		Map<String,List<String>> authorComposerIpMap = new HashMap<>();
		for (F2Record f2Record : f2Records) {
            if (f2Record instanceof F2Header) {
                F2Header f2Header = (F2Header) f2Record;
                listOverseasFileBase = updateListOverseasFileBaseByHeader(path.toFile().getAbsolutePath(),path.toFile().getName(),listOverseasFileBase, f2Header);
            }else if(f2Record instanceof F2RoyaltyDistribution){
                F2RoyaltyDistribution distribution = (F2RoyaltyDistribution) f2Record;
                ListOverseasFileWorkMapping listOverseasFileWorkMapping = updateListOverseasFileBaseByDetails(listOverseasFileBase, distribution,authorComposerMap,authorComposerIpMap);
//                calcTotalAmount(listOverseasFileWorkMapping,listOverseasFileBase);
                //amount数值调整
//		    listOverseasFileWorkMapping.setAmount(listOverseasFileWorkMapping.getAmount().divide(new BigDecimal(100),2, RoundingMode.HALF_UP));
							listOverseasFileWorkMapping.setFileBaseId(listOverseasFileBase.getId());
				listOverseasFileWorkMapping.setFileType(2);
				listOverseasFileWorkMapping.setLine(currentLine.get());
                listOverseasFileWorkMappingList.add(listOverseasFileWorkMapping);
            }else if(f2Record instanceof F2Trailer){
            	F2Trailer f2Trailer = (F2Trailer) f2Record;
            	listOverseasFileBase = updateListOverseasFileBaseByTrailer(listOverseasFileBase, f2Trailer);
            }
			currentLine.incrementAndGet();
        }

		calcTotalAmount(listOverseasFileWorkMappingList,listOverseasFileBase);
		listOverseasFileBaseService.update(listOverseasFileBase);

		Map<String,String> caIpNameNoMap = new HashMap<>();
		for(Map.Entry<String,List<String>> entry : authorComposerIpMap.entrySet()){
			String sourceWorkCode = entry.getKey();
			List<String> authorIpNames = entry.getValue();
			if(CollectionUtils.isNotEmpty(authorIpNames)){
				caIpNameNoMap.put(sourceWorkCode, StringUtils.join(authorIpNames,"/"));
			}
		}

		Map<String,String> caIpNameMap = new HashMap<>();
		for(Map.Entry<String,List<String>> entry : authorComposerMap.entrySet()){
			String sourceWorkCode = entry.getKey();
			List<String> authorIpNames = entry.getValue();
			if(CollectionUtils.isNotEmpty(authorIpNames)){
				caIpNameMap.put(sourceWorkCode, StringUtils.join(authorIpNames,"/"));
			}
		}

		listOverseasFileWorkMappingList.forEach(l -> {
			String sourceWorkCode = l.getSourceWorkCode();
			l.setAuthorComposer(caIpNameMap.getOrDefault(sourceWorkCode,""));
			l.setAuthorIpNameNo(caIpNameNoMap.getOrDefault(sourceWorkCode,""));
		});


		List<List<ListOverseasFileWorkMapping>> partitions = Lists.partition(listOverseasFileWorkMappingList,BATCH_SIZE_1000);
		for(List<ListOverseasFileWorkMapping> partition : partitions){
			listOverseasFileWorkMappingService.batchAddList(partition);
		}

		listOverseasFileWorkMappingCheckService.checkingForFileTotal(listOverseasFileWorkMappingList,listOverseasFileBase);
	}

	private void calcTotalAmount(ListOverseasFileBase listOverseasFileBase){
		BigDecimal normalAmount = listOverseasFileBase.getNomalAmount() == null ? BigDecimal.ZERO : listOverseasFileBase.getNomalAmount() ;
		BigDecimal adjAmount = listOverseasFileBase.getAdjAmount() == null ? BigDecimal.ZERO : listOverseasFileBase.getAdjAmount() ;
		BigDecimal fieAmount = listOverseasFileBase.getFieAmount() == null ? BigDecimal.ZERO: listOverseasFileBase.getFieAmount() ;

		listOverseasFileBase.setNetAmount(normalAmount.add(adjAmount));
		listOverseasFileBase.setTotalAmount(listOverseasFileBase.getNetAmount().add(fieAmount));
	}

	private void calcTotalAmount(List<ListOverseasFileWorkMapping> listOverseasFileWorkMappings,
								 ListOverseasFileBase listOverseasFileBase) {

		BigDecimal nomalAmount = BigDecimal.ZERO;
		BigDecimal adjAmount = BigDecimal.ZERO;
		BigDecimal fieAmount = BigDecimal.ZERO;
		Integer normalAdjIpNumber = 0;
		Integer fieIpNumber = 0;
		Set<String> normalAdjWorkNumberSet = new HashSet<>();
		Set<String> fieWorkNumberSet = new HashSet<>();

		for(ListOverseasFileWorkMapping listOverseasFileWorkMapping : listOverseasFileWorkMappings){
			Integer recordType = listOverseasFileWorkMapping.getRecordType();
			String signIndicator = listOverseasFileWorkMapping.getSignIndicator();
			BigDecimal amount = listOverseasFileWorkMapping.getAmount() == null ? BigDecimal.ZERO : listOverseasFileWorkMapping.getAmount();

			if(amount.compareTo(BigDecimal.ZERO) == 0){
				continue;
			}

			if ("D".equalsIgnoreCase(signIndicator)) {
				amount = amount.multiply(new BigDecimal(-1));
			}

			if (1 == recordType) {
				nomalAmount = nomalAmount.add(amount);
				normalAdjIpNumber ++;
				if(!normalAdjWorkNumberSet.contains(listOverseasFileWorkMapping.getDataUniqueKey())){
					normalAdjWorkNumberSet.add(listOverseasFileWorkMapping.getDataUniqueKey());
				}
			} else if (2 == recordType) {
				adjAmount = adjAmount.add(amount);
				normalAdjIpNumber ++;
				if(!normalAdjWorkNumberSet.contains(listOverseasFileWorkMapping.getDataUniqueKey())){
					normalAdjWorkNumberSet.add(listOverseasFileWorkMapping.getDataUniqueKey());
				}
			} else if (3 == recordType) {
				fieAmount = fieAmount.add(amount);
				fieIpNumber ++;
				if(!fieWorkNumberSet.contains(listOverseasFileWorkMapping.getDataUniqueKey())){
					fieWorkNumberSet.add(listOverseasFileWorkMapping.getDataUniqueKey());
				}
			}
		}

		listOverseasFileBase.setNomalAmount(nomalAmount);
		listOverseasFileBase.setAdjAmount(adjAmount);
		listOverseasFileBase.setFieAmount(fieAmount);
		listOverseasFileBase.setNetAmount(nomalAmount.add(adjAmount));
		listOverseasFileBase.setTotalAmount(listOverseasFileBase.getNetAmount().add(fieAmount));

		listOverseasFileBase.setNormalAdjWorkNumber(normalAdjWorkNumberSet.size());
		listOverseasFileBase.setFieWorkNumber(fieWorkNumberSet.size());
		listOverseasFileBase.setFileWorkNumber(normalAdjWorkNumberSet.size() + fieWorkNumberSet.size());

		listOverseasFileBase.setNormalAdjIpNumber(normalAdjIpNumber);
		listOverseasFileBase.setFieIpNumber(fieIpNumber);
		listOverseasFileBase.setFileIpNumber(listOverseasFileWorkMappings.size());

	}

	private void calcTotalAmount(ListOverseasFileWorkMapping listOverseasFileWorkMapping,
								 ListOverseasFileBase listOverseasFileBase) {
		Integer recordType = listOverseasFileWorkMapping.getRecordType();
		String signIndicator = listOverseasFileWorkMapping.getSignIndicator();
		BigDecimal amount = listOverseasFileWorkMapping.getAmount() == null ? BigDecimal.ZERO
				: listOverseasFileWorkMapping.getAmount();
		BigDecimal signDecimal = BigDecimal.ONE;
		if ("D".equalsIgnoreCase(signIndicator)) {
			signDecimal = new BigDecimal(-1);
		}
		BigDecimal adjAmount,fieAmount,nomalAmount;
		if (1 == recordType) {
			if (listOverseasFileBase.getNomalAmount() != null) {
				nomalAmount = listOverseasFileBase.getNomalAmount().add(amount.multiply(signDecimal));
			} else {
				nomalAmount = BigDecimal.ZERO.add(amount.multiply(signDecimal));
			}
			listOverseasFileBase.setNomalAmount(nomalAmount);
		} else if (2 == recordType) {
			if (listOverseasFileBase.getAdjAmount() != null) {
				adjAmount = listOverseasFileBase.getAdjAmount().add(amount.multiply(signDecimal));
			} else {
				adjAmount = BigDecimal.ZERO.add(amount.multiply(signDecimal));
			}
			listOverseasFileBase.setAdjAmount(adjAmount);
		} else if (3 == recordType) {
			if (listOverseasFileBase.getFieAmount() != null) {
				fieAmount = listOverseasFileBase.getFieAmount().add(amount.multiply(signDecimal));
			} else {
				fieAmount = BigDecimal.ZERO.add(amount.multiply(signDecimal));
			}
			listOverseasFileBase.setFieAmount(fieAmount);
		}
	}

	private ListOverseasFileWorkMapping updateListOverseasFileBaseByDetails(ListOverseasFileBase listOverseasFileBase,
			F2RoyaltyDistribution distribution,Map<String,List<String>> authorComposerMap, Map<String,List<String>> authorComposerIpMap) {
		ListOverseasFileWorkMapping listOverseasFileWorkMapping = new ListOverseasFileWorkMapping();
		
		// 1:正常   2：adj 3:fie
		Integer recordType = distribution.getRecordType();
		Integer pointSize = listOverseasFileBase.getAmountPointSize();
		Double pointNumber = Math.pow(10L,pointSize);
		
		String signIndicator = distribution.getSignIndicator();
		listOverseasFileWorkMapping.setSignIndicator(signIndicator);
		
		String workCode = distribution.getWorkCode();
		String iswc = distribution.getIswc();
		Integer receiptSociety = distribution.getCodeOfReceivingSociety();
		Integer referenceNumber = distribution.getReferenceNumber();
		listOverseasFileWorkMapping.setFileBaseId(listOverseasFileBase.getId());
		listOverseasFileWorkMapping.setRemitSociety(distribution.getCodeOfRemittingSociety()==null?listOverseasFileBase.getRemitSocietyCode():distribution.getCodeOfRemittingSociety());
		listOverseasFileWorkMapping.setReceiptSociety(receiptSociety);
		listOverseasFileWorkMapping.setSourceWorkCode(workCode);
		listOverseasFileWorkMapping.setIswc(iswc);
		listOverseasFileWorkMapping.setIsrc("");
		listOverseasFileWorkMapping.setOriginalTitle(distribution.getOriginalTitle());
		listOverseasFileWorkMapping.setSubTitle(distribution.getSubtitle());
		listOverseasFileWorkMapping.setComposerName(distribution.getNameOfComposer());
//		listOverseasFileWorkMapping.setAuthorName(distribution.getNameOfComposer());
		listOverseasFileWorkMapping.setCreateTime(new Date());
		listOverseasFileWorkMapping.setAuthorComposer(distribution.getNameOfComposer());
		listOverseasFileWorkMapping.setAmendTime(new Date());
		listOverseasFileWorkMapping.setStatus(0);
		Integer distributionCategory = distribution.getDistributionCategory();
		listOverseasFileWorkMapping.setDistributionCategory(distributionCategory);
		listOverseasFileWorkMapping.setPoolCode(DistributionCategoryMenu.getValueByCode(distributionCategory));
		listOverseasFileWorkMapping.setRecordType(recordType);
		//source_work_code+iswc+isrc+receipt_society+reference_number
		String dataUniqueKeyStr = listOverseasFileWorkMapping.getRemitSociety() + "_" + workCode+"_"+ distribution.getOriginalTitle();
		listOverseasFileWorkMapping.setDataUniqueKey(Md5.getMd5(dataUniqueKeyStr));
		listOverseasFileWorkMapping.setDataUniqueKeyStr(dataUniqueKeyStr);
//		listOverseasFileWorkMapping.setRemitDistNo(distribution.getDistributionIdentificationCode());
		listOverseasFileWorkMapping.setReferenceNumber(referenceNumber+"");
		listOverseasFileWorkMapping.setProgram(distribution.getNameOfTheBroadcastChannel());
		listOverseasFileWorkMapping.setIpSocietyCode(distribution.getSocietyCodeOfRightOwner());
		listOverseasFileWorkMapping.setIpName(distribution.getNameOfRightOwner());
		listOverseasFileWorkMapping.setComposerName(distribution.getNameOfComposer());
		listOverseasFileWorkMapping.setIpNameNo(distribution.getIpiNameNumber());
		listOverseasFileWorkMapping.setWorkIpRole(distribution.getCategoryOfRightOwner());
		BigDecimal shareNumerator = distribution.getShareNumerator() == null ? BigDecimal.ZERO : BigDecimal.valueOf(distribution.getShareNumerator());
		BigDecimal shareDenominator = distribution.getShareDenominator() == null ? BigDecimal.ZERO : BigDecimal.valueOf(distribution.getShareDenominator());
		BigDecimal shareRatio = BigDecimal.ZERO;
//		BigDecimal big_100 = new BigDecimal(100);
		if(shareNumerator.compareTo(BigDecimal.ZERO) > 0 && shareDenominator.compareTo(BigDecimal.ZERO) > 0){
//			shareDenominator = shareDenominator.divide(big_100,6,RoundingMode.HALF_UP);
			shareRatio = shareNumerator.divide(new BigDecimal(100),6,RoundingMode.HALF_UP);
		}
		listOverseasFileWorkMapping.setShareRatio(shareRatio);
		listOverseasFileWorkMapping.setShareDenominator(shareDenominator);
		listOverseasFileWorkMapping.setAmount(new BigDecimal(distribution.getAmount()/pointNumber));
		listOverseasFileWorkMapping.setTerritoryCode(distribution.getCodeOfTerritoryOfOriginPerformance()+"");
		listOverseasFileWorkMapping.setTerritoryName(distribution. getTerritoryNameOfOriginPerformance());
		listOverseasFileWorkMapping.setAdjustmentNumber(distribution.getAdjustmentNumber());
		listOverseasFileWorkMapping.setAdjustmentResaonCode(distribution.getAdjustmentReasonCode());
		listOverseasFileWorkMapping.setNumberOfPerformances(distribution.getNumberOfPerformances());
		listOverseasFileWorkMapping.setAdjustmentOriginalDistributionIdentificationCode(distribution.getAdjustmentOriginalDistributionIdentificationCode());
		listOverseasFileWorkMapping.setAdjustmentCommentary(distribution.getAdjustmentCommentary());
		listOverseasFileWorkMapping.setExtJson(distribution.getSparePositionsForExpansion());
		listOverseasFileWorkMapping.setSignIndicator(distribution.getSignIndicator());
		
		// F2新增
		listOverseasFileWorkMapping.setPeriodOfAccount(distribution.getPeriodOfAccount());
		listOverseasFileWorkMapping.setIsan(distribution.getIsan());
		listOverseasFileWorkMapping.setAudiovisualWorkCode(distribution.getAudiovisualWorkCode());
		listOverseasFileWorkMapping.setIsan(distribution.getIsan());
		listOverseasFileWorkMapping.setAudiovisualWorkType(distribution.getAudiovisualWorkType());
		listOverseasFileWorkMapping.setAudiovisualWorkCategory(distribution.getAudiovisualWorkCategory());
		listOverseasFileWorkMapping.setNameOfProducer(distribution.getNameOfProducer());
		listOverseasFileWorkMapping.setYearOfProductionOrRelease(distribution.getYearOfProductionOrRelease());
		listOverseasFileWorkMapping.setNameOfDirector(distribution.getNameOfDirector());
		listOverseasFileWorkMapping.setLeadingActor(distribution.getLeadingActor());
		listOverseasFileWorkMapping.setCountryOfOriginOfTheAudiovisualWork(distribution.getCountryOfOriginOfTheAudiovisualWork());
		listOverseasFileWorkMapping.setOriginalTitleOfTheAudiovisualWork(distribution.getOriginalTitleOfTheAudiovisualWork());
		listOverseasFileWorkMapping.setLocalTitleOfTheAudiovisualWork(distribution.getLocalTitleOfTheAudiovisualWork());
		listOverseasFileWorkMapping.setOriginalTitleOfTheEpisode(distribution.getOriginalTitleOfTheEpisode());
		listOverseasFileWorkMapping.setLocalTitleOfTheEpisode(distribution.getLocalTitleOfTheEpisode());
		listOverseasFileWorkMapping.setProducerEpisodeNumber(distribution.getProducerEpisodeNumber());
		listOverseasFileWorkMapping.setSocietyEpisodeNumber(distribution.getSocietyEpisodeNumber());
		listOverseasFileWorkMapping.setTotalDurationOfTheAudiovisualWork(distribution.getTotalDurationOfTheAudiovisualWork());
		listOverseasFileWorkMapping.setTotalDurationOfTheProtectedMusic(distribution.getTotalDurationOfTheProtectedMusic());
		listOverseasFileWorkMapping.setTotalDurationOfTheMusic(distribution.getTotalDurationOfTheMusic());
		listOverseasFileWorkMapping.setOriginOfWorkOrCue(distribution.getOriginOfWorkOrCue());
		listOverseasFileWorkMapping.setSequenceNumberOfWorkOrCue(distribution.getSequenceNumberOfWorkOrCue());
		listOverseasFileWorkMapping.setDurationOfWorkOrCue(distribution.getDurationOfWorkOrCue());
		listOverseasFileWorkMapping.setPerReceivedByRemittingSocForFeesPaidInError(distribution.getPercentageReceivedByTheRemittingSocietyForFeesPaidInError());
		listOverseasFileWorkMapping.setReceiptDetailsId(listOverseasFileBase.getReceiptDetailsId());
		if(Constants.WORK_IP_ROLE_CRD_CA.contains(distribution.getCategoryOfRightOwner())){

			if(StringUtils.isNotBlank(distribution.getNameOfRightOwner())){
				List<String> authorComposerList = authorComposerMap.get(workCode);
				if(authorComposerList == null ){
					authorComposerList = new ArrayList<>();
					authorComposerList.add(distribution.getNameOfRightOwner());
					authorComposerMap.put(workCode,authorComposerList);
				} else if(!authorComposerList.contains(distribution.getNameOfRightOwner())){
					authorComposerList.add(distribution.getNameOfRightOwner());
				}
			}

			if(StringUtils.isNotBlank(distribution.getIpiNameNumber()) && !StringUtils.equals(distribution.getIpiNameNumber(),"00000000000")){
				List<String> authorComposerIpList = authorComposerIpMap.get(workCode);
				if(authorComposerIpList == null ){
					authorComposerIpList = new ArrayList<>();
					authorComposerIpList.add(distribution.getIpiNameNumber());
					authorComposerIpMap.put(workCode,authorComposerIpList);
				} else if(!authorComposerIpList.contains(distribution.getIpiNameNumber())){
					authorComposerIpList.add(distribution.getIpiNameNumber());
				}
			}
		}
		return listOverseasFileWorkMapping;
	}
	
	/**
	 * 20	royalties deriving from radio
	 * 21	royalties deriving from television
     * 50	royalties deriving from live Performances  现场表演  c
     * 60	royalties deriving from discotheques   舞厅   k
     * 70	royalties deriving from music by electronical or mechanical means  电子或机械音乐  i
     * 30	royalties deriving from private copying  私人copy   d
     * 31	royalties deriving from rental and lending  租赁  f
     * 40	royalties deriving from on-demand services 按需分配 g
     * 90	other royalties to be specified in the distribution summary form 0
           * 对应ref_pool_mstr
	 */
	private String createPoolCode(Integer distributionCategory) {
		String poolCode = "";
		if(20 == distributionCategory)
			poolCode = "R";
		else if(21 == distributionCategory)
			poolCode = "T";
		else if(50 == distributionCategory)
			poolCode = "C";
		else if(60 == distributionCategory)
			poolCode = "K";
		else if(70 == distributionCategory)
			poolCode = "I";
		else if(30 == distributionCategory)
			poolCode = "D";
		else if(31 == distributionCategory)
			poolCode = "F";
		else if(40 == distributionCategory)
			poolCode = "G";
		else if(90 == distributionCategory)
			poolCode = "O";
		return poolCode;
	}

	private ListOverseasFileBase updateListOverseasFileBaseByTrailer(ListOverseasFileBase listOverseasFileBase,
			F2Trailer f2Trailer) {
//		listOverseasFileBase.setAdjAmount(e4Trailer.getNumberOfAdjustmentRoyaltyDistributionRecords());
//		e4Trailer.getNumberOfDistributionRecordsForFeesPaidInErrorBySisterSocieties();
//		e4Trailer.getNumberOfNormalRoyaltyDistributionRecords();
		listOverseasFileBase.setFileDetailsNumber(f2Trailer.getNumberOfRecords());
		String signIndicator = f2Trailer.getSignIndicator();
		BigDecimal fileTotalAmount ;
		Long controlTotal = f2Trailer.getControlTotal();
		Integer pointSize = listOverseasFileBase.getAmountPointSize();
		BigDecimal pointNumber = BigDecimal.TEN.pow(pointSize);
		if("D".equalsIgnoreCase(signIndicator)) {
			fileTotalAmount = new BigDecimal(controlTotal).divide(pointNumber,pointSize,RoundingMode.HALF_UP).multiply(new BigDecimal(-1));
		}else {
			fileTotalAmount = new BigDecimal(controlTotal).divide(pointNumber,pointSize,RoundingMode.HALF_UP);
		}
		listOverseasFileBase.setFileTotalAmount(fileTotalAmount);
		StringBuffer sb = new StringBuffer(listOverseasFileBase.getExtJson());
		sb.append(f2Trailer.getSparePositions());
		listOverseasFileBase.setExtJson(sb.toString());
		return listOverseasFileBase;
	}

	private ListOverseasFileBase updateListOverseasFileBaseByHeader(String filePath, String fileName, ListOverseasFileBase listOverseasFileBase, F2Header f2Header) {
		listOverseasFileBase.setCurrencyCode(f2Header.getCurrencyCode());
		if(null != f2Header.getCodeOfRemittingSociety())
			listOverseasFileBase.setRemitSocietyCode(f2Header.getCodeOfRemittingSociety());
//		listOverseasFileBase.setReceiptSocietyCode(f2Header.getCodeOfReceivingSociety());
		listOverseasFileBase.setOverseasDistNo(f2Header.getDistributionsIdentificationCodes());
		listOverseasFileBase.setFileType(f2Header.getFormatTypeUsed());
		listOverseasFileBase.setAmountPointSize(f2Header.getNumberOfDecimalPlacesInAmountFields());
		listOverseasFileBase.setExtJson(f2Header.getSparePositions());
		listOverseasFileBase.setFilePath(filePath);
//		listOverseasFileBase.setFileName(fileName);
		listOverseasFileBase.setFileMd5(Md5.getMd5(filePath));
		return listOverseasFileBase;
	}

}
