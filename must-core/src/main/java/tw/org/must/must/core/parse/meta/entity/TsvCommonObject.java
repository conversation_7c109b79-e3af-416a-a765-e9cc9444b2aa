package tw.org.must.must.core.parse.meta.entity;

public class TsvCommonObject {

    private String usage_start_date ;
    private String usage_end_date ;
    private String territory ;
    private String product ;
    private String event_count ;
    private String elected_isrc ;
    private String artist ;
    private String title ;
    private String isrcs ;
    private String iswcs ;
    private String sales_transaction_id ;
    private String custom_ids;
    private String mechanical_licensed_collection_shares;
    private String performance_licensed_collection_shares;
    private String songwriters;
    private String net_revenue;
    private String currency;

    public String getUsage_start_date() {
        return usage_start_date;
    }

    public void setUsage_start_date(String usage_start_date) {
        this.usage_start_date = usage_start_date;
    }

    public String getUsage_end_date() {
        return usage_end_date;
    }

    public void setUsage_end_date(String usage_end_date) {
        this.usage_end_date = usage_end_date;
    }

    public String getTerritory() {
        return territory;
    }

    public void setTerritory(String territory) {
        this.territory = territory;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getEvent_count() {
        return event_count;
    }

    public void setEvent_count(String event_count) {
        this.event_count = event_count;
    }

    public String getElected_isrc() {
        return elected_isrc;
    }

    public void setElected_isrc(String elected_isrc) {
        this.elected_isrc = elected_isrc;
    }

    public String getArtist() {
        return artist;
    }

    public void setArtist(String artist) {
        this.artist = artist;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIsrcs() {
        return isrcs;
    }

    public void setIsrcs(String isrcs) {
        this.isrcs = isrcs;
    }

    public String getIswcs() {
        return iswcs;
    }

    public void setIswcs(String iswcs) {
        this.iswcs = iswcs;
    }

    public String getSales_transaction_id() {
        return sales_transaction_id;
    }

    public void setSales_transaction_id(String sales_transaction_id) {
        this.sales_transaction_id = sales_transaction_id;
    }

    public String getCustom_ids() {
        return custom_ids;
    }

    public void setCustom_ids(String custom_ids) {
        this.custom_ids = custom_ids;
    }

    public String getMechanical_licensed_collection_shares() {
        return mechanical_licensed_collection_shares;
    }

    public void setMechanical_licensed_collection_shares(String mechanical_licensed_collection_shares) {
        this.mechanical_licensed_collection_shares = mechanical_licensed_collection_shares;
    }

    public String getPerformance_licensed_collection_shares() {
        return performance_licensed_collection_shares;
    }

    public void setPerformance_licensed_collection_shares(String performance_licensed_collection_shares) {
        this.performance_licensed_collection_shares = performance_licensed_collection_shares;
    }

    public String getSongwriters() {
        return songwriters;
    }

    public void setSongwriters(String songwriters) {
        this.songwriters = songwriters;
    }

    public String getNet_revenue() {
        return net_revenue;
    }

    public void setNet_revenue(String net_revenue) {
        this.net_revenue = net_revenue;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
