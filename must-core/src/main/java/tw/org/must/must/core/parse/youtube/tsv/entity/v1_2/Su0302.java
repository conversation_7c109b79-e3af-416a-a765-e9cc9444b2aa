package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

import tw.org.must.must.core.parse.youtube.tsv.entity.v1_3.LI0103;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_3.MW0102;

import java.util.List;

public class Su0302 extends BaseTsvEntity{


	
	private String BlockId;
	
	private String SalesTransactionId;
	
	private String SummaryRecordId;
	
	private String DspReleaseId;

	private String DspResourceId;
	
	private String Usages;
	
	private String NetRevenue;
	
	private String ValidityPeriodStart;
	
	private String ValidityPeriodEnd;
	
	private String ContentCategory;
	
	private String IsRoyaltyBearing;



	private List<LI0102> li0102List;

	private List<LI0103> li0103List;

	private List<Mw0101> mw0101List;
	private List<MW0102> mw0102List;


	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getSalesTransactionId() {
		return SalesTransactionId;
	}

	public void setSalesTransactionId(String salesTransactionId) {
		SalesTransactionId = salesTransactionId;
	}

	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getDspReleaseId() {
		return DspReleaseId;
	}

	public void setDspReleaseId(String dspReleaseId) {
		DspReleaseId = dspReleaseId;
	}

	public String getUsages() {
		return Usages;
	}

	public void setUsages(String usages) {
		Usages = usages;
	}

	public String getNetRevenue() {
		return NetRevenue;
	}

	public void setNetRevenue(String netRevenue) {
		NetRevenue = netRevenue;
	}

	public String getValidityPeriodStart() {
		return ValidityPeriodStart;
	}

	public void setValidityPeriodStart(String validityPeriodStart) {
		ValidityPeriodStart = validityPeriodStart;
	}

	public String getValidityPeriodEnd() {
		return ValidityPeriodEnd;
	}

	public void setValidityPeriodEnd(String validityPeriodEnd) {
		ValidityPeriodEnd = validityPeriodEnd;
	}

	public String getContentCategory() {
		return ContentCategory;
	}

	public void setContentCategory(String contentCategory) {
		ContentCategory = contentCategory;
	}

	public String getIsRoyaltyBearing() {
		return IsRoyaltyBearing;
	}

	public void setIsRoyaltyBearing(String isRoyaltyBearing) {
		IsRoyaltyBearing = isRoyaltyBearing;
	}

	public List<LI0102> getLi0102List() {
		return li0102List;
	}

	public void setLi0102List(List<LI0102> li0102List) {
		this.li0102List = li0102List;
	}

	public List<Mw0101> getMw0101List() {
		return mw0101List;
	}

	public void setMw0101List(List<Mw0101> mw0101List) {
		this.mw0101List = mw0101List;
	}

	public List<LI0103> getLi0103List() {
		return li0103List;
	}

	public void setLi0103List(List<LI0103> li0103List) {
		this.li0103List = li0103List;
	}

	public List<MW0102> getMw0102List() {
		return mw0102List;
	}

	public void setMw0102List(List<MW0102> mw0102List) {
		this.mw0102List = mw0102List;
	}

	public String getDspResourceId() {
		return DspResourceId;
	}

	public void setDspResourceId(String dspResourceId) {
		DspResourceId = dspResourceId;
	}
}
