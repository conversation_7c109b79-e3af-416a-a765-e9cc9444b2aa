package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;

@Data
public class AS0102 extends BaseTsvEntity {
    private String BlockId;
    private String ResourceReference;
    private String DspResourceId;
    private String ResourceISRC;
    private String ResourceTitle;
    private String ResourceSubTitle;
    private String ResourceDisplayArtistName;
    private String ResourceDisplayArtistPartyId;
    private String ResourceDuration;


    private String durationM;

    private String durationS;
    private String ResourceType;
    private String IsMasterRecording;
    private String IsSubjectToOwnershipConflict;
    private String LastConflictCheck;
 }
