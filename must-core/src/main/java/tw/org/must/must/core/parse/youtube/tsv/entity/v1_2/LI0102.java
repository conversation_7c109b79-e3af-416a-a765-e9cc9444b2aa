package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class LI0102 extends BaseTsvEntity {

	private String name;
	
	private String BlockId;
	
	
	private String SummaryRecordId;
	
	private String RightsController;

	private String RightsControllerPartyId;
	
	private String RightsControllerWorkID;

	private String RightShare;
	
	private String RightType;

	private String AllocatedNetRevenue;
	
	private String AllocatedAmount;

	private String AllocatedUsages;

	private String uniqueKey;



	public String getBlockId() {
		return BlockId;
	}

	public void setBlockId(String blockId) {
		BlockId = blockId;
	}

	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getRightsController() {
		return RightsController;
	}

	public void setRightsController(String rightsController) {
		RightsController = rightsController;
	}

	public String getRightsControllerPartyId() {
		return RightsControllerPartyId;
	}

	public void setRightsControllerPartyId(String rightsControllerPartyId) {
		RightsControllerPartyId = rightsControllerPartyId;
	}

	public String getRightsControllerWorkID() {
		return RightsControllerWorkID;
	}

	public void setRightsControllerWorkID(String rightsControllerWorkID) {
		RightsControllerWorkID = rightsControllerWorkID;
	}

	public String getRightShare() {
		return RightShare;
	}

	public void setRightShare(String rightShare) {
		RightShare = rightShare;
	}

	public String getRightType() {
		return RightType;
	}

	public void setRightType(String rightType) {
		RightType = rightType;
	}

	public String getAllocatedNetRevenue() {
		return AllocatedNetRevenue;
	}

	public void setAllocatedNetRevenue(String allocatedNetRevenue) {
		AllocatedNetRevenue = allocatedNetRevenue;
	}

	public String getAllocatedAmount() {
		return AllocatedAmount;
	}

	public void setAllocatedAmount(String allocatedAmount) {
		AllocatedAmount = allocatedAmount;
	}

	public String getAllocatedUsages() {
		return AllocatedUsages;
	}

	public void setAllocatedUsages(String allocatedUsages) {
		AllocatedUsages = allocatedUsages;
	}

	public String getUniqueKey() {
		return uniqueKey;
	}

	public void setUniqueKey(String uniqueKey) {
		this.uniqueKey = uniqueKey;
	}
}
