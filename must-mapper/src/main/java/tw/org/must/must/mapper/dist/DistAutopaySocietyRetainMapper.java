package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistAutopaySocRetain;
import tw.org.must.must.model.dist.DistAutopaySocietyRetain;

import java.util.List;

@Repository
public interface DistAutopaySocietyRetainMapper extends BaseMapper<DistAutopaySocietyRetain> {
    @Select("SELECT dasr.*,dasr.is_pay as pay FROM dist_autopay_society_retain dasr where society_code IN (SELECT society_code FROM dist_autopay_society_retain  WHERE  is_pay = 'Y' GROUP BY society_code having SUM(pay_amount) > 2000)")
    List<DistAutopaySocietyRetain> selectAutopayForSociety();

    Integer updateIsPayByIds(@Param("isPay") String isPay, @Param("list") List<Long> ids);
}