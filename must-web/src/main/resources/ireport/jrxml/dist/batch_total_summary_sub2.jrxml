<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.16.0.final using JasperReports Library version 6.16.0-48579d909b7943b64690c65c71e07e0b80981928  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="batch_total_summary_sub2" pageWidth="837" pageHeight="593" orientation="Landscape" columnWidth="837" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fb506072-2bad-45c1-964e-a63c3177c298">
	<property name="com.jaspersoft.studio.unit." value="cm"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/dist/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="no" class="java.lang.String"/>
	<field name="cur" class="java.lang.String"/>
	<field name="soc" class="java.lang.String"/>
	<field name="socName" class="java.lang.String"/>
	<field name="mbr" class="java.lang.String"/>
	<field name="commRate" class="java.lang.String"/>
	<field name="taxRate" class="java.lang.String"/>
	<field name="royalties" class="java.lang.String"/>
	<field name="commission" class="java.lang.String"/>
	<field name="taxable" class="java.lang.String"/>
	<field name="tax" class="java.lang.String"/>
	<field name="netTotal" class="java.lang.String"/>
	<field name="sub3" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="59" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="StretchHeight">
				<reportElement x="11" y="1" width="28" height="11" uuid="75bd000d-8f57-483b-8a27-b6e07f026d48">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{no}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="39" y="1" width="28" height="11" uuid="599cf31a-a9de-48c7-9d13-bb9e73d28ed6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cur}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="80" y="1" width="56" height="11" uuid="122588d9-0912-48ba-b579-2acf3e4e55f0">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soc}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="147" y="1" width="56" height="11" uuid="7d3d2999-1fe8-41fa-8c35-275ca47c0a8e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{socName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="324" y="1" width="19" height="11" uuid="7ed26f74-b8cc-40b3-a480-2d434d1971a8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mbr}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="347" y="1" width="36" height="11" uuid="d4de5958-5d55-4ebd-8d2e-b36f500827a8">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{commRate}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="389" y="1" width="36" height="11" uuid="ed4583a4-a0ed-4a13-afda-4a14ed7138d9">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{taxRate}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="455" y="1" width="36" height="11" uuid="0dce7706-b6ee-4c9f-bf30-872fb0c0112b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{royalties}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="512" y="1" width="36" height="11" uuid="a04b07d1-48c4-41cf-90e0-89a92317f97f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{commission}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="579" y="1" width="36" height="11" uuid="6e053cde-a378-456b-a15b-cf4c59520370">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{taxable}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="642" y="1" width="36" height="11" uuid="a3ecfcf7-6c0e-4376-94ec-36d6d0a72f7d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tax}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="707" y="1" width="36" height="11" uuid="b3f2df22-9c8e-4a6f-9856-99f626a48c18">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{netTotal}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="0" y="24" width="821" height="31" uuid="9e480389-aeb1-4eb0-9dab-3643037c6629">
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("sub3")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "batch_total_summary_sub3.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
