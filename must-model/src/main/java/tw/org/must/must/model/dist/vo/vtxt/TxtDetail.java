package tw.org.must.must.model.dist.vo.vtxt;

import java.math.BigDecimal;

public class TxtDetail {

    private String bankBrNo ;//收款行庫代號: 輸入收款單位之 總行代號(3位) + 分行代號(3位)

    private BigDecimal payAmount ;//匯款金額    : 輸入貨款明細金額，不得小於零，或大於兩千萬 长度：13

    private BigDecimal payFee; //單筆手續費 长度：5

    private String receiverAccount; //收款人帳號: 收款人帳號若不足14位，則右靠左補零

    private String receiverName; //收款人名稱 长度：30/50

    private String note; //附言 可輸入20個全形字(不可輸入半形字)，左靠右補半形空白  长度：40

    private String remark;//備註:可輸入9位特定用途之數字資料，若不足位則前面補零

    public String getBankBrNo() {
        return bankBrNo;
    }

    public void setBankBrNo(String bankBrNo) {
        this.bankBrNo = bankBrNo;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public BigDecimal getPayFee() {
        return payFee;
    }

    public void setPayFee(BigDecimal payFee) {
        this.payFee = payFee;
    }

    public String getReceiverAccount() {
        return receiverAccount;
    }

    public void setReceiverAccount(String receiverAccount) {
        this.receiverAccount = receiverAccount;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
