package tw.org.must.must.model.dist.vo.v800;

import lombok.Data;
import tw.org.must.must.common.util.LocalCommonMethodUtils;

import java.math.BigDecimal;

/**
 * @Author: hanDa
 * @Date: 2021/5/28 14:25
 * @Version:1.0
 * @Description:
 */
@Data
public class DistNoDetail800 {
    private String societyName;
    private String distNo;
    private String payAbleTo;
    private String royalties;
    private String payAbleAmount;
    private String taxRate;
    private String admissionRate;
    private String reciprocalRate;
    private String taxAmount;
    private String admissionAmount;
    private String reciprocalAmount;

    public BigDecimal getRoyaltiesA() {
        return royalties == null ? BigDecimal.ZERO : new BigDecimal(royalties);
    }

    public BigDecimal getPayA(){
        return payAbleAmount == null ? BigDecimal.ZERO : new BigDecimal(payAbleAmount);
    }
    public BigDecimal getTaxA(){
        return taxAmount == null ? BigDecimal.ZERO : new BigDecimal(taxAmount);
    }

    public BigDecimal getCommissionA(){
        return admissionAmount == null ? BigDecimal.ZERO : new BigDecimal(admissionAmount);
    }
    public BigDecimal getReciprocalA(){
        return reciprocalAmount == null ? BigDecimal.ZERO : new BigDecimal(reciprocalAmount);
    }

    public String getRoyalties() {
        return LocalCommonMethodUtils.formateNoScale(royalties);
    }

    public String getPayAbleAmount() {
        return LocalCommonMethodUtils.formateNoScale(payAbleAmount);
    }

    public String getTaxAmount() {
        return LocalCommonMethodUtils.formateNoScale(taxAmount);
    }

    public String getAdmissionAmount() {
        return LocalCommonMethodUtils.formateNoScale(admissionAmount);
    }

    public String getReciprocalAmount() {
        return LocalCommonMethodUtils.formateNoScale(reciprocalAmount);
    }

    public String getORoyalties() {
        return royalties;
    }

    public String getOPayAbleAmount() {
        return payAbleAmount;
    }

    public String getOTaxAmount() {
        return taxAmount;
    }

    public String getOAdmissionAmount() {
        return admissionAmount;
    }

    public String getOReciprocalAmount() {
        return reciprocalAmount;
    }
}