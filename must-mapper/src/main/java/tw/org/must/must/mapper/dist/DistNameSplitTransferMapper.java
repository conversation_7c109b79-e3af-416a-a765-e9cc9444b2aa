package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistNameSplitTransfer;

import java.util.List;

@Repository
public interface DistNameSplitTransferMapper extends BaseMapper<DistNameSplitTransfer> {


    @Select("select * from dist_name_split_transfer where pa_name_no = #{paNameNo}")
    List<DistNameSplitTransfer> getByPaNameNo(String paNameNo);
}