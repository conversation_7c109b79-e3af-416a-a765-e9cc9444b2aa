package tw.org.must.must.model.wrk.vo;

import lombok.Data;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.util.Date;

@Data
public class WrkWorkTitleVO {

    private Long id;
    private String title;
    private String titleEn;
    private String languageCode;
    private String episodeNo;
    private String titleType;

    private String genreCode;
    private Long workId;
    private Integer workSocietyCode;

    private String workUniqueKey;

    private Long subTitleId;
    private Integer durationM;
	private Integer durationS;
	private String generDtlFlag;
	private String transfer;
	private Integer inputSoc;

    private Date createTime;
    private Date amendTime;

    // work
    private String iswc;
    private String workType;
    private String lyrics;
    private String isLocal;

	private WrkWork wrkWork;

    public WrkWorkTitleVO() {
    }

    public WrkWorkTitleVO(Long id, String title, String titleEn, String languageCode, String episodeNo, String titleType, String genreCode, Long workId, Integer workSocietyCode, String workUniqueKey, Long subTitleId, Integer durationM, Integer durationS, String generDtlFlag, String transfer, Integer inputSoc, Date createTime, Date amendTime, String iswc, String workType, String lyrics, String isLocal) {
        this.id = id;
        this.title = title;
        this.titleEn = titleEn;
        this.languageCode = languageCode;
        this.episodeNo = episodeNo;
        this.titleType = titleType;
        this.genreCode = genreCode;
        this.workId = workId;
        this.workSocietyCode = workSocietyCode;
        this.workUniqueKey = workUniqueKey;
        this.subTitleId = subTitleId;
        this.durationM = durationM;
        this.durationS = durationS;
        this.generDtlFlag = generDtlFlag;
        this.transfer = transfer;
        this.inputSoc = inputSoc;
        this.createTime = createTime;
        this.amendTime = amendTime;
        this.iswc = iswc;
        this.workType = workType;
        this.lyrics = lyrics;
        this.isLocal = isLocal;
    }

	public WrkWorkTitleVO(WrkWorkTitle wrkWorkTitle,WrkWork wrkWork) {
		this.id = wrkWorkTitle.getId();
		this.title = wrkWorkTitle.getTitle();
		this.titleEn = wrkWorkTitle.getTitleEn();
		this.languageCode = wrkWorkTitle.getLanguageCode();
		this.episodeNo = wrkWorkTitle.getEpisodeNo();
		this.titleType = wrkWorkTitle.getTitleType();
		this.genreCode = wrkWorkTitle.getGenreCode();
		this.workId = wrkWorkTitle.getWorkId();
		this.workSocietyCode = wrkWorkTitle.getWorkSocietyCode();
		this.workUniqueKey = wrkWorkTitle.getWorkUniqueKey();
		this.subTitleId = wrkWorkTitle.getSubTitleId();
		this.durationM = wrkWorkTitle.getDurationM();
		this.durationS = wrkWorkTitle.getDurationS();
		this.generDtlFlag = wrkWorkTitle.getGenerDtlFlag();
		this.transfer = wrkWorkTitle.getTransfer();
		this.inputSoc = wrkWorkTitle.getInputSoc();
		this.createTime = wrkWorkTitle.getCreateTime();
		this.amendTime = wrkWorkTitle.getAmendTime();
		if (wrkWork !=null){
			this.wrkWork = wrkWork;
			this.amendTime = wrkWork.getAmendTime();
			this.iswc = wrkWork.getISWC();
			this.workType = wrkWork.getWorkType();
			this.lyrics = wrkWork.getLyrics();
			this.isLocal = wrkWork.getLocal();
		}

	}

	
	
}