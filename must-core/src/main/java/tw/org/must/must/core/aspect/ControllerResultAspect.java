package tw.org.must.must.core.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import tw.org.must.must.core.exception.CustomException;

import java.util.Collection;
import java.util.Map;

/**
 * 查询结果空值提示
 *
 */
@Aspect
@Component
public class ControllerResultAspect {

    @Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping)")
    public void pointcut() {

    }

    /*@Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        ResponseEntity<?> result = (ResponseEntity<?>) point.proceed();
        Object data = result.getBody();
        if(data == null){
            throw new CustomException();
        }
        if(data instanceof Collection){
            Collection<?> dataCollection = (Collection<?>) data;
            if(dataCollection.isEmpty()){
                throw new CustomException();
            }
        }
        if (data instanceof Map){
            Map<?,?> dataMap = (Map<?,?>) data;
            if(dataMap.isEmpty()){
                throw new CustomException();
            }
        }
        return result;
    }*/
}
