package com.firstbrave.api.ccidv13.vo;

import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.base.ApiContentType;

public class InvoiceDetailRecordV13 extends CCIDRecordPrefix {

    /**
     * claim的type ORI,ADJ,COR etc.
     */
    @ApiContentType(index=2,length = 3)
	private String transactionType;
    
    /**
     * MUST發送過去的號碼 Uniqueness across all CCID files generated by a sender.
     */
    @ApiContentType(index=3,length = 20)
    private String refId;
 
    /**
     * 
     */
    @ApiContentType(index=4,length = 20)
    private String correctionReference;
    
    /**
     *	 銷售單ID(打包專輯用)
     */
    @ApiContentType(index=5,length = 60)
    private String salesTransactionId;
    
    /**
     *	 從哪個DDEX來源檔來的
     */
    @ApiContentType(index=6,length = 18)
    private String tradingBrand;
       
    /**
     *	 發布ID，對應到DDEX的ProprietaryId
     */
    @ApiContentType(index=7,length = 60)
    private String releaseId;
    
    /**
     *	 產品ID，對應到DDEX的ProprietaryId，跟releaseID一樣?
     */
    @ApiContentType(index=8,length = 60)
    private String resourceId;
    
    /**
     * 	用DIVA產出的CCID檔案來看，是抓DDEX裡面的ISRC欄位
     */
    @ApiContentType(index=9,length = 12)
    private String isrc;

    @ApiContentType(index=10,length = 11)
    private String iswc;

    /**
     * 	第一個字母表示協會標示(C:CASH,U:MUST)，後面是帶worknum	EX:"C-1239645922","U-1030060602"
     */
    @ApiContentType(index=11,length = 39)
    private String workcode;
    
    @ApiContentType(index=12,length = 60)
    private String workTitle;
    
    /**
     * LSS:Limited subscriotion service
     */
    @ApiContentType(index=13,length = 10)
    private String serviceType;
    
    @ApiContentType(index=14,length = 10)
    private String useType;
    
    /**
     * 使用次數
     */
    @ApiContentType(index=15,length = 8)
    private String useQuantity;
    
    /**
     * 符合的費率
     */
    @ApiContentType(index=16,length = 10)
    private String appliedTariff;
    
    /**
     * Calculation of royalty: 
		M = Minimum 
		P = Percentage 
		O = Other 
     */
    @ApiContentType(index=17,length = 2)
    private String royaltyType;
    
    
    /**
     * 第19項單價幣別換算(都是台幣的話跟19項是一樣的)
     */
    @ApiContentType(index=18,length = 7)
    private String priceBasis;
    
    /**
     * 原始單價:取其高後，除以下載(播放)總數得出的值
     */
    @ApiContentType(index=19,length = 7)
    private String originalPriceBasis;
       
    /**
     * ROYALTY: Royalty value of the work for a single usage
     */
    @ApiContentType(index=20,length = 9)
    private String royalty;
    
    /**
     * 	這個音樂在這一整包裡面所佔的比例(如果是100%就是填10000)
     */
    @ApiContentType(index=21,length = 5)
    private String musicShare;
    
    /**
     *	 非必填，如果填了NR代表限制不能使用成鈴聲。
     */
    @ApiContentType(index=22,length = 10)
    private String restrictions;
    
    /**
     *	 是否能授權(A = accepted P = pending R = rejected )
     */
    @ApiContentType(index=23,length = 1)
    private String fstLicense;
    
    /**
     * claim share*重製佔比75%+claim share*公傳佔比25%
     */
    @ApiContentType(index=24,length = 5)
    private String claimLicensorCombined;
    
    @ApiContentType(index=25,length = 5)
    private String claimPaiCombined;
    
    @ApiContentType(index=26,length = 5)
    private String claimUnmatchedCombined;
    
    @ApiContentType(index=27,length = 5)
    private String claimPdCombined;
    
    /**
     * unclaim share*重製佔比75%+unclaim share*公傳佔比25%
     */
    @ApiContentType(index=28,length = 5)
    private String claimNotCollectedCombined;
        
    /**
     * 
     */
    @ApiContentType(index=29,length = 13)
    private String amountInvoicedTotal;
    
    /**
     * claim share(MEC)
     */
    @ApiContentType(index=30,length = 5)
    private String claimLicensorMech;
    
    /**
     * claim share(PER)
     */
    @ApiContentType(index=31,length = 5)
    private String claimLicensorPerf;
    
    /**
     * 等於:useQuantity*單價(per usage)*claimLicensorCombined佔比
     */
    @ApiContentType(index=32,length = 13)
    private String amountLicensorMech;
    
    /**
     *    
     */
    @ApiContentType(index=33,length = 13)
    private String amountLicensorPerf;
    
    @ApiContentType(index=34,length = 13)
    private String amountPaiMech;
    
    @ApiContentType(index=35,length = 13)
    private String amountPaiPerf;
    
    @ApiContentType(index=36,length = 13)
    private String amountPdMech;
    
    @ApiContentType(index=37,length = 13)
    private String amountPdPerf;
    
    /**
     * 等於:useQuantity*單價(per usage)*claimNotCollectedCombined佔比的重製部分
     */
    @ApiContentType(index=38,length = 13)
    private String amountNotCollectedMech;
    
    @ApiContentType(index=39,length = 13)
    private String amountNotCollectedPerf;
    
    @ApiContentType(index=40,length = 13)
    private String amountUnmatchedMech;
    
    @ApiContentType(index=41,length = 13)
    private String amountUnmatchedPerf;
    
    @ApiContentType(index=42,length = 5)
    private String claimPaiMech;
    
    @ApiContentType(index=43,length = 5)
    private String claimPaiPerf;
    
    @ApiContentType(index=44,length = 5)
    private String claimPdMech;
    
    @ApiContentType(index=45,length = 5)
    private String claimPdPerf;
    
    @ApiContentType(index=46,length = 5)
    private String claimNotCollectedMech;
    
    @ApiContentType(index=47,length = 5)
    private String claimNotCollectedrPerf;
    
    @ApiContentType(index=48,length = 5)
    private String claimUnmatchedMech;
    
    @ApiContentType(index=49,length = 5)
    private String claimUnmatchedPerf;

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getRefId() {
		return refId;
	}

	public void setRefId(String refId) {
		this.refId = refId;
	}

	public String getCorrectionReference() {
		return correctionReference;
	}

	public void setCorrectionReference(String correctionReference) {
		this.correctionReference = correctionReference;
	}

	public String getSalesTransactionId() {
		return salesTransactionId;
	}

	public void setSalesTransactionId(String salesTransactionId) {
		this.salesTransactionId = salesTransactionId;
	}

	public String getTradingBrand() {
		return tradingBrand;
	}

	public void setTradingBrand(String tradingBrand) {
		this.tradingBrand = tradingBrand;
	}

	public String getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(String releaseId) {
		this.releaseId = releaseId;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId;
	}

	public String getIsrc() {
		return isrc;
	}

	public void setIsrc(String isrc) {
		this.isrc = isrc;
	}

	public String getIswc() {
		return iswc;
	}

	public void setIswc(String iswc) {
		this.iswc = iswc;
	}

	public String getWorkcode() {
		return workcode;
	}

	public void setWorkcode(String workcode) {
		this.workcode = workcode;
	}

	public String getWorkTitle() {
		return workTitle;
	}

	public void setWorkTitle(String workTitle) {
		this.workTitle = workTitle;
	}

	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	public String getUseType() {
		return useType;
	}

	public void setUseType(String useType) {
		this.useType = useType;
	}

	public String getUseQuantity() {
		return useQuantity;
	}

	public void setUseQuantity(String useQuantity) {
		this.useQuantity = useQuantity;
	}

	public String getAppliedTariff() {
		return appliedTariff;
	}

	public void setAppliedTariff(String appliedTariff) {
		this.appliedTariff = appliedTariff;
	}

	public String getRoyaltyType() {
		return royaltyType;
	}

	public void setRoyaltyType(String royaltyType) {
		this.royaltyType = royaltyType;
	}

	public String getPriceBasis() {
		return priceBasis;
	}

	public void setPriceBasis(String priceBasis) {
		this.priceBasis = priceBasis;
	}

	public String getOriginalPriceBasis() {
		return originalPriceBasis;
	}

	public void setOriginalPriceBasis(String originalPriceBasis) {
		this.originalPriceBasis = originalPriceBasis;
	}

	public String getRoyalty() {
		return royalty;
	}

	public void setRoyalty(String royalty) {
		this.royalty = royalty;
	}

	public String getMusicShare() {
		return musicShare;
	}

	public void setMusicShare(String musicShare) {
		this.musicShare = musicShare;
	}

	public String getRestrictions() {
		return restrictions;
	}

	public void setRestrictions(String restrictions) {
		this.restrictions = restrictions;
	}

	public String getFstLicense() {
		return fstLicense;
	}

	public void setFstLicense(String fstLicense) {
		this.fstLicense = fstLicense;
	}

	public String getClaimLicensorCombined() {
		return claimLicensorCombined;
	}

	public void setClaimLicensorCombined(String claimLicensorCombined) {
		this.claimLicensorCombined = claimLicensorCombined;
	}

	public String getClaimPaiCombined() {
		return claimPaiCombined;
	}

	public void setClaimPaiCombined(String claimPaiCombined) {
		this.claimPaiCombined = claimPaiCombined;
	}

	public String getClaimUnmatchedCombined() {
		return claimUnmatchedCombined;
	}

	public void setClaimUnmatchedCombined(String claimUnmatchedCombined) {
		this.claimUnmatchedCombined = claimUnmatchedCombined;
	}

	public String getClaimPdCombined() {
		return claimPdCombined;
	}

	public void setClaimPdCombined(String claimPdCombined) {
		this.claimPdCombined = claimPdCombined;
	}

	public String getClaimNotCollectedCombined() {
		return claimNotCollectedCombined;
	}

	public void setClaimNotCollectedCombined(String claimNotCollectedCombined) {
		this.claimNotCollectedCombined = claimNotCollectedCombined;
	}

	public String getAmountInvoicedTotal() {
		return amountInvoicedTotal;
	}

	public void setAmountInvoicedTotal(String amountInvoicedTotal) {
		this.amountInvoicedTotal = amountInvoicedTotal;
	}

	public String getClaimLicensorMech() {
		return claimLicensorMech;
	}

	public void setClaimLicensorMech(String claimLicensorMech) {
		this.claimLicensorMech = claimLicensorMech;
	}

	public String getClaimLicensorPerf() {
		return claimLicensorPerf;
	}

	public void setClaimLicensorPerf(String claimLicensorPerf) {
		this.claimLicensorPerf = claimLicensorPerf;
	}

	public String getAmountLicensorMech() {
		return amountLicensorMech;
	}

	public void setAmountLicensorMech(String amountLicensorMech) {
		this.amountLicensorMech = amountLicensorMech;
	}

	public String getAmountLicensorPerf() {
		return amountLicensorPerf;
	}

	public void setAmountLicensorPerf(String amountLicensorPerf) {
		this.amountLicensorPerf = amountLicensorPerf;
	}

	public String getAmountPaiMech() {
		return amountPaiMech;
	}

	public void setAmountPaiMech(String amountPaiMech) {
		this.amountPaiMech = amountPaiMech;
	}

	public String getAmountPaiPerf() {
		return amountPaiPerf;
	}

	public void setAmountPaiPerf(String amountPaiPerf) {
		this.amountPaiPerf = amountPaiPerf;
	}

	public String getAmountPdMech() {
		return amountPdMech;
	}

	public void setAmountPdMech(String amountPdMech) {
		this.amountPdMech = amountPdMech;
	}

	public String getAmountPdPerf() {
		return amountPdPerf;
	}

	public void setAmountPdPerf(String amountPdPerf) {
		this.amountPdPerf = amountPdPerf;
	}

	public String getAmountNotCollectedMech() {
		return amountNotCollectedMech;
	}

	public void setAmountNotCollectedMech(String amountNotCollectedMech) {
		this.amountNotCollectedMech = amountNotCollectedMech;
	}

	public String getAmountNotCollectedPerf() {
		return amountNotCollectedPerf;
	}

	public void setAmountNotCollectedPerf(String amountNotCollectedPerf) {
		this.amountNotCollectedPerf = amountNotCollectedPerf;
	}

	public String getAmountUnmatchedMech() {
		return amountUnmatchedMech;
	}

	public void setAmountUnmatchedMech(String amountUnmatchedMech) {
		this.amountUnmatchedMech = amountUnmatchedMech;
	}

	public String getAmountUnmatchedPerf() {
		return amountUnmatchedPerf;
	}

	public void setAmountUnmatchedPerf(String amountUnmatchedPerf) {
		this.amountUnmatchedPerf = amountUnmatchedPerf;
	}

	public String getClaimPaiMech() {
		return claimPaiMech;
	}

	public void setClaimPaiMech(String claimPaiMech) {
		this.claimPaiMech = claimPaiMech;
	}

	public String getClaimPaiPerf() {
		return claimPaiPerf;
	}

	public void setClaimPaiPerf(String claimPaiPerf) {
		this.claimPaiPerf = claimPaiPerf;
	}

	public String getClaimPdMech() {
		return claimPdMech;
	}

	public void setClaimPdMech(String claimPdMech) {
		this.claimPdMech = claimPdMech;
	}

	public String getClaimPdPerf() {
		return claimPdPerf;
	}

	public void setClaimPdPerf(String claimPdPerf) {
		this.claimPdPerf = claimPdPerf;
	}

	public String getClaimNotCollectedMech() {
		return claimNotCollectedMech;
	}

	public void setClaimNotCollectedMech(String claimNotCollectedMech) {
		this.claimNotCollectedMech = claimNotCollectedMech;
	}

	public String getClaimNotCollectedrPerf() {
		return claimNotCollectedrPerf;
	}

	public void setClaimNotCollectedrPerf(String claimNotCollectedrPerf) {
		this.claimNotCollectedrPerf = claimNotCollectedrPerf;
	}

	public String getClaimUnmatchedMech() {
		return claimUnmatchedMech;
	}

	public void setClaimUnmatchedMech(String claimUnmatchedMech) {
		this.claimUnmatchedMech = claimUnmatchedMech;
	}

	public String getClaimUnmatchedPerf() {
		return claimUnmatchedPerf;
	}

	public void setClaimUnmatchedPerf(String claimUnmatchedPerf) {
		this.claimUnmatchedPerf = claimUnmatchedPerf;
	}

	@Override
	public String toString() {
		return "InvoiceDetailRecord [transactionType=" + transactionType + ", refId=" + refId + ", correctionReference="
				+ correctionReference + ", salesTransactionId=" + salesTransactionId + ", tradingBrand=" + tradingBrand
				+ ", releaseId=" + releaseId + ", resourceId=" + resourceId + ", isrc=" + isrc + ", iswc=" + iswc
				+ ", workcode=" + workcode + ", workTitle=" + workTitle + ", serviceType=" + serviceType + ", useType="
				+ useType + ", useQuantity=" + useQuantity + ", appliedTariff=" + appliedTariff + ", royaltyType="
				+ royaltyType + ", priceBasis=" + priceBasis + ", originalPriceBasis=" + originalPriceBasis
				+ ", royalty=" + royalty + ", musicShare=" + musicShare + ", restrictions=" + restrictions
				+ ", fstLicense=" + fstLicense + ", claimLicensorCombined=" + claimLicensorCombined
				+ ", claimPaiCombined=" + claimPaiCombined + ", claimUnmatchedCombined=" + claimUnmatchedCombined
				+ ", claimPdCombined=" + claimPdCombined + ", claimNotCollectedCombined=" + claimNotCollectedCombined
				+ ", amountInvoicedTotal=" + amountInvoicedTotal + ", claimLicensorMech=" + claimLicensorMech
				+ ", claimLicensorPerf=" + claimLicensorPerf + ", amountLicensorMech=" + amountLicensorMech
				+ ", amountLicensorPerf=" + amountLicensorPerf + ", amountPaiMech=" + amountPaiMech + ", amountPaiPerf="
				+ amountPaiPerf + ", amountPdMech=" + amountPdMech + ", amountPdPerf=" + amountPdPerf
				+ ", amountNotCollectedMech=" + amountNotCollectedMech + ", amountNotCollectedPerf="
				+ amountNotCollectedPerf + ", amountUnmatchedMech=" + amountUnmatchedMech + ", amountUnmatchedPerf="
				+ amountUnmatchedPerf + ", claimPaiMech=" + claimPaiMech + ", claimPaiPerf=" + claimPaiPerf
				+ ", claimPdMech=" + claimPdMech + ", claimPdPerf=" + claimPdPerf + ", claimNotCollectedMech="
				+ claimNotCollectedMech + ", claimNotCollectedrPerf=" + claimNotCollectedrPerf + ", claimUnmatchedMech="
				+ claimUnmatchedMech + ", claimUnmatchedPerf=" + claimUnmatchedPerf + ", recordType=" + recordType
				+ "]";
	}

    
    
}
