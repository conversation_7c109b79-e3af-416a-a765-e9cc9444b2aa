<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="780_sub2" pageWidth="800" pageHeight="842" columnWidth="800" leftMargin="0" rightMargin="0"
              topMargin="0" bottomMargin="0" uuid="2752dd5b-62fe-434b-bdec-a3768fcb362d">
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="distNo" class="java.lang.String"/>
    <field name="societies" class="java.lang.String"/>
    <field name="royalties" class="java.lang.String"/>
    <field name="tax" class="java.lang.String"/>
    <field name="commission" class="java.lang.String"/>
    <field name="netRoyalties" class="java.lang.String"/>
    <background>
        <band splitType="Stretch"/>
    </background>
    <detail>
        <band height="37" splitType="Stretch">
            <textField>
                <reportElement x="0" y="0" width="100" height="30" uuid="914c8949-9089-4c84-866d-5cef0d3e18b4"/>
                <textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="100" y="0" width="100" height="30" uuid="d54fb4e4-c567-496f-94aa-8215e63df1cb"/>
                <textFieldExpression><![CDATA[$F{societies}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="230" y="0" width="70" height="30" uuid="66aabf37-20ce-4f46-8b15-7a98ca8a2895"/>
                <textFieldExpression><![CDATA[$F{royalties}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="330" y="3" width="100" height="30" uuid="5efc0529-dbfc-471a-ad8b-b26fbd6054d1"/>
                <textFieldExpression><![CDATA[$F{tax}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="440" y="0" width="100" height="30" uuid="508fa6ec-81c7-4caf-a725-9f8d1bd14f55"/>
                <textFieldExpression><![CDATA[$F{commission}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="560" y="0" width="100" height="30" uuid="27fc4c1a-045b-4ed0-9b50-e62ddbe4c3e5"/>
                <textFieldExpression><![CDATA[$F{netRoyalties}]]></textFieldExpression>
            </textField>
        </band>
	</detail>
</jasperReport>
