package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalMbrIpName;

@Repository
public interface OrcalMbrIpNameMapper {

	List<OrcalMbrIpName> getMbrIpNameByIpNameNo(String ipNameNo);

    List<OrcalMbrIpName> getMbrIpNameByIpNameNoByAmendTime(String lastMbrIpNameAmendTime);

    List<OrcalMbrIpName> selectByDateByRowNum(String lastMbrIpNameAmendTime, int start, int end);

    List<OrcalMbrIpName> selectByIpNameNos(List<String> ipNameNos);
}
