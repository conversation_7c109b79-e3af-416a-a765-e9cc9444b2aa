package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`wrk_work_iswc_status`")
public class WrkWorkIswcStatus extends BaseEntity {

    /**
     *
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 协会编号
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    @Column(name = "`submission_id`")
    private Long submissionId;

    /**
     *
     */
    @Column(name = "`iswc`")
    private String iswc;

    @Column(name = "`status`")
    private Integer status;

    /**
     *
     */
    @Column(name = "`code`")
    private Integer code;

    @Column(name = "`update_code`")
    private Integer updateCode;

    /**
     *
     */
    @Column(name = "`message`")
    private String message;

    @Column(name = "`update_message`")
    private String updateMessage;

    @Column(name = "category")
    private String category;

    @Column(name = "interested_parties")
    private String interestedParties;

    @Column(name = "original_title")
    private String originalTitle;

    @Column(name = "sourcedb")
    private Integer sourcedb;

    @Column(name = "other_titles")
    private String otherTitles;

    @Column(name = "source_type")
    private Integer sourceType;

    @Column(name = "update_logo")
    private Boolean updateLogo;

    public Boolean getUpdateLogo() {
        return updateLogo;
    }

    public void setUpdateLogo(Boolean updateLogo) {
        this.updateLogo = updateLogo;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getUpdateCode() {
        return updateCode;
    }

    public void setUpdateCode(Integer updateCode) {
        this.updateCode = updateCode;
    }

    public String getUpdateMessage() {
        return updateMessage;
    }

    public void setUpdateMessage(String updateMessage) {
        this.updateMessage = updateMessage;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public Long getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(Long submissionId) {
        this.submissionId = submissionId;
    }

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getInterestedParties() {
        return interestedParties;
    }

    public void setInterestedParties(String interestedParties) {
        this.interestedParties = interestedParties;
    }

    public String getOriginalTitle() {
        return originalTitle;
    }

    public void setOriginalTitle(String originalTitle) {
        this.originalTitle = originalTitle;
    }

    public Integer getSourcedb() {
        return sourcedb;
    }

    public void setSourcedb(Integer sourcedb) {
        this.sourcedb = sourcedb;
    }

    public String getOtherTitles() {
        return otherTitles;
    }

    public void setOtherTitles(String otherTitles) {
        this.otherTitles = otherTitles;
    }


}