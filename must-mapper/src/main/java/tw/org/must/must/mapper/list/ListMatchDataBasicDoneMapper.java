package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataBasicDone;

import java.util.List;

@Repository
public interface ListMatchDataBasicDoneMapper extends BaseMapper<ListMatchDataBasicDone> {

    Integer updateListMatchDataBasicDoneFromHistory(@Param("listMatchDataBasicDone") ListMatchDataBasicDone listMatchDataBasicDone, @Param("list") List<Long> list) ;

//    @Select("SELECT * FROM list_match_data_basic_done lmdbd WHERE file_base_id in(53234, 53236, 53231, 53233) AND file_mapping_id not in( " +
//            " SELECT file_mapping_id FROM dist_data_calc_work_point WHERE dist_no = 'I194' " +
//            ")")
    @Select("SELECT * FROM list_match_data_basic_done lmdbd WHERE file_mapping_id IN(140920430) ")
    List<ListMatchDataBasicDone> test() ;

    void insertDuplicate(List<ListMatchDataBasicDone> list);
}