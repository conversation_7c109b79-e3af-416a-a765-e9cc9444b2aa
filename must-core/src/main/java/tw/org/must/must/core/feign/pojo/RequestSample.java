package tw.org.must.must.core.feign.pojo;

import java.util.ArrayList;
import java.util.List;

import org.elasticsearch.search.SearchHit;

import com.fasterxml.jackson.annotation.JsonIgnore;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 样本
 * 
 * <AUTHOR>
 *
 */
// NOTE: This is mapped by FeignFbiService/JacksonEncoder into JSON; be careful when adding non-@JsonIgnore fields 
public class RequestSample {
	private String id;
	public @JsonIgnore float esScore = -1; // score from ES; -1 if this is not a search result
	private List<String> names;
	private List<String> artists; // 表演者
	private List<String> lyricists; // 词
	private List<String> composers; // 曲
	private String album;// 专辑
	private String lyrics;// 歌词作品表
	private List<String> isrc;// wrk_isrc
	private String iswc;

	public static RequestSample fromSearchHit(SearchHit searchHit) {
		final JSONObject source = JSONObject.fromObject(searchHit.getSourceAsString());
		final RequestSample requestSample = new RequestSample();
		requestSample.setId(String.valueOf(source.getLong("id")));
		requestSample.esScore = searchHit.getScore();
		List<String> names = new ArrayList<String>();
		if (source.containsKey("title")) {
			String titleCn = source.getString("title");
			if (titleCn != null && !"".equals(titleCn)) {
				names.add(titleCn);
			}
		}
		if (source.containsKey("title_en")) {
			String titleEn = source.getString("title_en");
			if (titleEn != null && !"".equals(titleEn)) {
				names.add(titleEn);
			}
		}
		requestSample.setNames(names);

		List<String> artists = new ArrayList<String>();
		if (source.containsKey("artist")) {
			JSONArray artist = source.getJSONArray("artist");
			for (int i = 0; i < artist.size(); i++) {
				String name = artist.getString(i);
				if (name != null && !"".equals(name)) {
					artists.add(name);
				}
			}
		}
		requestSample.setArtists(artists);

		if (source.containsKey("iswc")) {
			String iswc = source.getString("iswc");
			requestSample.setIswc(iswc);
		}

		List<String> isrc = new ArrayList<String>();
		if (source.containsKey("isrc")) {
			JSONArray isrcJson = source.getJSONArray("isrc");
			for (int i = 0; i < isrcJson.size(); i++) {
				String rc = isrcJson.getString(i);
				if (rc != null && !"".equals(rc)) {
					isrc.add(rc);
				}
			}
		}
		requestSample.setIsrc(isrc);

		// 作词
		List<String> lyricists = new ArrayList<String>();
		if (source.containsKey("author")) {
			JSONArray composerJson = source.getJSONArray("author");
			for (int i = 0; i < composerJson.size(); i++) {
				String composer = composerJson.getString(i);
				if (composer != null && !"".equals(composer)) {
					lyricists.add(composer);
				}
			}
		}
		requestSample.setLyricists(lyricists);

		List<String> composers = new ArrayList<String>();
		if (source.containsKey("composer")) {
			JSONArray composer = source.getJSONArray("composer");
			for (int i = 0; i < composer.size(); i++) {
				String name = composer.getString(i);
				if (name != null && !"".equals(name)) {
					composers.add(name);
				}
			}
		}
		requestSample.setComposers(composers);
		// private List<String> album;// 专辑
		return requestSample;
	}
 

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
	
	public List<String> getNames() {
		return names;
	}

	public void setNames(List<String> names) {
		this.names = names;
	}

	public List<String> getArtists() {
		return artists;
	}

	public void setArtists(List<String> artists) {
		this.artists = artists;
	}

	public List<String> getLyricists() {
		return lyricists;
	}

	public void setLyricists(List<String> lyricists) {
		this.lyricists = lyricists;
	}

	public List<String> getComposers() {
		return composers;
	}

	public void setComposers(List<String> composers) {
		this.composers = composers;
	}



	public String getAlbum() {
		return album;
	}

	public void setAlbum(String album) {
		this.album = album;
	}

	public String getLyrics() {
		return lyrics;
	}

	public void setLyrics(String lyrics) {
		this.lyrics = lyrics;
	}

 

	public List<String> getIsrc() {
		return isrc;
	}

	public void setIsrc(List<String> isrc) {
		this.isrc = isrc;
	}

	public String getIswc() {
		return iswc;
	}

	public void setIswc(String iswc) {
		this.iswc = iswc;
	}

}
