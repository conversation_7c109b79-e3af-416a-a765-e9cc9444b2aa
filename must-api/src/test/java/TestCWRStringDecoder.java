import com.firstbrave.api.base.CwrRecord;
import com.firstbrave.api.parser.CwrFileDecoder;
import com.firstbrave.api.parser.CwrFileEncoder;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class TestCWRStringDecoder {


   @Test
    public void finalTest() throws Exception {
        // \u000d	System.out.println("Comments in java");
        String file = "G:\\项目资料\\MUST\\document\\解析接口文檔\\编码测试文件\\SGAE_CRD20171218124851161072.021";
        Path path = Paths.get(file);
        if(!path.toFile().exists()) return;
        List<String> list =new ArrayList<>();
       Reader fin = new FileReader(file);

       BufferedReader bin = new BufferedReader(fin);
       String str;
       do {
           str = bin.readLine();
           if (str != null){
               list.add(str);
           }
       }
       while (str != null);



        List<CwrRecord> records = CwrFileDecoder.decode(new LinkedList<>(list));

        List<String> ls = CwrFileEncoder.encode(records);

        Files.write(Paths.get(file.concat(".ENCODE")),ls);
    }



}
