<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="880_child" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a85a31da-9e03-42ac-96ce-df43134840f6">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="distNo" class="java.lang.String"/>
	<field name="balance" class="java.lang.String"/>
	<field name="bankAccountName" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="18" splitType="Stretch">
			<textField>
				<reportElement x="40" y="0" width="70" height="18" uuid="b94ecca7-207c-47b6-a16c-bef22815e585">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="0" width="70" height="18" uuid="50030a0e-bfd5-4693-b869-************">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{balance}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="180" y="0" width="150" height="18" uuid="1c983543-a039-44bd-b25c-6831c518fc9a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankAccountName}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
