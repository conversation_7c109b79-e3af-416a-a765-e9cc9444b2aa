package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistAutopaySocRetain;

import java.util.List;

@Repository
public interface DistAutopaySocRetainMapper extends BaseMapper<DistAutopaySocRetain> {

    Integer insertOrUpdate(List<DistAutopaySocRetain> distAutopaySocRetainList);

    @Select("SELECT * FROM dist_autopay_society_retain where society_code IN (SELECT society_code FROM dist_autopay_society_retain  WHERE  is_pay = 'Y' GROUP BY society_code having SUM(pay_amount) > 2000)")
    List<DistAutopaySocRetain> selectAutopayForSociety();


}