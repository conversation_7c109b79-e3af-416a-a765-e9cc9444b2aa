package tw.org.must.must.model.wrk;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`wrk_work_origin_writer`")
public class WrkWorkOriginWriter extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 685015252362190088L;

	/**
     * 
     */
    @Column(name = "work_id")
    @NotNull(message="wrk_work_origin_writer中的workId不能为空！")
    private Long workId;

    /**
     * 
     */
    @Column(name = "work_society_code")
    @NotNull(message="wrk_work_origin_writer中的workSocietyCode不能为空！")
    private Integer workSocietyCode;
    
    /**
     * 
     */
    @Column(name = "work_unique_key")
    @NotNull(message="wrk_work_origin_writer中的work_unique_key不能为空！")
    private String workUniqueKey;

    /**
     * 
     */
    @Column(name = "ip_name_no")
    private String ipNameNo;

    /**
     * 
     */
    @Column(name = "ip_base_no")
    private String ipBaseNo;


    /**
     * 
     */
    @Column(name = "work_ip_role")
    private String workIpRole;

    /**
     * 
     */
    @Column(name = "name")
    private String name;

    /**
     * 
     */
    @Column(name = "chinese_name")
    private String chineseName;

    /**
     * 
     */
    @Column(name = "name_type")
    private String nameType;

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

	public Integer getWorkSocietyCode() {
		return workSocietyCode;
	}

	public void setWorkSocietyCode(Integer workSocietyCode) {
		this.workSocietyCode = workSocietyCode;
	}

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

	public String getIpNameNo() {
		return ipNameNo;
	}

	public void setIpNameNo(String ipNameNo) {
		this.ipNameNo = ipNameNo;
	}

	public String getIpBaseNo() {
		return ipBaseNo;
	}

	public void setIpBaseNo(String ipBaseNo) {
		this.ipBaseNo = ipBaseNo;
	}

	public String getWorkIpRole() {
		return workIpRole;
	}

	public void setWorkIpRole(String workIpRole) {
		this.workIpRole = workIpRole;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getChineseName() {
		return chineseName;
	}

	public void setChineseName(String chineseName) {
		this.chineseName = chineseName;
	}

	public String getNameType() {
		return nameType;
	}

	public void setNameType(String nameType) {
		this.nameType = nameType;
	}
    
}