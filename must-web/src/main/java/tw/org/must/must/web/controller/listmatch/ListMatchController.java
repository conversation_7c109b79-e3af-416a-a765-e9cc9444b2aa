package tw.org.must.must.web.controller.listmatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.common.util.excel.ExcelUtil;
import tw.org.must.must.common.util.excel.GenerateExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.list.*;
import tw.org.must.must.dto.listmatch.*;
import tw.org.must.must.model.list.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "一般分配-比对数据审核")
@RestController
@RequestMapping("/listmatch")
public class ListMatchController {

    private static final Logger log = LoggerFactory.getLogger(ListMatchController.class);

    @Autowired
    private ListMatchDataBasicService listMatchDataBasicService;
    @Autowired
    private ListMatchDataBasicMatchWorkService listMatchDataBasicMatchWorkService;
    @Autowired
    private ListBasicFileBaseService listBasicFileBaseService;
    @Autowired
    private ListBasicFileDataMappingService listBasicFileDataMappingService;
    @Autowired
    private ListMatchDataBasicMatchHistoryService listMatchDataBasicMatchHistoryService;

    @Autowired
    private ListFilePathChangeService listFilePathChangeService;

    @ApiOperation(value = "获取待审核列表 - 一般清单")
    @PostMapping("getListMatchList")
    public Result<PageInfo<ListMatchDataBasic>> getListMatchList(@RequestBody ListMatchV2Dto listMatchV2Dto) {
        Long fileBaseId = listMatchV2Dto.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
        }
        ListBasicFileBase base = listBasicFileBaseService.getById(fileBaseId);
        if (null == base || !StringUtils.equalsIgnoreCase("P", base.getBatchType())) {
            return new Result<>(HttpStatus.OK.value(), null);
        }
        ListMatchDataBasic listMatchDataBasic = new ListMatchDataBasic();
        BeanUtils.copyProperties(listMatchV2Dto, listMatchDataBasic);
        PageHelper.startPage(listMatchV2Dto.getPageNum(), listMatchV2Dto.getPageSize());
        List<ListMatchDataBasic> list = listMatchDataBasicService.getListMatchList(listMatchDataBasic, listMatchV2Dto.getBatchIdA(), listMatchV2Dto.getBatchIdB());
        return new Result(HttpStatus.OK.value(), null, new PageInfo<>(list));
    }

    @ApiOperation(value = "获取列表 - 单场次清单")
    @PostMapping("getSingleListMatchList")
    public Result<Map<String, Object>> getSingleListMatchList(@RequestBody ListMatchDto listMatchDto) {
        Page page = listMatchDto.getPage();
        ListMatchDataBasic listMatchDataBasic = listMatchDto.getListMatchDataBasic();
        Long fileBaseId = listMatchDataBasic.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
        }
        Map<String, Object> result = new HashMap<>();
        ListBasicFileBase base = listBasicFileBaseService.getById(fileBaseId);
        if (null == base || !StringUtils.equalsIgnoreCase("S", base.getBatchType())) {
            return new Result<>(HttpStatus.OK.value(), null);
        }
        base.initConcertTitle();
        PageInfo<ListMatchDataBasic> list = listMatchDataBasicService.getListMatchList(page, listMatchDataBasic);
        result.put("base", base);
        result.put("list", list);
        return new Result(HttpStatus.OK.value(), null, result);
    }

    @ApiOperation(value = "获取待审核列表")
    @PostMapping("getSingleListMatchListNotAudit")
    public Result<Map<String, Object>> getSingleListMatchListNotAudit(@RequestBody ListMatchDto listMatchDto) {
        Page page = listMatchDto.getPage();
        ListMatchDataBasic listMatchDataBasic = listMatchDto.getListMatchDataBasic();
        listMatchDataBasic.setStatus(3);
        Long fileBaseId = listMatchDataBasic.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
        }
        Map<String, Object> result = new HashMap<>();
        ListBasicFileBase base = listBasicFileBaseService.getById(fileBaseId);
        if (null == base || !StringUtils.equalsIgnoreCase("S", base.getBatchType())) {
            return new Result<>(HttpStatus.OK.value(), null);
        }
        base.initConcertTitle();
        PageInfo<ListMatchDataBasic> list = listMatchDataBasicService.getListMatchList(page, listMatchDataBasic);
        result.put("base", base);
        result.put("list", list);
        return new Result(HttpStatus.OK.value(), null, result);
    }

    @ApiOperation(value = "根据id查看详情")
    @GetMapping("getListMatchDataBasicById")
    public ResponseEntity<ListMatchDataBasic> getListMatchDataBasicById(Long id) {
        ListMatchDataBasic listMatchDataBasic = listMatchDataBasicService.getById(id);
        return new ResponseEntity<>(listMatchDataBasic, HttpStatus.OK);
    }

    @ApiOperation(value = "根据MatchBaseId查看列表")
    @GetMapping("getListMatchDataBasicMatchWorkByMatchBaseId")
    public ResponseEntity<List<ListMatchDataBasicMatchWork>> getListMatchDataBasicMatchWorkByMatchBaseId(
            Long matchBaseId) {
        List<ListMatchDataBasicMatchWork> list = listMatchDataBasicMatchWorkService
                .getListMatchDataBasicMatchWorkByMatchBaseId(matchBaseId);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @ApiOperation(value = "审核清单")
    @PostMapping("checkListMatchDataBasic")
    public Result<String> checkListMatchDataBasic(@RequestBody CheckListMatchDto checkListMatchDto) {
        String message = "";
        int code = HttpStatus.BAD_REQUEST.value();
        Integer status = checkListMatchDto.getStatus();
        String uniqueKeyMd5 = checkListMatchDto.getUniqueKeyMd5();
        Long matchDspMappingId = checkListMatchDto.getMatchDspMappingId();

        if ((matchDspMappingId == null || matchDspMappingId < 1L) && status == 1) {
            message = "沒有選擇對應的Match Work Info數據！";
        } else {
            Integer type = null;
            try {
                type = listBasicFileDataMappingService.checkListMatchDataBasic(status, uniqueKeyMd5, matchDspMappingId,checkListMatchDto.getFileBaseId());
            } catch (MustException e) {
                return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
            }
            if (type == -1) {
                message = "匹配workTitleId不存在";
            } else if (type == -2) {
                message = "uniqueKeyMd5為空！";
            } else {
                code = HttpStatus.OK.value();
            }
        }
        return new Result<>(code, message);
    }

    @ApiOperation(value = "批量审核清单")
    @PostMapping("checkListMatchDataBasicAll")
    public Result<String> checkListMatchDataBasicAll(@RequestBody CheckBatchListMatchDto checkBatchListMatchDto) {
        List<String> uniqueKeyMd5List = checkBatchListMatchDto.getUniqueKeyMd5List();
        Integer status = checkBatchListMatchDto.getStatus();
        Long fileBaseId = checkBatchListMatchDto.getFileBaseId();

        if (fileBaseId == null) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "fileBaseId为空");
        }

        if (uniqueKeyMd5List == null || uniqueKeyMd5List.isEmpty()) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "uniqueKeyMd5List为空");
        }

        if (status != 1 && status != 2) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "传入的status不正确");
        }

        List<ListMatchDataBasic> listMatchDataBasicList = listMatchDataBasicService.getListMatchListByUniqueKeyMd5List(uniqueKeyMd5List,fileBaseId);
        if (listMatchDataBasicList == null || listMatchDataBasicList.size() < 1) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "根据uniqueKeyMd5List没有找到对应的数据");
        }
        StringBuilder sb = new StringBuilder();
        if (status == 1) {
            List<ListMatchDataBasic> isNotMatchList = listMatchDataBasicList.stream().filter(it -> StringUtils.isBlank(it.getMatchWorkUniqueKey())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(isNotMatchList)) {
                //审核通过多少条，多少条无match数据，未配成功
                sb.append("，").append(isNotMatchList.size()).append("條無匹配數據，請單獨指定作品！");
            }
        }
        // 结果中去除 match_work_unique_key 为空的数据
        List<ListMatchDataBasic> resultList = listMatchDataBasicList.stream().filter(it -> StringUtils.isNotBlank(it.getMatchWorkUniqueKey())).collect(Collectors.toList());
        listMatchDataBasicService.checkListMatchDataBasicAll(resultList, status);
        if (sb.length() == 0) {
            sb.append(resultList.size()).append("條！");
        } else {
            sb.insert(0, String.format("%d條", resultList.size()));
        }
        if (status == 1) {
            return new Result<>(HttpStatus.OK.value(), null, String.format("批量審核通過成功%s", sb.toString()));
        }
        return new Result<>(HttpStatus.OK.value(), null, String.format("批量審核拒絕成功%s", sb.toString()));
    }

//	@ApiOperation(value = "批量审核拒绝清单")
//	@PostMapping("checkRefuseListMatchDataBasicAll")
//	public ResponseEntity<Integer> checkRefuseListMatchDataBasicAll(@RequestBody List<String> uniqueKeyMd5List) {
//		if (uniqueKeyMd5List == null || uniqueKeyMd5List.isEmpty()) {
//			return new ResponseEntity<>(0, HttpStatus.OK);
//		}
//		List<ListMatchDataBasic> listMatchDataBasicList = listMatchDataBasicService
//				.getListMatchListByUniqueKeyMd5List(uniqueKeyMd5List);
//		if (listMatchDataBasicList == null || listMatchDataBasicList.size() < 1) {
//			return new ResponseEntity<>(0, HttpStatus.OK);
//		}
//
//		listMatchDataBasicList.forEach(matchDataBasic ->{
//			Long matchWorkTitleId = matchDataBasic.getMatchWorkTitleId();
//			String uniqueKeyMd5 = matchDataBasic.getUniqueKeyMd5();
//			Integer status = 2;
//			listBasicFileDataMappingService.checkListMatchDataBasic(status, uniqueKeyMd5, matchWorkTitleId);
//		});
//
////		Map<String, List<ListMatchDataBasic>> map = listMatchDataBasicList.stream()
////				.collect(Collectors.groupingBy(ListMatchDataBasic::getUniqueKeyMd5));
////		for (String key : map.keySet()) {
////			ListMatchDataBasic listMatchDataBasic = map.get(key).get(0);
////			listMatchDataBasic.setStatus(2);
////			listBasicFileDataMappingService.checkListMatchDataBasic(listMatchDataBasic);
////		}
//
//		return new ResponseEntity<>(1, HttpStatus.OK);
//	}

    @ApiOperation(value = "下载待审核列表")
    @GetMapping("downloadListMatchList")
    public void downloadListMatchList(@RequestParam ListMatchDataBasic listMatchDataBasic,
                                      HttpServletResponse response) {
        Page page = new Page();
        page.setPageNum(1);
        page.setPageNum(Integer.MAX_VALUE);
        PageInfo<ListMatchDataBasic> pageInfo = listMatchDataBasicService.getListMatchList(page, listMatchDataBasic);
        List<ListMatchDataBasic> list = pageInfo.getList();

        list.forEach(it -> {
            it.setTvName(it.getDurationM() + ":" + it.getDurationS());
            it.setUniqueKeyMd5(it.getMatchWorkSocietyCode() + "" + it.getMatchWorkId());
        });

        String[] title = new String[]{"FID", "List Type", "Source", "Category;", "Title", "Aritists", "Authors",
                "Composers", "Duration", "Match Work Titile", "Soc-WorkId", "Score"};
        String[] titleKey = new String[]{"id", "uploadType", "sourceCode", "categoryCode", "title", "artists",
                "authors", "composers", "tvName", "matchWorkTitle", "uniqueKeyMd5", "matchScore"};

        GenerateExcelUtil<ListMatchDataBasic> generateExcelUtil = new GenerateExcelUtil<ListMatchDataBasic>(title,
                titleKey, list);

        generateExcelUtil.init();
        Workbook workbook = generateExcelUtil.getWorkbook();
        try {
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String("ListMatchDataMasic.xlsx".getBytes("utf-8"), "ISO-8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Value("${list.file.uploadTempPath}")
    private String uploadTempPath;

    @Value("${list.file.errorPath}")
    private String errorFilePath;


    /*ExcelUtil.read(targetFile, Arrays.asList("dataUniqueKey", "matchWorkNum", "matchSoc"), (t, u) -> {
        log.info("t: {} ========  u: {}}", t, JSON.toJSONString(u));
        try {
            String dataUniqueKey = u.get("dataUniqueKey");
            String workNum = u.get("matchWorkNum");
            String workSoc = u.get("matchSoc");
            if (StringUtils.isBlank(dataUniqueKey) || StringUtils.isBlank(workNum) || StringUtils.isBlank(workSoc)) {
                parseLogLines.add(t + "dataUniqueKey,workNum,workSoc至少有一個為空");
                return;
            }else {
                List<String> values = map.get(dataUniqueKey);
                if(null == values ){
                    map.put(dataUniqueKey,Arrays.asList(workSoc,workNum,t));
                }else {
                    String workSoc2 = values.get(0);
                    String workNum2 = values.get(1);
                    if(!workSoc.equals(workSoc2) || !workNum.equals(workNum2)){ // dataUniqueKey相等  worksoc或者worknum不等
                        conflicts.add(dataUniqueKey) ;
                        parseLogLines.add(String.format("%s:[%s]-[%s]","同一Data Unique Key填寫不同WORK NO",values.get(2),t));
                    }
                }

            }
//                    listMatchDataBasicService.importAuditResults(dataUniqueKey, workNum, workSoc);
        } catch (Exception e) {
            log.error("一般清单 - 审核结果导入处理失败， {}, msg： {}", t, e.getMessage());
            sb.append(String.format("%s, msg： %s", t, e.getMessage())).append(CommonUtils.LINE_BREAK);
        }
    });*/

    @ApiOperation(value = "审核结果导入")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件", name = "file", dataType = "MultipartFile", paramType = "query"),
            @ApiImplicitParam(value = "fid", name = "fid", dataType = "Long", paramType = "query"),})
    @RequestMapping(value = "/importAuditResults", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<String> importAuditResults(
            @RequestParam("file") MultipartFile file,@RequestParam(name = "fid") Long fid) {
        String message = "";
        if(fid == null){
            message = "fid不能为空！";
        }
        if (null == file) {
            message = "上傳文件不能為空！";
        } else if (file.getSize() < 0L) {
            message = "上傳文件内容爲空！";
        } else if (!StringUtils.endsWithAny(file.getOriginalFilename().toLowerCase(), ".xlsx", ".xls")) {
            message = "上傳文件格式必須是以下類型【.xlsx, .xls】！";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), message);
        }

        if (CommonUtils.isWindows()) {
            uploadTempPath = "E:\\Test";
        }
        String filePath = uploadTempPath;
        File parentFile = new File(filePath);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fileName = file.getOriginalFilename();
        File targetFile = null;
        try {
            String targetFileName = Md5.getMd5ByInputStream(file.getInputStream());
            if (StringUtils.isBlank(targetFileName)) {
                message = "文件讀取異常！";
            }
            String fileExt = fileName.substring(fileName.lastIndexOf("."));
            targetFileName = targetFileName + fileExt;
            targetFile = new File(parentFile, Objects.requireNonNull(targetFileName));
            file.transferTo(targetFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (!targetFile.exists()) {
            message = "文件轉換失敗！";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
        }
        // 读取文件，找到对应字段， 主要是 DataUniqueKey, workNum, workSoc
        StringBuilder sb = new StringBuilder();
        try {

            Map<String, List<String>> dataUniqueKeyMap = new HashMap<>();
            Map<String, List<String>> dataUniqueKeyTitleMap = new HashMap<>();
            List<String> errorList = new ArrayList<>();
            ExcelUtil.read(targetFile, Arrays.asList("Match Work Num", "Match Soc","Data Unique Key","Title"), (t, u) -> {
                log.info("t: {} ========  u: {}}", t, JSON.toJSONString(u));
                try {
                    String workNum = u.get("Match Work Num");
                    String workSoc = u.get("Match Soc");
                    String dataUniqueKey = u.get("Data Unique Key");
                    String title = u.get("Title");
                    if(StringUtils.isNotBlank(dataUniqueKey) && StringUtils.isNotBlank(workNum) && StringUtils.isNotBlank(workSoc)){
                        List<String> list = dataUniqueKeyMap.get(dataUniqueKey);
                        if(list == null){
                            list = new ArrayList<>();
                            dataUniqueKeyMap.put(dataUniqueKey,list);
                        }
                        list.add(StringUtils.leftPad(workSoc.trim(), 3, "0").concat("-").concat(workNum.trim()).concat("-"));

                        if(StringUtils.isNotBlank(title)){
                            List<String> titleList = dataUniqueKeyTitleMap.get(dataUniqueKey);
                            if(titleList == null){
                                titleList = new ArrayList<>();
                                dataUniqueKeyTitleMap.put(dataUniqueKey,titleList);
                            }
                            titleList.add(StringUtils.leftPad(workSoc.trim(), 3, "0").concat("-").concat(workNum.trim()).concat("-").concat(title.trim()));
                        }
                    }
//                    listMatchDataBasicService.importAuditResults(dataUniqueKey, workNum, workSoc,isDistMap);
//                    listMatchDataBasicService.importAuditResults(fid,dataUniqueKey, workNum, workSoc);
                } catch (Exception e) {
                    log.error("一般清单 - 审核结果导入处理失败， {}, msg： {}", t, e.getMessage());
                    sb.append(String.format("%s, msg： %s", t, e.getMessage())).append(CommonUtils.LINE_BREAK);
                }
            });

            Map<String, String> filterMap = new HashMap<>();
            for(String  dataUniqueKey : dataUniqueKeyTitleMap.keySet() ){
                List<String> list = dataUniqueKeyTitleMap.get(dataUniqueKey);
                if(list.size() > 1){
                    Set<String> set = new HashSet<>(list);
                    if(set.size() > 1){
                        String error = "相同的dataUniqueKey: 【" +  dataUniqueKey + "】,指定了不同的workNum/workSoc/title";
                        errorList.add(error);
                    }
                } else {
                    filterMap.put(dataUniqueKey,list.get(0));
                }
            }
            for(String  dataUniqueKey : dataUniqueKeyMap.keySet() ){
                List<String> list = dataUniqueKeyMap.get(dataUniqueKey);
                if(list.size() > 1){
                    Set<String> set = new HashSet<>(list);
                    if(set.size() > 1){
                        String error = "相同的dataUniqueKey: 【" +  dataUniqueKey + "】,指定了不同的workNum/workSoc";
                        errorList.add(error);
                    }
                } else if(!filterMap.containsKey(dataUniqueKey)){
                    filterMap.put(dataUniqueKey,list.get(0));
                }
            }

            if(errorList.size() > 0){
                String errorPath = errorFilePath;
                String parseLogFilePath = String.format("%s/%s_%s.log",errorPath, fileName.substring(0, filePath.lastIndexOf(".")), DateParse.format(new Date(), DateParse.patternFile));
                try (FileWriter fileWriter = new FileWriter(parseLogFilePath, true)) {
                    for (String line : errorList) {
                        fileWriter.write(line);
                        fileWriter.write(System.lineSeparator());
                    }
                    fileWriter.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                String shareFilePath = listFilePathChangeService.getShareFilePath(parseLogFilePath);
//                sb.append(System.lineSeparator()).append("错误文件：").append(shareFilePath);
                return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "指定數據有衝突。請確認！" + shareFilePath);
            }

            for(Map.Entry<String,String> entry : filterMap.entrySet()){
                String[] strings = entry.getValue().split("-");
                String error = listMatchDataBasicService.importAuditResults(fid,entry.getKey(), strings[1], strings[0],strings[2]);
                if(StringUtils.isNotBlank(error)){
                    errorList.add(error);
                }
            }

            if(errorList.size() > 0){
                String errorPath = errorFilePath;
                String parseLogFilePath = String.format("%s%s%s_%s_%s.log",errorPath,File.separator, fileName.substring(0, filePath.lastIndexOf(".")), DateParse.format(new Date(), DateParse.patternFile));
                try (FileWriter fileWriter = new FileWriter(parseLogFilePath, true)) {
                    for (String line : errorList) {
                        fileWriter.write(line);
                    }
                    fileWriter.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                String shareFilePath = listFilePathChangeService.getShareFilePath(parseLogFilePath);
//                sb.append(System.lineSeparator()).append("错误文件：").append(shareFilePath);
                return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "部分數據回填失敗。請確認！" + shareFilePath);
            }

        } catch (Exception e) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            sb.insert(0, String.format("文件解析異常!%s", CommonUtils.LINE_BREAK));
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), sb.toString());
        }
        return new Result<>(HttpStatus.OK.value(), message, "導入成功！");
    }


    @Deprecated
    @ApiOperation(value = "导出")
    @PostMapping(value = "/export1", produces = "application/json;charset=utf-8")
    public void export1(HttpServletResponse response, @RequestBody ListMatchDto listMatchDto) {
        ListMatchDataBasic listMatchDataBasic = listMatchDto.getListMatchDataBasic();
        Long fileBaseId = listMatchDataBasic.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
//            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
            return;
        }
        Integer count = listMatchDataBasicService.getListMatchListCount(listMatchDataBasic);
        ListBasicFileBase base = listBasicFileBaseService.getById(fileBaseId);
        if (count == 0) {
//            return new Result(HttpStatus.OK.value(), "查询结果数量：0 ");
            return;
        }
        String format = DateParse.format(new Date(), DateParse.patternTime);
        String titlePrex = "一般";
        if(Objects.nonNull(base) && "S".equals(base.getBatchType())) {
            titlePrex = "單場次";
        }
        String fileName = String.format("%s清單審核數據_%s_%d_%s", titlePrex, fileBaseId, count, format);
        GenerateExcelUtil<ListMatchDataBasic> generateExcelUtil = null;
        String message = null;
        try {
            // 这里URLEncoder.encode可以防止中文乱码
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(new String(fileName.concat(".xlsx").getBytes("utf-8")), "UTF-8"));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String[] titleArray = {"FID","Title", "Performer", "Author", "Composer", "ISRC", "iswc", "STATUS", "Match Work Num",
                    "Match Soc", "Data Unique Key","Type","CATEGORY","match title","SCORE","FREQ","REMARK"};
            String[] keyArray = {"fileBaseId","title", "artists", "authors", "composers", "isrc", "iswc", "statusStr", "matchWorkId",
                    "matchWorkSocietyCode", "uniqueKeyMd5","uploadType","categoryCode","matchWorkTitle","matchScore","clickNumber","extJson"};
            generateExcelUtil = new GenerateExcelUtil<>(titleArray, keyArray, false);
            String sheetName = "sheet";
            int sheetAt = 1;
            for (int i = 1; i < Integer.MAX_VALUE; i++) {
                PageInfo<ListMatchDataBasic> pageInfo = listMatchDataBasicService.getListMatchList(new Page(i, 10000), listMatchDataBasic);
                if (null == pageInfo || CollectionUtils.isEmpty(pageInfo.getList())) {
                    break;
                }
                Sheet sheet = generateExcelUtil.getWorkbook().getSheet(sheetName);
                // 减1 去除行数
                if (null != sheet && sheet.getLastRowNum() > SpreadsheetVersion.EXCEL2007.getLastRowIndex() - pageInfo.getList().size() - 1) {
                    sheetName = String.format("%s_%d", sheetName, sheetAt++);
                }
                List<ListMatchDataBasic> list = pageInfo.getList();
                list.forEach(it -> {
                    switch(it.getStatus()){
                        case 0:
                            it.setStatusStr("待審核");
                            break;
                        case 1:
                            it.setStatusStr("已匹配");
                            break;
                        case 2:
                            it.setStatusStr("不匹配");
                            break;
                        case 3:
                            it.setStatusStr("認作者");
                            break;
                    }

                });
                /*list.stream().filter(x -> x.getStatus() == 2).forEach(z -> {
                    // 出了匹配成功的需要导出match info信息外，其他状态的置为0
                    z.setMatchWorkId(null);
                    z.setMatchWorkSocietyCode(null);
                });*/

                generateExcelUtil.add(sheetName, list);
                pageInfo.getList().clear();
            }
            // 最后输出
            generateExcelUtil.getWorkbook().write(response.getOutputStream());
        } catch (UnsupportedEncodingException e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        } catch (IOException e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        } catch (Exception e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        }
//        finally {
//            if (generateExcelUtil != null && null != generateExcelUtil.getWorkbook()) {
//                try {
//                    generateExcelUtil.getWorkbook().close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        if(StringUtils.isNotBlank(message)) {
//            if(!response.isCommitted()) {
//                response.reset();
//            }
//            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
//        }
//        return new Result<>(HttpStatus.OK.value(), null);
    }

    public Map listMatchDataBasictoMap(ListMatchDataBasic basic){
        Map<String,Object> map = new HashMap<>();
        map.put("fileBaseId",basic.getFileBaseId());
        map.put("title",basic.getTitle());
        map.put("artists",basic.getArtists());
        map.put("authors",basic.getAuthors());
        map.put("composers",basic.getComposers());
        map.put("isrc",basic.getIsrc());
        map.put("iswc",basic.getIswc());
        map.put("matchWorkId",basic.getMatchWorkId());
        map.put("matchWorkSocietyCode",basic.getMatchWorkSocietyCode());
        map.put("uniqueKeyMd5",basic.getUniqueKeyMd5());
        map.put("uploadType",basic.getUploadType());
        map.put("categoryCode",basic.getCategoryCode());
        map.put("matchWorkTitle",basic.getMatchWorkTitle());
        map.put("matchScore",basic.getMatchScore());
        map.put("clickNumber",basic.getClickNumber());

        String statusStr = "";
        switch(basic.getStatus()){
            case 0:
                statusStr = "待審核";
                break;
            case 1:
                statusStr = "已匹配";
                break;
            case 2:
                statusStr = "不匹配";
                break;
            case 3:
                statusStr = "認作者";
                break;
        }
        map.put("statusStr",statusStr);
        return map;
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/export", produces = "application/json;charset=utf-8")
    public void export(HttpServletResponse response, @RequestBody ListMatchDto listMatchDto) {
        ListMatchDataBasic listMatchDataBasic = listMatchDto.getListMatchDataBasic();
        Long fileBaseId = listMatchDataBasic.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
//            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
            return;
        }
        Integer count = listMatchDataBasicService.getListMatchListCount(listMatchDataBasic);
        ListBasicFileBase base = listBasicFileBaseService.getById(fileBaseId);
        if (count == 0) {
//            return new Result(HttpStatus.OK.value(), "查询结果数量：0 ");
            return;
        }
        String format = DateParse.format(new Date(), DateParse.patternTime);
        String titlePrex = "一般";
        if(Objects.nonNull(base) && "S".equals(base.getBatchType())) {
            titlePrex = "單場次";
        }
        String fileName = String.format("%s清單審核數據_%s_%d_%s", titlePrex, fileBaseId, count, format);
        GenerateExcelUtil<Map> generateExcelUtil = null;
        String message = null;
        try {
            // 这里URLEncoder.encode可以防止中文乱码
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(new String(fileName.concat(".xlsx").getBytes("utf-8")), "UTF-8"));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");

            List<String> fixedTitles = new ArrayList<>(Arrays.asList("FID","Title", "Performer", "Author", "Composer", "ISRC", "iswc", "STATUS", "Match Work Num",
                    "Match Soc", "Data Unique Key","Type","CATEGORY","match title","SCORE","FREQ"));
            List<String> fixedKeys = new ArrayList<>(Arrays.asList("fileBaseId","title", "artists", "authors", "composers", "isrc", "iswc", "statusStr", "matchWorkId",
                    "matchWorkSocietyCode", "uniqueKeyMd5","uploadType","categoryCode","matchWorkTitle","matchScore","clickNumber"));

            String sheetName = "sheet";
            int sheetAt = 1;
            for (int i = 1; i < Integer.MAX_VALUE; i++) {
                PageInfo<ListMatchDataBasic> pageInfo = listMatchDataBasicService.getListMatchList(new Page(i, 10000), listMatchDataBasic);
                if (null == pageInfo || CollectionUtils.isEmpty(pageInfo.getList())) {
                    break;
                }

                List<ListMatchDataBasic> list = pageInfo.getList();
                Set<String> dynamicTitleSet = new HashSet<>();
                List<Map> exports = new ArrayList<>();
                list.forEach(it -> {

                    Map map = this.listMatchDataBasictoMap(it);
                    if(StringUtils.isNotEmpty(it.getExtJson())){
                        Map<String,String> extJsonMap = JSON.parseObject(it.getExtJson(),Map.class);
                        String remark = extJsonMap.get("remark");
                        if(StringUtils.isNotEmpty(remark)){
                            Map<String,String> remarkMap = JSON.parseObject(remark,Map.class);
                            dynamicTitleSet.addAll(remarkMap.keySet());
                            map.putAll(remarkMap);
                        }
                    }
                    exports.add(map);
                });
                /*list.stream().filter(x -> x.getStatus() == 2).forEach(z -> {
                    // 出了匹配成功的需要导出match info信息外，其他状态的置为0
                    z.setMatchWorkId(null);
                    z.setMatchWorkSocietyCode(null);
                });*/

                if(i == 1){
                    fixedTitles.addAll(dynamicTitleSet);
                    fixedKeys.addAll(dynamicTitleSet);
                    generateExcelUtil = new GenerateExcelUtil<>(fixedTitles.toArray(new String[fixedTitles.size()]), fixedKeys.toArray(new String[fixedKeys.size()]), false);

                    Sheet sheet = generateExcelUtil.getWorkbook().getSheet(sheetName);
                    // 减1 去除行数
                    if (null != sheet && sheet.getLastRowNum() > SpreadsheetVersion.EXCEL2007.getLastRowIndex() - pageInfo.getList().size() - 1) {
                        sheetName = String.format("%s_%d", sheetName, sheetAt++);
                    }
                }

                generateExcelUtil.add(sheetName, exports);
                pageInfo.getList().clear();
            }
            // 最后输出
            generateExcelUtil.getWorkbook().write(response.getOutputStream());
        } catch (UnsupportedEncodingException e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        } catch (IOException e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        } catch (Exception e) {
            log.error("msg", e);
            message = "msg: " + e.getMessage();
        }
//        finally {
//            if (generateExcelUtil != null && null != generateExcelUtil.getWorkbook()) {
//                try {
//                    generateExcelUtil.getWorkbook().close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        if(StringUtils.isNotBlank(message)) {
//            if(!response.isCommitted()) {
//                response.reset();
//            }
//            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
//        }
//        return new Result<>(HttpStatus.OK.value(), null);
    }

    /**
     * 单场次清单数据保存和修改，需要同步修改mapping，done，history数据
     * @param listMatchDataBasic
     * @return
     */
    @ApiOperation("新增or更新（单场次）")
    @PostMapping(value = "/saveOrUpateSingle")
    public Result<Integer> saveOrUpateSingle(@RequestBody ListMatchDataBasic listMatchDataBasic) {
        if (null == listMatchDataBasic.getFileBaseId() && null == listMatchDataBasic.getId()) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正确！");
        }
        if(StringUtils.isBlank(listMatchDataBasic.getTitle())) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "work title不能爲空！");
        }
        if (null == listMatchDataBasic.getId()) {
            ListBasicFileBase fileBase = listBasicFileBaseService.getById(listMatchDataBasic.getFileBaseId());
            listMatchDataBasic.setCategoryCode(fileBase.getCategoryCode());
            listMatchDataBasic.setPoolCode(fileBase.getPoolCode());
            listMatchDataBasic.setPoolRight(fileBase.getPoolRight());
            ListBasicFileDataMapping mapping = transformToMapping(listMatchDataBasic); // 注意说明categoryCode等参数从base数据中获取
            listBasicFileDataMappingService.saveMatchBasic(mapping, listMatchDataBasic);
        } else {
            // 跟新
            listBasicFileDataMappingService.updateMatchBasic(listMatchDataBasic);
        }
        List<ListBasicFileDataMapping> mappingList = listBasicFileDataMappingService.getListBasicFileDataMappingListByBaseId(listMatchDataBasic.getFileBaseId(), null, null);
        ListBasicFileBase base = new ListBasicFileBase();
        base.setId(listMatchDataBasic.getFileBaseId());
        base.setNumberOfLines((long) mappingList.size());
        base.setClickNumber(mappingList.stream().map(t -> null == t.getClickNumber() ? BigDecimal.ONE : t.getClickNumber()).reduce(BigDecimal.ZERO, BigDecimal::add));
        listBasicFileBaseService.updateSelective(base);
        return new Result<>(HttpStatus.OK.value(), null, 1);
    }

    @ApiOperation("删除（单场次，物理删除）")
    @PostMapping(value = "/deleteSingle")
    public Result<Integer> deleteSingle(@RequestParam(value = "id") Long id) {
        if (null == id) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正确！");
        }
        Integer result = listBasicFileDataMappingService.deleteMatchBasic(id);
        return new Result<>(HttpStatus.OK.value(), null, result);
    }

    private ListBasicFileDataMapping transformToMapping(ListMatchDataBasic listMatchDataBasic) {
        ListBasicFileDataMapping mapping = new ListBasicFileDataMapping();
        BeanUtils.copyProperties(listMatchDataBasic, mapping);
        mapping.setBaseId(listMatchDataBasic.getFileBaseId());
        if (null == mapping.getDurationM() && null == mapping.getDurationS()) {
            mapping.setDurationM(4); // 单场次默认FW 4分钟
            mapping.setDurationS(0);
        }
        mapping.init();
        return mapping;
    }

    @ApiOperation("单场次审核 - 认作者")
    @PostMapping(value = "/recognizedAuthorSingle")
    public Result<Integer> recognizedAuthorSingle(@RequestBody ListMatchDataBasic basic) {
        if (null == basic.getId()) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正确！");
        }
        if (null == basic.getMatchWorkTitle()) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "標題不能為空！");
        }
        ListMatchDataBasic update = new ListMatchDataBasic();
        update.setId(basic.getId());
        update.setStatus(3);
        update.setMatchWorkTitle(basic.getMatchWorkTitle());
        update.setMatchWorkAuthors(basic.getMatchWorkAuthors());
        update.setMatchWorkComposers(basic.getMatchWorkComposers());
        update.setExtJson(basic.getExtJson());
        update.init();
        Integer result = listMatchDataBasicService.updateSelective(update);
        return new Result<>(HttpStatus.OK.value(), null, result);
    }

    @ApiOperation(value = "获取History列表")
    @PostMapping("/getListMatchDataBasicMatchHistory")
    public Result<PageInfo<ListMatchDataBasicMatchHistory>> getListMatchDataBasicMatchHistory(@RequestBody ListMatchDataBasicMatchHistoryDto historyDto) {
        Page page = historyDto.getPage();
        if (null == page || null == page.getPageNum() || null == page.getPageSize()) {
            page = new Page(1, 10);
        }
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<ListMatchDataBasicMatchHistory> list = listMatchDataBasicMatchHistoryService.getList(historyDto.getListMatchDataBasicMatchHistory());
        return new Result(HttpStatus.OK.value(), null, new PageInfo<>(list));
    }

    @ApiOperation(value = "History修改")
    @PostMapping("/updateListMatchDataBasicListMatchHistory")
    public Result<Integer> updateListMatchDataBasicListMatchHistory(@RequestBody ListMatchDataBasicMatchHistory history) {
        log.info("updateListMatchDataBasicListMatchHistory,param:{}",history);
        if (Objects.isNull(history)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "刪除參數ID不能爲空！");
        }
        Long id = history.getId();
        if (Objects.isNull(id)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "修改參數ID不能爲空！");
        }
        if (Objects.isNull(history.getWorkId()) || Objects.isNull(history.getWorkSocietyCode())) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "MatchWorkId 和 MatchWorkSocietyCode 不能爲空！");
        }
        ListMatchDataBasicMatchHistory exist = listMatchDataBasicMatchHistoryService.getById(id);
        if (Objects.isNull(exist)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "修改參數ID的数据不存在！");
        }
        listMatchDataBasicMatchHistoryService.checkAndCompletionWork(exist, history,false);
        Integer update = listMatchDataBasicMatchHistoryService.updateSelective(exist);
        return new Result(HttpStatus.OK.value(), null, update);
    }

    @ApiOperation(value = "History修改")
    @PostMapping("/updateListMatchDataBasicListMatchHistoryNew")
    public Result<Integer> updateListMatchDataBasicListMatchHistoryNew(@RequestBody String param) {
        log.info("updateListMatchDataBasicListMatchHistoryNew,param:{}",param);
        if(StringUtils.isEmpty(param)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "参数不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(param) ;

        String scope = jsonObject.getString("scope") ;
        if(StringUtils.isEmpty(scope)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "scope不能为空！");
        }
        ListMatchDataBasicMatchHistory history = jsonObject.getObject("history",ListMatchDataBasicMatchHistory.class);
        if (Objects.isNull(history)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "刪除參數ID不能爲空！");
        }
        Long id = history.getId();
        if (Objects.isNull(id)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "修改參數ID不能爲空！");
        }
        if (StringUtils.isEmpty(history.getUniqueKeyMd5())) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "unique Key Md5 不能爲空！");
        }
        if (Objects.isNull(history.getWorkId()) || Objects.isNull(history.getWorkSocietyCode())) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "WorkId 和 WorkSocietyCode 不能爲空！");
        }

        Integer update = listMatchDataBasicService.updateListMatchDataBasicListMatchHistory(history,scope);
        return new Result(HttpStatus.OK.value(), null, update);
    }

    @ApiOperation(value = "History批量修改")
    @PostMapping("/updateListMatchDataBasicListMatchHistoryBatch")
    public Result<Integer> updateListMatchDataBasicListMatchHistoryBatch(@RequestBody String param) {
        log.info("updateListMatchDataBasicListMatchHistoryBatch,param:{}",param);
        if(StringUtils.isEmpty(param)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "参数不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(param) ;

        String scope = jsonObject.getString("scope") ;
        if(StringUtils.isEmpty(scope)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "scope不能为空！");
        }
        Long matchWorkId = jsonObject.getLong("matchWorkId") ;
        Integer matchWorkSoc = jsonObject.getInteger("matchWorkSoc");
        if (matchWorkId == null || matchWorkSoc == null) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "MatchWorkId 和 MatchWorkSocietyCode 不能爲空！");
        }

        JSONArray jsonArray = jsonObject.getJSONArray("dataUniqueKeys");
        List<String> dataUniqueKeys = jsonArray.toJavaList(String.class);
        if(CollectionUtils.isEmpty(dataUniqueKeys)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "Data unique key 不能爲空！");
        }

        Integer update = listMatchDataBasicService.updateListMatchDataBasicListMatchHistoryBatch(dataUniqueKeys,matchWorkId, matchWorkSoc ,scope);
        return new Result(HttpStatus.OK.value(), null, update);
    }

    @ApiOperation(value = "History删除")
    @DeleteMapping("/deleteListMatchDataBasicListMatchHistory/{id}")
    public Result<Integer> deleteListMatchDataBasicListMatchHistory(@PathVariable("id") Long id) {
        if (Objects.isNull(id)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "刪除參數ID不能爲空！");
        }
        Integer delete = listMatchDataBasicMatchHistoryService.delete(id);
        return new Result<>(HttpStatus.OK.value(), null, delete);
    }

}
