package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_autopay_soc_retain`")
public class DistAutopaySocRetain extends BaseEntity {

    /**
     * 协会编码
     */
    @Column(name = "`society_code`")
    private Integer societyCode;

    /**
     * 协会名称
     */
    @Column(name = "`society_name`")
    private String societyName;

    /**
     * 支付金额
     */
    @Column(name = "`pay_amount`")
    private BigDecimal payAmount;

    /**
     * 国家编码
     */
    @Column(name = "`country_code`")
    private String countryCode;

    /**
     * 国家名称
     */
    @Column(name = "`country_name`")
    private String countryName;

    /**
     * Y是否支付
     */
    @Column(name = "`sd`")
    private String sd;

    @Column(name = "`total_pay_amount`")
    private BigDecimal totalPayAmount;

    public Integer getSocietyCode() {
        return societyCode;
    }

    public void setSocietyCode(Integer societyCode) {
        this.societyCode = societyCode;
    }

    public String getSocietyName() {
        return societyName;
    }

    public void setSocietyName(String societyName) {
        this.societyName = societyName;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public BigDecimal getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(BigDecimal totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }
}