package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;
@Data
public class LI0103 extends BaseTsvEntity {
    private String BlockId;
    private String SummaryRecordId;
    private String RightsController;
    private String RightsControllerPartyId;
    private String RightsControllerWorkId;
    private String RightShare;
    private String RightsType;
    private String AllocatedNetRevenue;
    private String AllocatedAmount;
    private String AllocatedUsages;
    private String ParentSalesTransactionId;
    private String LicensorDataRecordId;

    public String getBlockId() {
        return BlockId;
    }

    public void setBlockId(String blockId) {
        BlockId = blockId;
    }

    public String getSummaryRecordId() {
        return SummaryRecordId;
    }

    public void setSummaryRecordId(String summaryRecordId) {
        SummaryRecordId = summaryRecordId;
    }

    public String getRightsController() {
        return RightsController;
    }

    public void setRightsController(String rightsController) {
        RightsController = rightsController;
    }

    public String getRightsControllerPartyId() {
        return RightsControllerPartyId;
    }

    public void setRightsControllerPartyId(String rightsControllerPartyId) {
        RightsControllerPartyId = rightsControllerPartyId;
    }

    public String getRightsControllerWorkId() {
        return RightsControllerWorkId;
    }

    public void setRightsControllerWorkId(String rightsControllerWorkId) {
        RightsControllerWorkId = rightsControllerWorkId;
    }

    public String getRightShare() {
        return RightShare;
    }

    public void setRightShare(String rightShare) {
        RightShare = rightShare;
    }

    public String getRightsType() {
        return RightsType;
    }

    public void setRightsType(String rightsType) {
        RightsType = rightsType;
    }

    public String getAllocatedNetRevenue() {
        return AllocatedNetRevenue;
    }

    public void setAllocatedNetRevenue(String allocatedNetRevenue) {
        AllocatedNetRevenue = allocatedNetRevenue;
    }

    public String getAllocatedAmount() {
        return AllocatedAmount;
    }

    public void setAllocatedAmount(String allocatedAmount) {
        AllocatedAmount = allocatedAmount;
    }

    public String getAllocatedUsages() {
        return AllocatedUsages;
    }

    public void setAllocatedUsages(String allocatedUsages) {
        AllocatedUsages = allocatedUsages;
    }

    public String getParentSalesTransactionId() {
        return ParentSalesTransactionId;
    }

    public void setParentSalesTransactionId(String parentSalesTransactionId) {
        ParentSalesTransactionId = parentSalesTransactionId;
    }

    public String getLicensorDataRecordId() {
        return LicensorDataRecordId;
    }

    public void setLicensorDataRecordId(String licensorDataRecordId) {
        LicensorDataRecordId = licensorDataRecordId;
    }
}
