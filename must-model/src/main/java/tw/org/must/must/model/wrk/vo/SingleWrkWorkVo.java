package tw.org.must.must.model.wrk.vo;

public class SingleWrkWorkVo {
	
	private String title;
	
	private String titleLanguage;
	
	private String genre;
	
	private String performLanguage;
	
	private Integer durationM;
	
	private Integer durationS;
	
	private String workType;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTitleLanguage() {
		return titleLanguage;
	}

	public void setTitleLanguage(String titleLanguage) {
		this.titleLanguage = titleLanguage;
	}

	public String getGenre() {
		return genre;
	}

	public void setGenre(String genre) {
		this.genre = genre;
	}

	public String getPerformLanguage() {
		return performLanguage;
	}

	public void setPerformLanguage(String performLanguage) {
		this.performLanguage = performLanguage;
	}

	public Integer getDurationM() {
		return durationM;
	}

	public void setDurationM(Integer durationM) {
		this.durationM = durationM;
	}

	public Integer getDurationS() {
		return durationS;
	}

	public void setDurationS(Integer durationS) {
		this.durationS = durationS;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}
}
