package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

@Table(name = "`dist_calc_retain`")
public class DistCalcRetain extends BaseEntity {

	/**
	 * 分配編號
	 */
	@Column(name = "`dist_no`")
	private String distNo;

	/**
	 * 0，狀態，distribtion check，1.commit，2分配成功
	 */
	@Column(name = "`status`")
	private Integer status;

	/**
	 * 
	 */
	@Column(name = "`ip_base_no`")
	private String ipBaseNo;

	/**
	 * 
	 */
	@Column(name = "`ref_ip_base_no`")
	private String refIpBaseNo;

	/**
	 * name type=PA的name no
	 */
	@Column(name = "`pa_name_no`")
	private String paNameNo;

	/**
	 * 
	 */
	@Column(name = "`name_no`")
	private String nameNo;

	/**
	 * ip英文名
	 */
	@Column(name = "`ip_name`")
	private String ipName;

	/**
	 * ip中文名
	 */
	@Column(name = "`ip_chinese_name`")
	private String ipChineseName;

	/**
	 * 繼承人名稱，如果是團體，填寫註銷公司後新公司名稱
	 */
	@Column(name = "`successor_name`")
	private String successorName;

	/**
	 * L（圖體）\N（個人）\S(協會)
	 */
	@Column(name = "`ip_type`")
	private String ipType;

	@Column(name = "`roy_ext_json`")
	private String royExtJson;



	/**
	 * tv類金額,對應list_file_type的tv
	 */
	@Column(name = "`tv_roy`")
	private BigDecimal tvRoy;

	/**
	 * 音樂會類金額，對應list_file_type的concert
	 */
	@Column(name = "`concert_roy`")
	private BigDecimal concertRoy;

	/**
	 * 對應list_file_type的Karaoke\film
	 */
	@Column(name = "`karaoke_film_roy`")
	private BigDecimal karaokeFilmRoy;

	/**
	 * 對應list_file_type的radio
	 */
	@Column(name = "`radio_roy`")
	private BigDecimal radioRoy;

	/**
	 * 對應list_file_type的airline
	 */
	@Column(name = "`airline_roy`")
	private BigDecimal airlineRoy;

	/**
	 * 對應list_file_type的公傳+其他（download、public transmission、others）
	 */
	@Column(name = "`other_performance_roy`")
	private BigDecimal otherPerformanceRoy;

	/**
	 * tv+concert+karaoke+radio+airline+other
	 */
	@Column(name = "`sub_total_roy`")
	private BigDecimal subTotalRoy;

	/**
	 * UPA費用
	 */
	@Column(name = "`upa_amt`")
	private BigDecimal upaAmt;

	/**
	 * tv類調整
	 */
	@Column(name = "`adj_tv_roy`")
	private BigDecimal adjTvRoy;

	/**
	 *
	 */
	@Column(name = "`adj_concert_roy`")
	private BigDecimal adjConcertRoy;

	/**
	 *
	 */
	@Column(name = "`adj_karaoke_roy`")
	private BigDecimal adjKaraokeRoy;

	/**
	 *
	 */
	@Column(name = "`adj_radio_roy`")
	private BigDecimal adjRadioRoy;

	/**
	 *
	 */
	@Column(name = "`adj_airline_roy`")
	private BigDecimal adjAirlineRoy;

	/**
	 *
	 */
	@Column(name = "`adj_other_performance_roy`")
	private BigDecimal adjOtherPerformanceRoy;

	/**
	 * sub_total_roy+adj的费用
	 */
	@Column(name = "`adj_sub_total_roy`")
	private BigDecimal adjSubTotalRoy;

	/**
	 * 權利金總額
	 */
	@Column(name = "`roy_adj_total`")
	private BigDecimal royAdjTotal;

	/**
	 * ？待确定
	 */
	@Column(name = "`transaction_taxable_income`")
	private BigDecimal transactionTaxableIncome;

	/**
	 * 个人所得税率
	 */
	@Column(name = "`tax_rate`")
	private BigDecimal taxRate;

	/**
	 * 税额=adj_sub_total_roy * tax_rate /100
	 */
	@Column(name = "`withheld_tax`")
	private BigDecimal withheldTax;

	/**
	 * 调整税额
	 */
	@Column(name = "`revised_withheld_tax`")
	private BigDecimal revisedWithheldTax;

	/**
	 * 基础管理费比例
	 */
	@Column(name = "`basic_commission_rate`")
	private BigDecimal basicCommissionRate;

	/**
	 * 基礎管理費
	 */
	@Column(name = "`basic_commission_amt`")
	private BigDecimal basicCommissionAmt;

	/**
	 * 准会员补充管理費比例
	 */
	@Column(name = "`additional_commission_rate`")
	private BigDecimal additionalCommissionRate;

	/**
	 * 准会员补充管理費金額
	 */
	@Column(name = "`additional_commission_amt`")
	private BigDecimal additionalCommissionAmt;

	/**
	 * 调整後管理費
	 */
	@Column(name = "`revised_commission_amt`")
	private BigDecimal revisedCommissionAmt;

	/**
	 * 總管理費
	 */
	@Column(name = "`total_commission_amt`")
	private BigDecimal totalCommissionAmt;

	/**
	 * 加上管理费后的小计金额
	 */
	@Column(name = "`commission_sub_total_roy`")
	private BigDecimal commissionSubTotalRoy;

	/**
	 * 企業稅率只有企业有
	 */
	@Column(name = "`sales_tax_rate`")
	private BigDecimal salesTaxRate;

	/**
	 * 企業稅費
	 */
	@Column(name = "`sales_tax`")
	private BigDecimal salesTax;

	/**
	 * 保留款，
	 */
	@Column(name = "`deduction`")
	private BigDecimal deduction;

	/**
	 * 海外协会会员收费比例
	 */
	@Column(name = "`reciprocal_rate`")
	private BigDecimal reciprocalRate;

	/**
	 * 海外协会会员收费金额
	 */
	@Column(name = "`reciprocal_amt`")
	private BigDecimal reciprocalAmt;

	/**
	 * 淨付款金額
	 */
	@Column(name = "`net_payment`")
	private BigDecimal netPayment;

	/**
	 * 付款描述
	 */
	@Column(name = "`payment_desc`")
	private String paymentDesc;

	/**
	 * 支付方式
	 */
	@Column(name = "`payable_payment_method`")
	private String payablePaymentMethod;

	/**
	 * 支付貨幣
	 */
	@Column(name = "`payable_currency_code`")
	private String payableCurrencyCode;

	/**
	 * 支付類別，支票、其他、具體支付銀行
	 */
	@Column(name = "`payable_type`")
	private String payableType;

	/**
	 * 會員編號
	 */
	@Column(name = "`member_no`")
	private String memberNo;

	/**
	 * 支付賬戶名稱
	 */
	@Column(name = "`account_name`")
	private String accountName;

	/**
	 * 企業稅號
	 */
	@Column(name = "`dist_br_no`")
	private String distBrNo;

	/**
	 * 發票編號
	 */
	@Column(name = "`inv_no`")
	private String invNo;

	@Column(name = "is_pay")
	private String isPay;

	@Column(name = "society_code")
	private Integer societyCode;
	@Column(name = "society_name")
	private String societyName;

	/**
	 * 用于个人会员。协会和团体会员不适用
	 * 	TaxType		TaxType Description  		会籍		Recipient的tax type			special exchange rate		Tax rate稅率計算
	 * 	   1		本國人						台湾			空白												    0%;分配金额>2萬，10%
	 * 	   2		本國人(Overseas)			    台湾		Natural Person(Overseas)								稅率配置.Overseas(%)
	 * 	   3		本國人(SpecailOverseas)		台湾		Natural Person(Overseas)			V					special exchange rate的country税率
	 * 	   4		外國人						海外			空白												    税率配置.0verseas(%)
	 * 	   5		外國人(specail)				海外			空白							    V					special exchange rate的country税率
	 * 	   6		外國人(Local)				海外		Natural Person(Local)									0%;分配金额>2萬，10%
	 *
	 */
	@Column(name = "tax_type")
	private Integer taxType;

	@Transient
	private String statusStr;
	@Transient
	private String typeStr;
	@Transient
	private String ipChineseNameStr;
	@Transient
	private String ipNameStr;
	@Transient
	private String MustCurrentSuccessorName;
	@Transient
	private String MustCurrentSuccessorToWithdrawalPublisher;
	@Transient
	private String accountNameStr;

	public String getTypeStr() {
		return typeStr;
	}

	public void setTypeStr(String typeStr) {
		this.typeStr = typeStr;
	}

	public String getIsPay() {
		return isPay;
	}

	public void setIsPay(String isPay) {
		this.isPay = isPay;
	}

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getIpBaseNo() {
		return ipBaseNo;
	}

	public void setIpBaseNo(String ipBaseNo) {
		this.ipBaseNo = ipBaseNo;
	}

	public String getRefIpBaseNo() {
		return refIpBaseNo;
	}

	public void setRefIpBaseNo(String refIpBaseNo) {
		this.refIpBaseNo = refIpBaseNo;
	}

	public String getPaNameNo() {
		return paNameNo;
	}

	public void setPaNameNo(String paNameNo) {
		this.paNameNo = paNameNo;
	}

	public String getNameNo() {
		return nameNo;
	}

	public void setNameNo(String nameNo) {
		this.nameNo = nameNo;
	}

	public String getIpName() {
		return ipName;
	}

	public void setIpName(String ipName) {
		this.ipName = ipName;
	}

	public String getIpChineseName() {
		return ipChineseName;
	}

	public void setIpChineseName(String ipChineseName) {
		this.ipChineseName = ipChineseName;
	}

	public String getSuccessorName() {
		return successorName;
	}

	public void setSuccessorName(String successorName) {
		this.successorName = successorName;
	}

	public String getIpType() {
		return ipType;
	}

	public void setIpType(String ipType) {
		this.ipType = ipType;
	}

	public BigDecimal getTvRoy() {
		return tvRoy;
	}

	public void setTvRoy(BigDecimal tvRoy) {
		this.tvRoy = tvRoy;
	}

	public BigDecimal getConcertRoy() {
		return concertRoy;
	}

	public void setConcertRoy(BigDecimal concertRoy) {
		this.concertRoy = concertRoy;
	}

	public BigDecimal getKaraokeFilmRoy() {
		return karaokeFilmRoy;
	}

	public void setKaraokeFilmRoy(BigDecimal karaokeFilmRoy) {
		this.karaokeFilmRoy = karaokeFilmRoy;
	}

	public BigDecimal getRadioRoy() {
		return radioRoy;
	}

	public void setRadioRoy(BigDecimal radioRoy) {
		this.radioRoy = radioRoy;
	}

	public BigDecimal getAirlineRoy() {
		return airlineRoy;
	}

	public void setAirlineRoy(BigDecimal airlineRoy) {
		this.airlineRoy = airlineRoy;
	}

	public BigDecimal getOtherPerformanceRoy() {
		return otherPerformanceRoy;
	}

	public void setOtherPerformanceRoy(BigDecimal otherPerformanceRoy) {
		this.otherPerformanceRoy = otherPerformanceRoy;
	}

	public BigDecimal getSubTotalRoy() {
		return subTotalRoy;
	}

	public void setSubTotalRoy(BigDecimal subTotalRoy) {
		this.subTotalRoy = subTotalRoy;
	}

	public BigDecimal getUpaAmt() {
		return upaAmt;
	}

	public void setUpaAmt(BigDecimal upaAmt) {
		this.upaAmt = upaAmt;
	}

	public BigDecimal getAdjTvRoy() {
		return adjTvRoy;
	}

	public void setAdjTvRoy(BigDecimal adjTvRoy) {
		this.adjTvRoy = adjTvRoy;
	}

	public BigDecimal getAdjConcertRoy() {
		return adjConcertRoy;
	}

	public void setAdjConcertRoy(BigDecimal adjConcertRoy) {
		this.adjConcertRoy = adjConcertRoy;
	}

	public BigDecimal getAdjKaraokeRoy() {
		return adjKaraokeRoy;
	}

	public void setAdjKaraokeRoy(BigDecimal adjKaraokeRoy) {
		this.adjKaraokeRoy = adjKaraokeRoy;
	}

	public BigDecimal getAdjRadioRoy() {
		return adjRadioRoy;
	}

	public void setAdjRadioRoy(BigDecimal adjRadioRoy) {
		this.adjRadioRoy = adjRadioRoy;
	}

	public BigDecimal getAdjAirlineRoy() {
		return adjAirlineRoy;
	}

	public void setAdjAirlineRoy(BigDecimal adjAirlineRoy) {
		this.adjAirlineRoy = adjAirlineRoy;
	}

	public BigDecimal getAdjOtherPerformanceRoy() {
		return adjOtherPerformanceRoy;
	}

	public void setAdjOtherPerformanceRoy(BigDecimal adjOtherPerformanceRoy) {
		this.adjOtherPerformanceRoy = adjOtherPerformanceRoy;
	}

	public BigDecimal getAdjSubTotalRoy() {
		return adjSubTotalRoy;
	}

	public void setAdjSubTotalRoy(BigDecimal adjSubTotalRoy) {
		this.adjSubTotalRoy = adjSubTotalRoy;
	}

	public BigDecimal getTransactionTaxableIncome() {
		return transactionTaxableIncome;
	}

	public void setTransactionTaxableIncome(BigDecimal transactionTaxableIncome) {
		this.transactionTaxableIncome = transactionTaxableIncome;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public BigDecimal getWithheldTax() {
		return withheldTax;
	}

	public void setWithheldTax(BigDecimal withheldTax) {
		this.withheldTax = withheldTax;
	}

	public BigDecimal getRevisedWithheldTax() {
		return revisedWithheldTax;
	}

	public void setRevisedWithheldTax(BigDecimal revisedWithheldTax) {
		this.revisedWithheldTax = revisedWithheldTax;
	}

	public BigDecimal getBasicCommissionRate() {
		return basicCommissionRate;
	}

	public void setBasicCommissionRate(BigDecimal basicCommissionRate) {
		this.basicCommissionRate = basicCommissionRate;
	}

	public BigDecimal getBasicCommissionAmt() {
		return basicCommissionAmt;
	}

	public void setBasicCommissionAmt(BigDecimal basicCommissionAmt) {
		this.basicCommissionAmt = basicCommissionAmt;
	}

	public BigDecimal getAdditionalCommissionRate() {
		return additionalCommissionRate;
	}

	public void setAdditionalCommissionRate(BigDecimal additionalCommissionRate) {
		this.additionalCommissionRate = additionalCommissionRate;
	}

	public BigDecimal getAdditionalCommissionAmt() {
		return additionalCommissionAmt;
	}

	public void setAdditionalCommissionAmt(BigDecimal additionalCommissionAmt) {
		this.additionalCommissionAmt = additionalCommissionAmt;
	}

	public BigDecimal getRevisedCommissionAmt() {
		return revisedCommissionAmt;
	}

	public void setRevisedCommissionAmt(BigDecimal revisedCommissionAmt) {
		this.revisedCommissionAmt = revisedCommissionAmt;
	}

	public BigDecimal getTotalCommissionAmt() {
		return totalCommissionAmt;
	}

	public void setTotalCommissionAmt(BigDecimal totalCommissionAmt) {
		this.totalCommissionAmt = totalCommissionAmt;
	}

	public BigDecimal getCommissionSubTotalRoy() {
		return commissionSubTotalRoy;
	}

	public void setCommissionSubTotalRoy(BigDecimal commissionSubTotalRoy) {
		this.commissionSubTotalRoy = commissionSubTotalRoy;
	}

	public BigDecimal getSalesTaxRate() {
		return salesTaxRate;
	}

	public void setSalesTaxRate(BigDecimal salesTaxRate) {
		this.salesTaxRate = salesTaxRate;
	}

	public BigDecimal getSalesTax() {
		return salesTax;
	}

	public void setSalesTax(BigDecimal salesTax) {
		this.salesTax = salesTax;
	}

	public BigDecimal getDeduction() {
		return deduction;
	}

	public void setDeduction(BigDecimal deduction) {
		this.deduction = deduction;
	}

	public BigDecimal getReciprocalRate() {
		return reciprocalRate;
	}

	public void setReciprocalRate(BigDecimal reciprocalRate) {
		this.reciprocalRate = reciprocalRate;
	}

	public BigDecimal getReciprocalAmt() {
		return reciprocalAmt;
	}

	public void setReciprocalAmt(BigDecimal reciprocalAmt) {
		this.reciprocalAmt = reciprocalAmt;
	}

	public BigDecimal getNetPayment() {
		return netPayment;
	}

	public void setNetPayment(BigDecimal netPayment) {
		this.netPayment = netPayment;
	}

	public String getPaymentDesc() {
		return paymentDesc;
	}

	public void setPaymentDesc(String paymentDesc) {
		this.paymentDesc = paymentDesc;
	}

	public String getPayablePaymentMethod() {
		return payablePaymentMethod;
	}

	public void setPayablePaymentMethod(String payablePaymentMethod) {
		this.payablePaymentMethod = payablePaymentMethod;
	}

	public String getPayableCurrencyCode() {
		return payableCurrencyCode;
	}

	public void setPayableCurrencyCode(String payableCurrencyCode) {
		this.payableCurrencyCode = payableCurrencyCode;
	}

	public String getPayableType() {
		return payableType;
	}

	public void setPayableType(String payableType) {
		this.payableType = payableType;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getDistBrNo() {
		return distBrNo;
	}

	public void setDistBrNo(String distBrNo) {
		this.distBrNo = distBrNo;
	}

	public String getInvNo() {
		return invNo;
	}

	public void setInvNo(String invNo) {
		this.invNo = invNo;
	}

	public Integer getSocietyCode() {
		return societyCode;
	}

	public void setSocietyCode(Integer societyCode) {
		this.societyCode = societyCode;
	}

	public String getSocietyName() {
		return societyName;
	}

	public void setSocietyName(String societyName) {
		this.societyName = societyName;
	}

	public String getStatusStr() {
		return statusStr;
	}

	public void setStatusStr(String statusStr) {
		this.statusStr = statusStr;
	}

	public String getIpChineseNameStr() {
		return ipChineseNameStr;
	}

	public void setIpChineseNameStr(String ipChineseNameStr) {
		this.ipChineseNameStr = ipChineseNameStr;
	}

	public String getIpNameStr() {
		return ipNameStr;
	}

	public void setIpNameStr(String ipNameStr) {
		this.ipNameStr = ipNameStr;
	}

	public String getMustCurrentSuccessorName() {
		return MustCurrentSuccessorName;
	}

	public void setMustCurrentSuccessorName(String mustCurrentSuccessorName) {
		MustCurrentSuccessorName = mustCurrentSuccessorName;
	}

	public String getMustCurrentSuccessorToWithdrawalPublisher() {
		return MustCurrentSuccessorToWithdrawalPublisher;
	}

	public void setMustCurrentSuccessorToWithdrawalPublisher(
			String mustCurrentSuccessorToWithdrawalPublisher) {
		MustCurrentSuccessorToWithdrawalPublisher = mustCurrentSuccessorToWithdrawalPublisher;
	}

	public String getAccountNameStr() {
		return accountNameStr;
	}

	public void setAccountNameStr(String accountNameStr) {
		this.accountNameStr = accountNameStr;
	}

	public String getRoyExtJson() {
		return royExtJson;
	}

	public void setRoyExtJson(String royExtJson) {
		this.royExtJson = royExtJson;
	}

	public BigDecimal getRoyAdjTotal() {
		return royAdjTotal;
	}

	public void setRoyAdjTotal(BigDecimal royAdjTotal) {
		this.royAdjTotal = royAdjTotal;
	}

	public Integer getTaxType() {
		return taxType;
	}

	public void setTaxType(Integer taxType) {
		this.taxType = taxType;
	}
}