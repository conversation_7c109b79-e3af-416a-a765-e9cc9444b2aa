package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;

import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_roy_soc_tran_dtl`")
public class DistRoySocTranDtl extends BaseEntity {

    /**
     * 分配編號
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * soc no
     */
    @Column(name = "`society_code`")
    private String societyCode;

    /**
     * 
     */
    @Column(name = "`society_name`")
    private String societyName;

    /**
     * 描述
     */
    @Column(name = "`tran_descpt`")
    private String tranDescpt;

    /**
     * tran 費用
     */
    @Column(name = "`tran_amt`")
    private BigDecimal tranAmt;

    /**
     * 對應ref_tran_type表值
     */
    @Column(name = "`tran_type`")
    private Integer tranType;

    /**
     * 是否扣稅標識，N or Y
     */
    @Column(name = "`withheld_tax_ind`")
    private String withheldTaxInd;

    /**
     * 是否重新計算稅率直接更改，對應界面中的deduction
     */
    @Column(name = "`non_taxable_income_ind`")
    private String nonTaxableIncomeInd;

    /**
     * 待定，不知道後續用途
     */
    @Column(name = "`add_after_dist_ind`")
    private String addAfterDistInd;

    /**
     * 
     */
    @Column(name = "`auto_ind`")
    private String autoInd;

    /**
     * 
     */
    @Column(name = "`commission_ind`")
    private String commissionInd;

    /**
     * 
     */
    @Column(name = "`tran_item_type`")
    private String tranItemType;

    /**
     * 未知用途，先待定
     */
    @Column(name = "`comm_rate`")
    private BigDecimal commRate;

    /**
     * 未知用途，先待定
     */
    @Column(name = "`prg_gen`")
    private String prgGen;

    @Transient
    private String payStatus;

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getSocietyCode() {
        return societyCode;
    }

    public void setSocietyCode(String societyCode) {
        this.societyCode = societyCode;
    }

    public String getSocietyName() {
        return societyName;
    }

    public void setSocietyName(String societyName) {
        this.societyName = societyName;
    }

    public String getTranDescpt() {
        return tranDescpt;
    }

    public void setTranDescpt(String tranDescpt) {
        this.tranDescpt = tranDescpt;
    }

    public BigDecimal getTranAmt() {
        return tranAmt;
    }

    public void setTranAmt(BigDecimal tranAmt) {
        this.tranAmt = tranAmt;
    }

    public Integer getTranType() {
        return tranType;
    }

    public void setTranType(Integer tranType) {
        this.tranType = tranType;
    }

    public String getWithheldTaxInd() {
        return withheldTaxInd;
    }

    public void setWithheldTaxInd(String withheldTaxInd) {
        this.withheldTaxInd = withheldTaxInd;
    }

    public String getNonTaxableIncomeInd() {
        return nonTaxableIncomeInd;
    }

    public void setNonTaxableIncomeInd(String nonTaxableIncomeInd) {
        this.nonTaxableIncomeInd = nonTaxableIncomeInd;
    }

    public String getAddAfterDistInd() {
        return addAfterDistInd;
    }

    public void setAddAfterDistInd(String addAfterDistInd) {
        this.addAfterDistInd = addAfterDistInd;
    }

    public String getAutoInd() {
        return autoInd;
    }

    public void setAutoInd(String autoInd) {
        this.autoInd = autoInd;
    }

    public String getCommissionInd() {
        return commissionInd;
    }

    public void setCommissionInd(String commissionInd) {
        this.commissionInd = commissionInd;
    }

    public String getTranItemType() {
        return tranItemType;
    }

    public void setTranItemType(String tranItemType) {
        this.tranItemType = tranItemType;
    }

    public BigDecimal getCommRate() {
        return commRate;
    }

    public void setCommRate(BigDecimal commRate) {
        this.commRate = commRate;
    }

    public String getPrgGen() {
        return prgGen;
    }

    public void setPrgGen(String prgGen) {
        this.prgGen = prgGen;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }
}