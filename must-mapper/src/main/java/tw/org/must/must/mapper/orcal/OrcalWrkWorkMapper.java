package tw.org.must.must.mapper.orcal;

import java.util.List;

import org.springframework.stereotype.Repository;

import tw.org.must.must.model.orcal.OrcalWrkWork;

@Repository
public interface OrcalWrkWorkMapper {

	List<OrcalWrkWork> selectByDate(String lastAmendTime);

	List<OrcalWrkWork> selectByDateByRowNum(String lastAmendTime, int start, int end);
	
	List<OrcalWrkWork> selectWrkByTest();

    OrcalWrkWork getOrcalWrkWorkByWorkNumAndSoc(Long sourceId, Integer work_source_no_society);

	List<OrcalWrkWork> selectWorknumAndSociety(int start, int end);

	List<OrcalWrkWork> selectByDateByRowNumDay(String startDate, String endDate, int start, int end);

	List<OrcalWrkWork> selectByDateAndSocByRowNum(String lastAmendTime, Integer soc, int start, int end);
}
