package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;

@Data
public class SY0402 extends BaseTsvEntity {

    private String SummaryRecordId;
    private String DistributionChannel;
    private String DistributionChannelDPID;
    private String CommercialModel;
    private String UseType;
    private String Territory;
    private String ServiceDescription;
    private String SubscriberType;
    private String Subscribers;
    private String SubPeriodStartDate;
    private String SubPeriodEndDate;
    private String TotalUsagesInSubPeriod;
    private String TotalUsagesInReportingPeriod;
    private String CurrencyOfReporting;
    private String CurrencyOfTransaction;
    private String ExchangeRate;
    private String ConsumerPaidUnitPrice;
    private String NetRevenue;
    private String MusicUsagePercentage;

    private String net;
}
