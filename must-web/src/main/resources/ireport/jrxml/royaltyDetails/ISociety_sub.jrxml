<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PPersonal_sub" pageWidth="940" pageHeight="842" columnWidth="600" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d2a213c6-641d-4c51-899c-ec4ad88834aa">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="ipName" class="java.lang.String"/>
	<field name="ipNameNo" class="java.lang.String"/>
	<field name="share" class="java.lang.String"/>
	<field name="society" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="tatol" class="java.lang.String"/>
	<field name="tv" class="java.lang.String"/>
	<field name="radio" class="java.lang.String"/>
	<field name="concert" class="java.lang.String"/>
	<field name="karaoke" class="java.lang.String"/>
	<field name="airline" class="java.lang.String"/>
	<field name="others" class="java.lang.String"/>
	<field name="total" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="26" splitType="Stretch">
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="ContainerHeight" x="0" y="0" width="210" height="20" uuid="eb8fdfed-a48f-46f8-8644-f194ca83d568">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="8" pdfEncoding="UniGB-UCS2-V"/>
					<paragraph lineSpacing="AtLeast"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="0" width="50" height="20" uuid="990b4112-73d8-42a1-949d-5f8ce04b1e56">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="275" y="0" width="90" height="20" uuid="5f947b0d-0d6e-46b7-86c2-aedf75d75063">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipNameNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="370" y="0" width="45" height="20" uuid="48d25d26-3f50-46c3-88e7-9c3241e377c5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{society}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="0" width="70" height="19" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{share}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="500" y="0" width="60" height="20" isRemoveLineWhenBlank="true" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tv}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="560" y="0" width="60" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{radio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="620" y="0" width="60" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{concert}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="0" width="60" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{karaoke}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="740" y="0" width="60" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{airline}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="800" y="0" width="60" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{others}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="860" y="0" width="80" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
