package tw.org.must.must.mapper.dist;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.dist.DistAutopayNetPaySociety;
import tw.org.must.must.model.dist.vo.v800.Detail800;
import tw.org.must.must.model.dist.vo.v800.DistNoDetail800;

import java.util.List;

@Repository
public interface DistAutopayNetPaySocietyMapper extends BaseMapper<DistAutopayNetPaySociety> {

    List<DistAutopayNetPaySociety> selectBySocietyCodes(@Param("list") List<Integer> societyCodes);
    List<Detail800> getDetail800(@Param("autopayNo") String autopayNo, @Param("societyCode") String societyCode, @Param("distNo") String distNo);
    List<DistNoDetail800> getDistNoDetail800(@Param("autopayNo") String autopayNo, @Param("societyCode") String societyCode, @Param("distNo") String distNo);
}