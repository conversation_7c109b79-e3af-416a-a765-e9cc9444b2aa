package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;


public class Sy0401 extends BaseTsvEntity{


	
	private String SummaryRecordId;	
	
	private String DistributionChannel;
	
	private String DistributionChannelDPID;	
	
	private String CommercialModel;	
    
	private String UseType;
	
	private String Territory;
	
	private String ServiceDescription;
	
	private String SubscriberType;
	
	private String Subscribers;
	
	private String SubPeriodStartDate;
	
	private String SubPeriodEndDate;
	
	private String UsagesInSubPeriod;
	
	private String UsagesInReportingPeriod;
	
	private String Currency;
	
	private String ExchangeRateBaseCurrency;
    
	private String ExchangeRate;
	
	private String ConsumerPaidUnitPrice;
	
	private String Net;
	
	private String MusicUsagePercentage;



	public String getSummaryRecordId() {
		return SummaryRecordId;
	}

	public void setSummaryRecordId(String summaryRecordId) {
		SummaryRecordId = summaryRecordId;
	}

	public String getDistributionChannel() {
		return DistributionChannel;
	}

	public void setDistributionChannel(String distributionChannel) {
		DistributionChannel = distributionChannel;
	}

	public String getDistributionChannelDPID() {
		return DistributionChannelDPID;
	}

	public void setDistributionChannelDPID(String distributionChannelDPID) {
		DistributionChannelDPID = distributionChannelDPID;
	}

	public String getCommercialModel() {
		return CommercialModel;
	}

	public void setCommercialModel(String commercialModel) {
		CommercialModel = commercialModel;
	}

	public String getUseType() {
		return UseType;
	}

	public void setUseType(String useType) {
		UseType = useType;
	}

	public String getTerritory() {
		return Territory;
	}

	public void setTerritory(String territory) {
		Territory = territory;
	}

	public String getServiceDescription() {
		return ServiceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		ServiceDescription = serviceDescription;
	}

	public String getSubscriberType() {
		return SubscriberType;
	}

	public void setSubscriberType(String subscriberType) {
		SubscriberType = subscriberType;
	}

	public String getSubscribers() {
		return Subscribers;
	}

	public void setSubscribers(String subscribers) {
		Subscribers = subscribers;
	}

	public String getSubPeriodStartDate() {
		return SubPeriodStartDate;
	}

	public void setSubPeriodStartDate(String subPeriodStartDate) {
		SubPeriodStartDate = subPeriodStartDate;
	}

	public String getSubPeriodEndDate() {
		return SubPeriodEndDate;
	}

	public void setSubPeriodEndDate(String subPeriodEndDate) {
		SubPeriodEndDate = subPeriodEndDate;
	}

	public String getUsagesInSubPeriod() {
		return UsagesInSubPeriod;
	}

	public void setUsagesInSubPeriod(String usagesInSubPeriod) {
		UsagesInSubPeriod = usagesInSubPeriod;
	}

	public String getUsagesInReportingPeriod() {
		return UsagesInReportingPeriod;
	}

	public void setUsagesInReportingPeriod(String usagesInReportingPeriod) {
		UsagesInReportingPeriod = usagesInReportingPeriod;
	}

	public String getCurrency() {
		return Currency;
	}

	public void setCurrency(String currency) {
		Currency = currency;
	}

	public String getExchangeRateBaseCurrency() {
		return ExchangeRateBaseCurrency;
	}

	public void setExchangeRateBaseCurrency(String exchangeRateBaseCurrency) {
		ExchangeRateBaseCurrency = exchangeRateBaseCurrency;
	}

	public String getExchangeRate() {
		return ExchangeRate;
	}

	public void setExchangeRate(String exchangeRate) {
		ExchangeRate = exchangeRate;
	}

	public String getConsumerPaidUnitPrice() {
		return ConsumerPaidUnitPrice;
	}

	public void setConsumerPaidUnitPrice(String consumerPaidUnitPrice) {
		ConsumerPaidUnitPrice = consumerPaidUnitPrice;
	}

	public String getNet() {
		return Net;
	}

	public void setNet(String net) {
		Net = net;
	}

	public String getMusicUsagePercentage() {
		return MusicUsagePercentage;
	}

	public void setMusicUsagePercentage(String musicUsagePercentage) {
		MusicUsagePercentage = musicUsagePercentage;
	}
	
}
