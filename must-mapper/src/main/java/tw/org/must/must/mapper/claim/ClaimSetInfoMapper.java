package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimSetInfo;

import java.util.List;

@Repository
public interface ClaimSetInfoMapper extends BaseMapper<ClaimSetInfo> {

    List<ClaimSetInfo> selectByGcTime(@Param("company") String company,
                                      @Param("startTime") String startTime,
                                      @Param("endTime") String endTime,
                                      @Param("status") Integer status);

    List<ClaimSetInfo> selectByCzTime(@Param("company") String company,
                                      @Param("startTime") String startTime,
                                      @Param("endTime") String endTime,
                                      @Param("status") Integer status);
}