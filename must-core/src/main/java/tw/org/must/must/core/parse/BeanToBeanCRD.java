package tw.org.must.must.core.parse;

import com.firstbrave.api.base.CrdRecord;
import com.firstbrave.api.crd.vo.*;
import com.firstbrave.api.crd.vo.menu.ExploitationSourceTypeMenu;
import com.firstbrave.api.crd.vo.menu.WorkSocCodeEnum;
import com.firstbrave.api.ipi.enumerate.CrdGrhTypeEnum;
import com.firstbrave.api.ipi.enumerate.CrdPerformerEnum;
import com.firstbrave.api.parser.CrdFileEncoder;
import io.swagger.models.auth.In;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.enums.AVCategoreEnum;
import tw.org.must.must.common.enums.AvTypeEnum;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.Pinyin4jUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.redis.NoGener;
import tw.org.must.must.mapper.dist.DistParamCategoryMapper;
import tw.org.must.must.mapper.dist.DistParamInfoMapper;
import tw.org.must.must.mapper.distdata.DistDataCalcWorkIpRoyMapper;
import tw.org.must.must.mapper.distdata.DistDataCalcWorkPointMapper;
import tw.org.must.must.mapper.list.ListCategoryMapper;
import tw.org.must.must.mapper.list.ListSourceMapper;
import tw.org.must.must.mapper.mbr.MbrIpNameMapper;
import tw.org.must.must.mapper.ref.RefChinDictMapper;
import tw.org.must.must.mapper.ref.RefPoolMstrMapper;
import tw.org.must.must.mapper.ref.RefSocietyMapper;
import tw.org.must.must.mapper.wrk.*;
import tw.org.must.must.model.dist.DistCalcRetain;
import tw.org.must.must.model.dist.DistParamCategory;
import tw.org.must.must.model.dist.DistParamInfo;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.distdata.DistDataCalcWorkPoint;
import tw.org.must.must.model.list.ListCategory;
import tw.org.must.must.model.list.ListSource;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.ref.RefChinDict;
import tw.org.must.must.model.ref.RefPoolMstr;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.wrk.*;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @ClassName: BeanToBeanCRD
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/7/10 13:54
 */
public class BeanToBeanCRD {

  private DistDataCalcWorkIpRoyMapper distDataCalcWorkIpRoyMapper;
  private WrkWorkMapper wrkWorkMapper;
  private WrkWorkTitleMapper wrkWorkTitleMapper;
  private DistParamInfoMapper distParamInfoMapper;
  private RefSocietyMapper refSocietyMapper;
  private DistParamCategoryMapper distParamCategoryMapper;
  private ListCategoryMapper listCategoryMapper;
  private RefPoolMstrMapper refPoolMstrMapper;
  private MbrIpNameMapper mbrIpNameMapper;
  private DistDataCalcWorkPointMapper distDataCalcWorkPointMapper;
  private WrkWorkArtistMergeMapper wrkWorkArtistMergeMapper;
  private WrkArtistMapper wrkArtistMapper;
  private ListSourceMapper listSourceMapper;
  private RefChinDictMapper refChinDictMapper;
  private WrkIswcMapper wrkIswcMapper;

  private Map<String,String> sourceMap = new HashMap<>();
  private List<CrdRecord> records = new ArrayList<>();
  private Map<String,String> iswcMap = new HashMap<>();
  private static final String CRD_BATCH_REQUEST_NO = "crdBatchRequestNo";
  private static final String INTERESTERD_PARTY_NUMBER = "InterestedPartyNumber";

  private static AtomicInteger recordSequenceNo = new AtomicInteger(0);
  private static AtomicInteger transactionSequenceNo = new AtomicInteger(-1);
  private static AtomicInteger referenceIdentifieNo = new AtomicInteger(0);
  private Map<String,Long> amountMap ;
  private Long initAmount = 0L;

  private Map<String,List<DistDataCalcWorkIpRoy>> workUniqueKeyIpRoyMap;



  private static final Logger log = LoggerFactory.getLogger(BeanToBeanCRD.class);

  private List<ListSource> sourceList = new ArrayList<>() ;
  private Map<String,List<String>> sourceCategoryMap = new HashMap<>() ;

  private Map<String,Integer> distCategoryCodeMap = initDistCategoryCode();

  private Map<String,String> sourceRuomaMap = new HashMap<>();



  private static final String WEP = "WEP";
  private static final String WEA = "WEA";
  private static final String WER = "WER";
  private static final String WEX = "WEX";

  public BeanToBeanCRD(DistDataCalcWorkIpRoyMapper distDataCalcWorkIpRoyMapper, WrkWorkMapper wrkWorkMapper,
                       WrkWorkTitleMapper wrkWorkTitleMapper, DistParamInfoMapper distParamInfoMapper,
                       RefSocietyMapper refSocietyMapper, DistParamCategoryMapper distParamCategoryMapper,
                       ListCategoryMapper listCategoryMapper, RefPoolMstrMapper refPoolMstrMapper,
                       MbrIpNameMapper mbrIpNameMapper, DistDataCalcWorkPointMapper distDataCalcWorkPointMapper,
                       WrkWorkArtistMergeMapper wrkWorkArtistMergeMapper, WrkArtistMapper wrkArtistMapper,
                       ListSourceMapper listSourceMapper,RefChinDictMapper refChinDictMapper,WrkIswcMapper wrkIswcMapper) {
    this.distDataCalcWorkIpRoyMapper = distDataCalcWorkIpRoyMapper;
    this.wrkWorkMapper = wrkWorkMapper;
    this.wrkWorkTitleMapper = wrkWorkTitleMapper;
    this.distParamInfoMapper = distParamInfoMapper;
    this.refSocietyMapper = refSocietyMapper;
    this.distParamCategoryMapper = distParamCategoryMapper;
    this.listCategoryMapper = listCategoryMapper;
    this.refPoolMstrMapper = refPoolMstrMapper;
    this.mbrIpNameMapper = mbrIpNameMapper;
    this.distDataCalcWorkPointMapper = distDataCalcWorkPointMapper;
    this.wrkWorkArtistMergeMapper = wrkWorkArtistMergeMapper;
    this.wrkArtistMapper = wrkArtistMapper;
    this.listSourceMapper = listSourceMapper;
    this.refChinDictMapper = refChinDictMapper;
    this.wrkIswcMapper = wrkIswcMapper;
  }

  /**
   *  团体会员
   * @param distNo
   * @param ipBaseNo
   * @return
   * @throws IllegalAccessException
   */
  public List<String> generateCrdForLegal(String distNo,String ipBaseNo, DistCalcRetain distCalcRetain) throws IllegalAccessException{

    final AtomicInteger groupId = new AtomicInteger(1);
    amountMap = initAmountMap();
    TransmissionHeader hdr = transformToHdr("PB");
    records.add(hdr);
    AtomicReference<Integer> recordIndex = new AtomicReference<>(0);
    Arrays.stream(CrdGrhTypeEnum.values()).forEach(value -> {
      GroupHeader groupHeader = transformToGrh(groupId.get(), value.name());
      records.add(groupHeader);
      recordIndex.set(records.size());
      //分类SDN,MWN,RGT
      transformGroupGrhType(distNo, 161, value,ipBaseNo,distCalcRetain.getId());
      Integer recordCount = records.size() - recordIndex.get() ;
      if(CrdGrhTypeEnum.RGT.equals(value)){
        GroupTrailer grt = transformToGRT(3, 1,1);
        records.add(grt);
      } else {
        GroupTrailer grt = transformToGRT(groupId.get(), transactionSequenceNo.incrementAndGet(),recordCount);
        records.add(grt);
      }

      groupId.incrementAndGet();
    });

    TransmissionTrailer trl = transformToTRL(groupId.get() -1,transactionSequenceNo.incrementAndGet() + 1,records.size() + 1) ;
    records.add(trl);
    return CrdFileEncoder.encode(records);
  }

  /**
   * 协会
   * @param distNo
   * @param ipSocietyCode
   * @return
   * @throws IllegalAccessException
   */
  public List<String> generateCrdForSociety(String distNo, Integer ipSocietyCode, DistCalcRetain distCalcRetain)
      throws IllegalAccessException {
    amountMap = initAmountMap();
    final AtomicInteger groupId = new AtomicInteger(1);
    TransmissionHeader hdr = transformToHdr("SO");
    records.add(hdr);
    AtomicReference<Integer> recordIndex = new AtomicReference<>(0);
    Arrays.stream(CrdGrhTypeEnum.values()).forEach(value -> {
      GroupHeader groupHeader = transformToGrh(groupId.get(), value.name());
      records.add(groupHeader);
      recordIndex.set(records.size());
      //分类SDN,MWN,RGT
      transformGroupGrhType(distNo, ipSocietyCode, value,distCalcRetain.getId());
      Integer recordCount = records.size() - recordIndex.get() ;
      if(CrdGrhTypeEnum.RGT.equals(value)){
        GroupTrailer grt = transformToGRT(3, 1,1);
        records.add(grt);
      } else {
        GroupTrailer grt = transformToGRT(groupId.get(), transactionSequenceNo.incrementAndGet(),recordCount);
        records.add(grt);
      }

      groupId.incrementAndGet();
    });
    TransmissionTrailer trl = transformToTRL(groupId.get() -1,transactionSequenceNo.incrementAndGet() + 1,records.size() + 1) ;
    records.add(trl);
    return CrdFileEncoder.encode(records);
  }

  /**
   * 协会
   * @param distNo
   * @param ipSocietyCode
   * @param grhTypeEnum
   */
  private void transformGroupGrhType(String distNo, Integer ipSocietyCode,
      CrdGrhTypeEnum grhTypeEnum,Long retainId) {
    if (CrdGrhTypeEnum.SDN.equals(grhTypeEnum)) {
      transformForSDN(distNo, ipSocietyCode,null,retainId);
    }
    if (CrdGrhTypeEnum.MWN.equals(grhTypeEnum)) {
      transformForMWN(distNo, ipSocietyCode,null);
    }
    if (CrdGrhTypeEnum.RGT.equals(grhTypeEnum)) {
      transformRGT();
    }
  }

  /**
   * 团体会员
   * @param distNo
   * @param ipSocietyCode
   * @param grhTypeEnum
   * @param ipBaseNo
   */
  private void transformGroupGrhType(String distNo, Integer ipSocietyCode, CrdGrhTypeEnum grhTypeEnum, String ipBaseNo,Long retainId) {
    if (CrdGrhTypeEnum.SDN.equals(grhTypeEnum)) {
      transformForSDN(distNo, ipSocietyCode,ipBaseNo,retainId);
    }
    if (CrdGrhTypeEnum.MWN.equals(grhTypeEnum)) {
      transformForMWN(distNo, ipSocietyCode,ipBaseNo);
    }
    if (CrdGrhTypeEnum.RGT.equals(grhTypeEnum)) {
      transformRGT();
      //FIXME
    }
  }

  private void transformForMWN(String distNo, Integer ipSocietyCode,String ipBaseNo) {
    transactionSequenceNo.set(-1);
    referenceIdentifieNo.set(0) ;
    workUniqueKeyIpRoyMap.entrySet().forEach(entry0 -> {
      recordSequenceNo.set(0);
      String workUniqueKey = entry0.getKey();
      List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = entry0.getValue();
      WrkWork wrkWorkCon = new WrkWork();
      wrkWorkCon.setWorkUniqueKey(workUniqueKey);
      WrkWork wrkWork = wrkWorkMapper.selectOne(wrkWorkCon);
      if(wrkWork == null){
        return;
      }
      WrkWorkTitle wrkWorkTitle = new WrkWorkTitle();
      wrkWorkTitle.setWorkUniqueKey(workUniqueKey);
      wrkWorkTitle.setTitleType("OT");
      List<WrkWorkTitle> wrkWorkTitleList = wrkWorkTitleMapper.select(wrkWorkTitle);
      String titleEn = null;
      if (CollectionUtils.isNotEmpty(wrkWorkTitleList)) {
        titleEn = wrkWorkTitleList.get(0).getTitleEn();
      }
      MusicalWorkNotificationTransactionHeader mwn = transformToMWN(titleEn, wrkWork);
      records.add(mwn);
      MusicalWorkDistributionStatus mds = transformToMDS();
      records.add(mds);
      List<String> poolRightList = distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getPoolRight).distinct().collect(Collectors.toList());
      poolRightList.forEach(poolRight -> {
        MusicalWorkDistributionStatusRight mdr = transformToMDR(poolRight);
        records.add(mdr);
      });
      MusicalWorkDistributionStatusTerritory mdt = transformToMDT();
      records.add(mdt);
      //按source分组
      Map<String,List<DistDataCalcWorkIpRoy>> groupSourceMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getSourceName,LinkedHashMap::new,Collectors.toList())); //根据source分组
      for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry1 : groupSourceMap.entrySet()){
        List<DistDataCalcWorkIpRoy> sourceRoyList = entry1.getValue();

        Map<String,List<DistDataCalcWorkIpRoy>> groupIpNameMap = sourceRoyList.stream().collect(
                Collectors.groupingBy(d->d.getIpNameNo()+d.getGroupIndicator()+d.getWorkIpRole() + d.getIpSocietyCode(),LinkedHashMap::new,Collectors.toList())); //根据ipname+gp+rple分组
        for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry2 : groupIpNameMap.entrySet()){
          List<DistDataCalcWorkIpRoy> ipNameRoyList = entry2.getValue();
          Map<String,List<DistDataCalcWorkIpRoy>> groupByWorkTypeMap = ipNameRoyList.stream().collect(Collectors.groupingBy(d->d.getTitleId()+ d.getWorkType(),LinkedHashMap::new,Collectors.toList()));
          for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry3 : groupByWorkTypeMap.entrySet()){
            DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = entry3.getValue().get(0);

            BigDecimal ipshare = entry3.getValue().stream().filter(d-> d.getFileMappingId().equals(distDataCalcWorkIpRoy.getFileMappingId())).map(DistDataCalcWorkIpRoy::getIpShare).reduce(BigDecimal.ZERO,BigDecimal::add) ;
            MusicalWorkInterestedParty mip = transformToMIP(distDataCalcWorkIpRoy,ipshare);
            records.add(mip);

            BigDecimal amount = entry3.getValue().stream().map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal.ZERO,BigDecimal::add) ;
            if(amount.compareTo(BigDecimal.ZERO) == 1){
              if(ipSocietyCode.equals(161) && StringUtils.equals(ipBaseNo,distDataCalcWorkIpRoy.getIpBaseNo()) ){
                transformForPerformer(distDataCalcWorkIpRoy, amount) ;
              } else if(!ipSocietyCode.equals(161) && StringUtils.equals(ipSocietyCode+ "" ,distDataCalcWorkIpRoy.getIpSocietyCode())){
                transformForPerformer(distDataCalcWorkIpRoy,amount) ;
              }
            }
          }
        }
      }

      //FIXME 表演者
      /*DistDataCalcWorkPoint distDataCalcWorkPointCon = new DistDataCalcWorkPoint();
      distDataCalcWorkPointCon.setWorkUniqueKey(workUniqueKey);
      distDataCalcWorkPointCon.setDistNo(distNo);
      List<DistDataCalcWorkPoint> distDataCalcWorkPointList = distDataCalcWorkPointMapper
          .select(distDataCalcWorkPointCon);
      distDataCalcWorkPointList.forEach(this::transformForPerformer);*/
    });
  }

  private void transformForPerformer(DistDataCalcWorkIpRoy distDataCalcWorkIpRoy, BigDecimal amount) {
    Arrays.stream(CrdPerformerEnum.values()).forEach(crdPerformerEnum -> {
      if (Objects.equals(distDataCalcWorkIpRoy.getWorkType(), crdPerformerEnum.name())) {
        //WEP
        if (Objects.equals(WEP, crdPerformerEnum.getCode())) {
          WorkExploitationPerformance wep = transformToWEP(distDataCalcWorkIpRoy,amount);
          amountMap.put(WEP,amountMap.get(WEP)+wep.getGrossRoyaltyAmount());
          records.add(wep);
        }
        //WEA
        if (Objects.equals(WEA, crdPerformerEnum.getCode())) {
          WorkExploitationAudiovisualProgramPerformance wea = transformToWEA(distDataCalcWorkIpRoy, amount);
          amountMap.put(WEA,amountMap.get(WEA)+ wea.getGrossRoyaltyAmount());
          records.add(wea);
        }
      }
    });
  }

  private void transformForSDN(String distNo, Integer ipSocietyCode, String ipBaseNo, Long retainId) {
    DistParamInfo distParamInfoCon = new DistParamInfo();
    distParamInfoCon.setDistNo(distNo);
    List<DistParamInfo> distParamInfoList = distParamInfoMapper.select(distParamInfoCon);
    if (CollectionUtils.isEmpty(distParamInfoList)) {
      throw new MustException(ResultCode.NOT_EXIST.getCode(),
          String.format(ResultCode.NOT_EXIST.getMsg(), "distNo分配编号！"));
    }
    if(ipSocietyCode.equals(161)){
      if (StringUtils.isNotEmpty(ipBaseNo)) {
        MbrIpName mbrIpName = mbrIpNameMapper.getPANameByIpBaseNo(ipBaseNo);
        if (Objects.nonNull(mbrIpName)) {
          SocietyDistributionNotificationTransactionHeader sdn = transformToSDN(mbrIpName.getIpNameNo(), mbrIpName.getName(), distParamInfoList.get(0));
          records.add(sdn);
        }
      }

    } else {
      RefSociety refSocietyCon = new RefSociety();
      refSocietyCon.setSocietyCode(ipSocietyCode);
      RefSociety refSociety = refSocietyMapper.selectOne(refSocietyCon);
      String societyName = null;
      if (Objects.nonNull(refSociety)) {
        societyName = refSociety.getSocietyName();
      }
      SocietyDistributionNotificationTransactionHeader sdn = transformToSDN(ipSocietyCode, societyName, distParamInfoList.get(0));
      records.add(sdn);
    }
    //ESI //dist_param_source表
    DistParamCategory distParamCategoryCon = new DistParamCategory();
    distParamCategoryCon.setDistNo(distNo);
//    List<DistParamCategory> distParamCategoryList = distParamCategoryMapper.select(distParamCategoryCon);
    this.initSource(distNo);
    for(ListSource source : this.sourceList){
        List<String> cates = sourceCategoryMap.get(source.getSourceName()) ;
        if(cates.isEmpty()){
          continue;
        }

        if(ipSocietyCode.equals(161)){
          Long id = distDataCalcWorkIpRoyMapper.getIsHasRoyByMember(distNo,cates,ipBaseNo);
          if(id == null){
            continue;
          }
        } else {
          Long id = distDataCalcWorkIpRoyMapper.getIsHasRoyBySoc(distNo,cates,ipSocietyCode + "");
          if(id == null){
            continue;
          }
        }

      String poolCode = source.getPoolCode();
      RefPoolMstr refPoolMstrCon = new RefPoolMstr();
      refPoolMstrCon.setPoolCode(poolCode);
      RefPoolMstr refPoolMstr = refPoolMstrMapper.selectOne(refPoolMstrCon);
      String exploitationSourceType = ExploitationSourceTypeMenu.getCodeByMsg(refPoolMstr.getPoolCode());
      ExploitationSourceInformation esi = transformToESI(exploitationSourceType, source);
      transToRoman(esi,source);
      records.add(esi);
    }

    List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.getIpRoysByDistAndRetain(distNo,retainId);
    if(CollectionUtils.isEmpty(distDataCalcWorkIpRoyList)){
      return;
    }
    /*if(!ipSocietyCode.equals(161)){
      workUniqueKeyOfAllList = distDataCalcWorkIpRoyMapper.getWorkUniqueKeyByDisNoAndIpSociety(distNo, ipSocietyCode);
    } else {
      workUniqueKeyOfAllList = distDataCalcWorkIpRoyMapper.getWorkUniqueKeyByDisNoAndIpBaseNo(distNo, ipBaseNo);
    }

    if(CollectionUtils.isEmpty(workUniqueKeyOfAllList)){
      return;
    }*/

    List<String> workUniqueKeyOfAllList = distDataCalcWorkIpRoyList.stream().map(d -> d.getWorkUniqueKey()).distinct().collect(Collectors.toList());

    workUniqueKeyIpRoyMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(w -> w.getWorkUniqueKey()));

    Example iswcExample = new Example(WrkIswc.class);
    Example.Criteria iswcCriteria = iswcExample.createCriteria();
    iswcCriteria.andIn("workUniqueKey", workUniqueKeyOfAllList);
    List<WrkIswc> wrkIswcs = wrkIswcMapper.selectByExample(iswcExample);
    wrkIswcs.stream().sorted(Comparator.comparing(WrkIswc::getWorkUniqueKey).thenComparing(WrkIswc::getPreferred,Comparator.reverseOrder()));
    if(CollectionUtils.isNotEmpty(wrkIswcs)){
        iswcMap = wrkIswcs.stream().filter(iswc -> StringUtils.isNotBlank(iswc.getIswc())).collect(Collectors.toMap(WrkIswc::getWorkUniqueKey, WrkIswc::getIswc, (a,b) -> a));
    } else {
      iswcMap = new HashMap<>();
    }

//    List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyMapper.selectIpNameByDistNoAndIpSocietyCode(distNo,workUniqueKeyOfAllList);

    Map<String,List<DistDataCalcWorkIpRoy>> royGroupByIpNameMap = distDataCalcWorkIpRoyList.stream().collect(Collectors.groupingBy(d->d.getIpNameNo()));
//    List<MbrIpName> mbrIpNames = mbrIpNameMapper.
    Example example = new Example(MbrIpName.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("ipNameNo",royGroupByIpNameMap.keySet()) ;
    List<MbrIpName> mbrIpNames = mbrIpNameMapper.selectByExample(example) ;
    for(MbrIpName mbrIpName : mbrIpNames){
      DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = royGroupByIpNameMap.get(mbrIpName.getIpNameNo()).get(0);
      InterestedPartyInformation ipi = transformToIPI(mbrIpName, distDataCalcWorkIpRoy);
      records.add(ipi);
    }

    Map<String,List<DistDataCalcWorkIpRoy>> avWorkMap = distDataCalcWorkIpRoyList.stream().filter(d -> StringUtils.equals("AV",d.getWorkType()))
            .collect(Collectors.groupingBy(d->Constants.getWorkUniqueKey(d.getAvWorkSociety(),d.getAvWorkId())));
    for(Map.Entry<String,List<DistDataCalcWorkIpRoy>> entry : avWorkMap.entrySet()){
      AudioVisualProgramInformation api = transformToAPI(entry.getValue().get(0));
      records.add(api);
    }
  }

  private static TransmissionHeader transformToHdr(String type) {
    TransmissionHeader transmissionHeader = new TransmissionHeader();
    Date date = new Date();
    transmissionHeader.setCreationDate(DateParse.format(date, "yyyyMMdd"));
    transmissionHeader.setCreationTime(DateParse.format(date, "hhmmss"));
    transmissionHeader.setRecordType("HDR");
    transmissionHeader.setSenderId("000000161");
    transmissionHeader.setSenderName("MUST");
    transmissionHeader.setSenderType(type); // PB:Publisher WR:Writer SO:Society AA:Administrative Agency
    transmissionHeader.setStandardVersionNumber("02.00");
    transmissionHeader.setTransmissionDate(DateParse.format(date, "yyyyMMdd"));
    return transmissionHeader;
  }

  private static TransmissionTrailer transformToTRL(int groupCount, int transactionCount,int recordCount) {
    TransmissionTrailer transmissionTrailer = new TransmissionTrailer();
    transmissionTrailer.setRecordType("TRL");
    transmissionTrailer.setGroupCount(groupCount);
    transmissionTrailer.setTransactionCount(transactionCount);
    transmissionTrailer.setRecordCount(recordCount);
    return transmissionTrailer;
  }

  private static GroupHeader transformToGrh(int groupId, String transactionType) {
    GroupHeader grh = new GroupHeader();
    grh.setBatchRequest((int) NoGener.getIncrement(CRD_BATCH_REQUEST_NO));
    grh.setGroupId(groupId);
    grh.setRecordType("GRH");
    grh.setSubmissionType("DF");
    grh.setTransactionType(transactionType);
    grh.setVersionNumber("02.00");
    return grh;
  }

  private static SocietyDistributionNotificationTransactionHeader transformToSDN(
      Integer ipSocietyCode, String societyName, DistParamInfo distParamInfo) {//FIXME 数据来源
    recordSequenceNo.set(0);
    SocietyDistributionNotificationTransactionHeader sdn = new SocietyDistributionNotificationTransactionHeader();
    sdn.setRecordType("SDN");
    sdn.setTransactionSequenceNo(0);//FIXME 待确定
    sdn.setRecordSequenceNo(recordSequenceNo.get());//FIXME 待确定
    sdn.setRemittingSocietyDistributionIdentifier(distParamInfo.getDistNo());
    sdn.setTypeOfRightCategory("PER");
    //distno的起始时间
    sdn.setAccountingPeriodStart(
            "20".concat(distParamInfo.getDistNo().substring(1, 3)).concat("0101"));
    sdn.setAccountingPeriodEnd(
        "20".concat(distParamInfo.getDistNo().substring(1, 3)).concat("1231"));
    sdn.setRecipientSocietyPublisherCode(Long.valueOf(ipSocietyCode));
    sdn.setNameOfRecipient(societyName);
    sdn.setBankPaymentDate("");//无
    sdn.setAmountDecimalPlaces(2);
    sdn.setPercentageDecimalPlaces(2);
    sdn.setVersionNumber("02.00");
    sdn.setRemittanceCurrency("TWD");
    return sdn;
  }

  private static SocietyDistributionNotificationTransactionHeader transformToSDN(
          String ipNameNo, String ipName, DistParamInfo distParamInfo) {//FIXME 数据来源
    recordSequenceNo.set(0);
    SocietyDistributionNotificationTransactionHeader sdn = new SocietyDistributionNotificationTransactionHeader();
    sdn.setRecordType("SDN");
    sdn.setTransactionSequenceNo(0);//FIXME 待确定
    sdn.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME 待确定
    //分配编号
    sdn.setRemittingSocietyDistributionIdentifier(distParamInfo.getDistNo());
    sdn.setTypeOfRightCategory("PER");
    //distno的起始时间
    sdn.setAccountingPeriodStart(
            "20".concat(distParamInfo.getDistNo().substring(1, 3)).concat("0101"));
    sdn.setAccountingPeriodEnd(
            "20".concat(distParamInfo.getDistNo().substring(1, 3)).concat("1231"));
    sdn.setRecipientSocietyPublisherCode(Long.valueOf(ipNameNo));
    //ref_society society_name
    sdn.setNameOfRecipient(ipName);
    sdn.setBankPaymentDate(StringUtils.leftPad(" ",8," "));//无
    sdn.setAmountDecimalPlaces(2);
    sdn.setPercentageDecimalPlaces(2);
    sdn.setVersionNumber("02.00");
    sdn.setRemittanceCurrency("TWD");
    return sdn;
  }

  private static ExploitationSourceInformation transformToESI(String exploitationSourceType,
      ListSource listSource) {
    //distNo类型o：list_source_name，
    ExploitationSourceInformation exploitationSourceInformation = new ExploitationSourceInformation();
    //dist_param_category list_source_name
//    exploitationSourceInformation.setExploitationSourceName(listSource.getSourceDesc());
    exploitationSourceInformation.setExploitationTerritoryAbbreviatedNameValidFromDate("********");
    exploitationSourceInformation.setRecordType("ESI");
    //list_category_code 到 list_category 查pool_code  在根据ref_pool_mstr表到Exploitation Source Type找对应数字
    exploitationSourceInformation.setExploitationSourceType(exploitationSourceType);
    exploitationSourceInformation.setTransactionSequenceNo(0);//FIXME
    exploitationSourceInformation.setExploitationTerritoryCodeValidFromDate("********");
    exploitationSourceInformation.setExploitationTerritoryAbbreviatedName("TAIWAN");
    exploitationSourceInformation.setExploitationTerritoryCode(158);
    exploitationSourceInformation.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//Fixme
    //dist_param_category list_category_code
//    exploitationSourceInformation.setExploitationSourceIdentifier(listSource.getSourceName());

    return exploitationSourceInformation;
  }

  private static InterestedPartyInformation transformToIPI(MbrIpName mbrIpName,
      DistDataCalcWorkIpRoy distDataCalcWorkIpRoy) {
    InterestedPartyInformation ipi = new InterestedPartyInformation();
    ipi.setRecordType("IPI");
    String ipNameNo = distDataCalcWorkIpRoy.getIpNameNo() ;
    ipi.setInterestedPartyNumber(ipNameNo);
//    ipi.setIpNameNumber(ipNameNo);
    if(ipNameNo.startsWith("0")){
      ipi.setIpNameNumber(ipNameNo);
    }else{
      ipi.setIpNameNumber("********000");
    }
    ipi.setTransactionSequenceNo(0);
    ipi.setRecordSequenceNo(recordSequenceNo.getAndIncrement());
    String ipType = distDataCalcWorkIpRoy.getIpType();
    //L（圖體）\N（個人
    //C:个人 ，P:团体
    if ("L".equals(ipType)) {
      ipi.setGenericTypeOfIp("P");
    }
    if ("N".equals(ipType)) {
      ipi.setGenericTypeOfIp("C");
    }
    if(mbrIpName != null){
      ipi.setIpName(mbrIpName.getLastName());
      ipi.setIpFirstName(mbrIpName.getFirstName());
    }
    return ipi;
  }

  private AudioVisualProgramInformation transformToAPI(DistDataCalcWorkIpRoy distDataCalcWorkIpRoy) {
    AudioVisualProgramInformation api = new AudioVisualProgramInformation();
    api.setRecordType("API");
    api.setTransactionSequenceNo(0);
    api.setRecordSequenceNo(recordSequenceNo.getAndIncrement());
    api.setSocietyAvWorkIdentifier((WorkSocCodeEnum.getValueByCode(distDataCalcWorkIpRoy.getAvWorkSociety()) + distDataCalcWorkIpRoy.getAvWorkId() + "A00"));

    WrkWorkTitle avWrkWorkTitle = wrkWorkTitleMapper.selectByPrimaryKey(distDataCalcWorkIpRoy.getAvTitleId());
    if (null != avWrkWorkTitle) {
      api.setAvWorkTitle(avWrkWorkTitle.getTitleEn());
      String genre = avWrkWorkTitle.getGenreCode();
      if(StringUtils.isNotBlank(genre)){
        String type = AvTypeEnum.valueOf(genre).getCode();
        api.setType(type);
        api.setAvWorkCategory(AVCategoreEnum.valueOf(type).getCode());
      }
    }

    WrkWork wrkWorkCon = new WrkWork();
    wrkWorkCon.setWorkUniqueKey(Constants.getWorkUniqueKey(distDataCalcWorkIpRoy.getAvWorkSociety(),distDataCalcWorkIpRoy.getAvWorkId()));
    WrkWork wrkWork = wrkWorkMapper.selectOne(wrkWorkCon);
    if(wrkWork != null){
      api.setDirectorName(wrkWork.getDirector());
    }
//    String ipNameNo = distDataCalcWorkIpRoy.getIpNameNo() ;
    api.setYearOfProduction(wrkWork.getProductionYear());
    api.setTotalDuration("000000");
    api.setMusicDuration("000000");
    api.setProtectedMusicDuration("000000");
    api.setCountryOfOriginTerritoryCode(0);
    return api;
  }

  private static GroupTrailer transformToGRT(Integer groupId, Integer transactionCount, Integer recordCount) {
    GroupTrailer grt = new GroupTrailer();
    grt.setRecordType("GRT");
    grt.setGroupId(groupId);
    //FIXME
    grt.setRecordCount(recordCount + 2);
    grt.setCurrencyIndicator("");
    grt.setTotalMonetaryValue(0);
    //FIXME
    grt.setTransactionCount(transactionCount);
    return grt;
  }

  private MusicalWorkNotificationTransactionHeader transformToMWN(String titleEn,
      WrkWork wrkWork) {
    MusicalWorkNotificationTransactionHeader mwn = new MusicalWorkNotificationTransactionHeader();
    mwn.setSongCodeOfPublisher("");
    mwn.setRecordType("MWN");
    mwn.setIswc(iswcMap.get(wrkWork.getWorkUniqueKey()));
    mwn.setMusicalWorkDistributionCategory("");
    mwn.setInstrumentalOrVocalUse("U");//文件中写死
    if(wrkWork.getWorkType().equals("AV")){
      mwn.setCompositeWorkIndicator(2);
      mwn.setCompositeWorkIdentifier(wrkWork.getWorkUniqueKey());
      mwn.setCompositeWorkTitle(titleEn);
      mwn.setWorkPercentageOfComposite(0);
    } else {
      mwn.setCompositeWorkIndicator(1);
      mwn.setWorkPercentageOfComposite(0);
    }

    if(wrkWork.getWorkType().equals("ADP")){
      mwn.setLocalWorkTitle(titleEn);
      String oriWorkUniqueKey = Constants.getWorkUniqueKey(wrkWork.getRefWorkSociety(),wrkWork.getRefWorkId()) ;
      if(oriWorkUniqueKey != null){
        WrkWorkTitle query = new WrkWorkTitle();
        query.setWorkUniqueKey(oriWorkUniqueKey);
        query.setTitleType("OT");
        List<WrkWorkTitle> wrkWorkTitleList = wrkWorkTitleMapper.select(query);
        if (CollectionUtils.isNotEmpty(wrkWorkTitleList)) {
          String originalWorkTitle = wrkWorkTitleList.get(0).getTitleEn();
          mwn.setOriginalWorkTitle(originalWorkTitle);
        }
      }
    } else {
      mwn.setOriginalWorkTitle(titleEn);
    }
    mwn.setOpusNumber("");
    mwn.setTransactionSequenceNo(transactionSequenceNo.incrementAndGet());//FIXME
    mwn.setCompositeWorkTitle("");
    mwn.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME
    mwn.setWorkPercentageOfComposite(0);

    mwn.setSocietyWorkIdentifier(WorkSocCodeEnum.getValueByCode(wrkWork.getWorkSocietyCode()) + wrkWork.getWorkId() + "A00");
    /*String iswc = iswcMap.get(wrkWork.getWorkUniqueKey());
    if(StringUtils.isEmpty(iswc)){
      mwn.setSocietyWorkIdentifier(WorkSocCodeEnum.getValueByCode(wrkWork.getWorkSocietyCode()) + wrkWork.getWorkId() + "A00");
    } else {
      mwn.setSocietyWorkIdentifier(iswc + "A00");
      mwn.setIswc(iswc);
    }*/
    return mwn;
  }

  private static MusicalWorkDistributionStatus transformToMDS() {
    MusicalWorkDistributionStatus mds = new MusicalWorkDistributionStatus();
    mds.setMdsIdentifier(1);
    mds.setWorkStatusStartDate("");
    mds.setTypeOfShareStructure(1);
    mds.setRecordType("MDS");
    mds.setTransactionSequenceNo(transactionSequenceNo.get());//FIXME
    mds.setPaymentRuleIndicator("");
    mds.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME
    mds.setWorkStatusEndDate("");
    return mds;
  }

  private static MusicalWorkDistributionStatusRight transformToMDR(String poolRight) {
    MusicalWorkDistributionStatusRight mdr = new MusicalWorkDistributionStatusRight();
    mdr.setTypeOfRightCategory("");
    mdr.setRecordType("MDR");
    mdr.setRightCode(poolRight);
    mdr.setTransactionSequenceNo(transactionSequenceNo.get());//FIXME
    mdr.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME
    return mdr;
  }

  private static MusicalWorkDistributionStatusTerritory transformToMDT() {
    MusicalWorkDistributionStatusTerritory mdt = new MusicalWorkDistributionStatusTerritory();
    mdt.setInterestTerritoryTisn(158);
    mdt.setInterestTerritoryTisan("TAIWAN");
//    mdt.setInterestTerritoryTisanValidFromDate(StringUtils.leftPad("",8," "));
    mdt.setRecordType("MDT");
    mdt.setTransactionSequenceNo(transactionSequenceNo.get());//FIXME
    mdt.setInterestTerritoryTisnValidFromDate("");
    mdt.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME
    return mdt;
  }

  private MusicalWorkInterestedParty transformToMIP(DistDataCalcWorkIpRoy distDataCalcWorkIpRoy, BigDecimal share) {
    MusicalWorkInterestedParty mip = new MusicalWorkInterestedParty();
    mip.setShareDenominator(10000);
    mip.setIpRole(distDataCalcWorkIpRoy.getWorkIpRole());
    mip.setContractIndicator(distDataCalcWorkIpRoy.getGroupIndicator());
    mip.setNationalAgreementNumber("");
    mip.setNationalAdministratorNumber(0L);
    mip.setRecordType("MIP");
    mip.setInterestedPartyNumber(distDataCalcWorkIpRoy.getIpNameNo());
    mip.setNationalAdministratorNumber(0L);
    if(!StringUtils.isBlank(distDataCalcWorkIpRoy.getIpSocietyCode())){
      mip.setIpSociety(Integer.valueOf(distDataCalcWorkIpRoy.getIpSocietyCode()));
    }
    mip.setRecordSequenceNo(recordSequenceNo.getAndIncrement());//FIXME
    mip.setShareNumerator(share.multiply(new BigDecimal(100)).intValue());
    mip.setTransactionSequenceNo(transactionSequenceNo.get());
    String agrNo = "" ;
    String extJson = distDataCalcWorkIpRoy.getExtJson();
    if (StringUtils.isNotBlank(extJson)) {
     JSONObject jsonObject = JSONObject.fromObject(extJson);
      if (null != jsonObject && jsonObject.has("agrNo")) {
        agrNo = jsonObject.getString("agrNo");
        if(StringUtils.isNotBlank(agrNo)){
          mip.setNationalAgreementNumber(distDataCalcWorkIpRoy.getGroupIndicator() + "-" + agrNo);
        }
      }
    }
    if(agrNo.equals("")){
      mip.setNationalAgreementNumber(distDataCalcWorkIpRoy.getGroupIndicator() + "/" + mip.getRecordSequenceNo());
    }
    mip.setSharePercentage(0);
    return mip;
  }

  private WorkExploitationPerformance transformToWEP(DistDataCalcWorkIpRoy distDataCalcWorkIpRoy, BigDecimal amount) {
//    log.info("WorkExploitationPerformance,distDataCalcWorkIpRoy.id={}",distDataCalcWorkIpRoy.getId());
    WorkExploitationPerformance wep = new WorkExploitationPerformance();
    wep.setRecordType(WEP);
    wep.setTransactionSequenceNo(transactionSequenceNo.get());
    wep.setRecordSequenceNo(recordSequenceNo.getAndIncrement());
    String referenceIdentifie = "161" + distDataCalcWorkIpRoy.getDistNo() + StringUtils.leftPad(referenceIdentifieNo.getAndIncrement() + "",8,"0") ;
    wep.setReferenceIdentifier(referenceIdentifie);
    wep.setSourceSocietyTaxAmount(0L);
    wep.setSourceSociety(0);
    wep.setExploitationDate("********");
    wep.setExploitationTime("0");
    wep.setQuantity(0);
    wep.setAggregationPeriodType("Y");
    wep.setBonusLevelPerformanceIndicator("");
    wep.setCurrency("TWD");
    wep.setBasisOfRateCalculation("B");
    wep.setMinuteSecondPointValue(0L);
    wep.setSurveyType("");
    wep.setNationalGroupingIdentifier(distDataCalcWorkIpRoy.getGroupIndicator());
    wep.setDistributionCategory(distCategoryCodeMap.get(distDataCalcWorkIpRoy.getPoolCode()));
    wep.setIsrc("");
    wep.setSourceSocietyCommissionAmount(0L);
    if(distDataCalcWorkIpRoy == null || distDataCalcWorkIpRoy.getDistRoy() == null){
      wep.setGrossRoyaltyAmount(0L);
    } else {
      wep.setGrossRoyaltyAmount(amount.multiply(new BigDecimal(100)).longValue());
    }
    wep.setRemittedRoyaltyAmount(wep.getGrossRoyaltyAmount());
    wep.setSourceSocietyCommissionAmount(0L);
    wep.setMinuteSecondPointValue(0L);
    wep.setSourceSociety(0);
    wep.setExploitationSourceIdentifier(sourceRuomaMap.get(distDataCalcWorkIpRoy.getSourceName()));
    this.setArtist(distDataCalcWorkIpRoy.getWorkUniqueKey(),wep);
    wep.setTaxRate(0);
    wep.setTaxAmount(0L);
    wep.setCommissionAmount(0L);
    return wep;
  }

  private WorkExploitationAudiovisualProgramPerformance transformToWEA(DistDataCalcWorkIpRoy distDataCalcWorkIpRoy, BigDecimal amount) {
    WorkExploitationAudiovisualProgramPerformance wea = new WorkExploitationAudiovisualProgramPerformance();
    wea.setAggregationPeriodType("Y");
    wea.setBasisOfRateCalculation("B");
    wea.setCommissionAmount(0L);
    wea.setCueSequence(0);
    wea.setCurrency("TWD");
//    wea.setDayOfWeekCode();
    wea.setDistributionCategory(distCategoryCodeMap.get(distDataCalcWorkIpRoy.getPoolCode()));
    wea.setDuration("");
    wea.setExploitationDate("********");
    wea.setExploitationSourceIdentifier(sourceRuomaMap.get(distDataCalcWorkIpRoy.getSourceName()));
    wea.setExploitationTime("0");
    if(distDataCalcWorkIpRoy == null || distDataCalcWorkIpRoy.getDistRoy() == null){
      wea.setGrossRoyaltyAmount(0L);
    } else {
      wea.setGrossRoyaltyAmount(amount.multiply(new BigDecimal(100)).longValue());
    }
    wea.setNationalGroupingIdentifier("");
    wea.setOrigin("");
    wea.setProductCatalogueNumber("");
    wea.setQuantity(0);
    wea.setRecordSequenceNo(recordSequenceNo.getAndIncrement());
    wea.setRecordType(WEA);
    String referenceIdentifie = "161" + distDataCalcWorkIpRoy.getDistNo() + StringUtils.leftPad(referenceIdentifieNo.getAndIncrement() + "",8,"0") ;
    wea.setReferenceIdentifier(referenceIdentifie);
    wea.setNationalGroupingIdentifier(distDataCalcWorkIpRoy.getGroupIndicator());
    wea.setSocietyAvWorkIdentifier((WorkSocCodeEnum.getValueByCode(distDataCalcWorkIpRoy.getAvWorkSociety()) + distDataCalcWorkIpRoy.getAvWorkId() + "A00"));
    wea.setRemittedRoyaltyAmount(wea.getGrossRoyaltyAmount());
    wea.setSourceSociety(0);
    wea.setSourceSocietyCommissionAmount(0L);
    wea.setSourceSocietyTaxAmount(0L);
//    wea.setSurveyType();
    wea.setTaxAmount(0L);
    wea.setTaxRate(0);
    wea.setTransactionSequenceNo(transactionSequenceNo.get());
    return wea;
  }

  public void transformRGT(){
    String sign = "+";
    RoyaltyGrandTotalsTransactionHeader royaltyGrandTotalsTransactionHeader = new RoyaltyGrandTotalsTransactionHeader();
    royaltyGrandTotalsTransactionHeader.setRecordType("RGT");
    royaltyGrandTotalsTransactionHeader.setTransactionSequenceNo(0);
    royaltyGrandTotalsTransactionHeader.setRecordSequenceNo(0);
    royaltyGrandTotalsTransactionHeader.setWeaGrossRoyaltyAmount(amountMap.get(WEA));
    royaltyGrandTotalsTransactionHeader.setWepGrossRoyaltyAmount(amountMap.get(WEP));
    royaltyGrandTotalsTransactionHeader.setWerGrossRoyaltyAmount(amountMap.get(WER));
    Long wexAmount = royaltyGrandTotalsTransactionHeader.getWeaGrossRoyaltyAmount() + royaltyGrandTotalsTransactionHeader.getWepGrossRoyaltyAmount() + royaltyGrandTotalsTransactionHeader.getWerGrossRoyaltyAmount();
    royaltyGrandTotalsTransactionHeader.setWexRemittedRoyaltyAmount(wexAmount);
    royaltyGrandTotalsTransactionHeader.setIccSourceSocietyContingencyAmount(0L);
    royaltyGrandTotalsTransactionHeader.setIccSourceSocietyContingencyAmountSign(sign);
    royaltyGrandTotalsTransactionHeader.setIccRemittingSocietyContingencyAmount(0L);
    royaltyGrandTotalsTransactionHeader.setIccRemittingSocietyContingencyAmountSign(sign);
    royaltyGrandTotalsTransactionHeader.setAdjAdjustmentAmount(0L);
    royaltyGrandTotalsTransactionHeader.setAdjAdjustmentAmountSign(sign);
    royaltyGrandTotalsTransactionHeader.setFeoReturnAmount(0L);
    royaltyGrandTotalsTransactionHeader.setFeoReturnAmountSign(sign);
    records.add(royaltyGrandTotalsTransactionHeader);
  }

  public void setArtist(String workUniqueKey,WorkExploitationPerformance wep){

    if(StringUtils.isEmpty(workUniqueKey)){
      return ;
    }

    Example exampleMerge = new Example(WrkWorkArtistMerge.class);
    Example.Criteria criteriaMerge = exampleMerge.createCriteria();
    criteriaMerge.andEqualTo("workUniqueKey",workUniqueKey);
    List<WrkWorkArtistMerge> wrkWorkArtistMerges = wrkWorkArtistMergeMapper.selectByExample(exampleMerge);
    if(CollectionUtils.isEmpty(wrkWorkArtistMerges)){
      return ;
    }
    List<String> uniqueKeyMd5s = wrkWorkArtistMerges.stream().map(WrkWorkArtistMerge::getUniqueKeyMd5).collect(Collectors.toList());
    Example example = new Example(WrkArtist.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("uniqueKeyMd5",uniqueKeyMd5s);
    List<WrkArtist> wrkArtistList = wrkArtistMapper.selectByExample(example) ;
//    List<String> firstNames = wrkArtistList.stream().filter(w -> StringUtils.isNotEmpty(w.getFirstName())).map(WrkArtist::getFirstName).collect(Collectors.toList());
//    List<String> lastName = wrkArtistList.stream().filter(w -> StringUtils.isNotEmpty(w.getLastName())).map(WrkArtist::getLastName).collect(Collectors.toList());
    wep.setPerformingArtistFirstName(StringUtils.join(wrkArtistList.stream().filter(w -> StringUtils.isNotEmpty(w.getFirstName())
            && w.getFirstName().getBytes(StandardCharsets.UTF_8).length == w.getFirstName().length()).map(WrkArtist::getFirstName).collect(Collectors.toList()),"/"));
    wep.setPerformingArtistLastName(StringUtils.join(wrkArtistList.stream().filter(w -> StringUtils.isNotEmpty(w.getLastName()) &&
            w.getLastName().getBytes(StandardCharsets.UTF_8).length == w.getLastName().length()).map(WrkArtist::getLastName).collect(Collectors.toList()),"/"));
  }

  public void initSource(String distNo){
      if(this.sourceList.size() == 0){

        DistParamCategory distParamCategoryCon = new DistParamCategory();
        distParamCategoryCon.setDistNo(distNo);
        List<DistParamCategory> distParamCategoryList = distParamCategoryMapper.select(distParamCategoryCon);

        if(CollectionUtils.isEmpty(distParamCategoryList)){
          this.sourceList = new ArrayList<>() ;
          this.sourceCategoryMap = new HashMap<>();
          return;
        }

        Map<String,List<String>> sourceCategoryMap = distParamCategoryList.stream().collect(Collectors.groupingBy(DistParamCategory::getListSourceName,
                Collectors.mapping(DistParamCategory::getListCategoryCode, Collectors.toList())));

        List<ListSource> listSources = listSourceMapper.selectSourceWithPoolCode(sourceCategoryMap.keySet()) ;
        this.sourceList = new ArrayList<>() ;
        this.sourceCategoryMap = sourceCategoryMap;
      }


  }

  private void transToRoman(ExploitationSourceInformation exploitationSourceInformation, ListSource listSource){

    String sourceName = listSource.getSourceName();
    String sourceDesc = listSource.getSourceDesc();

    if(!sourceRuomaMap.containsKey(sourceName)){
      sourceRuomaMap.put(sourceName,chineseToRoman(sourceName,"_"));
      sourceRuomaMap.put(sourceDesc,chineseToRoman(sourceDesc,""));
    }

    exploitationSourceInformation.setExploitationSourceIdentifier(sourceRuomaMap.get(sourceName));
    exploitationSourceInformation.setExploitationSourceName(sourceRuomaMap.get(sourceDesc));

  }


  private void transToRoman(ListSource listSource){
    if(sourceRuomaMap.containsKey(listSource.getSourceName())){
      return;
    }

    sourceRuomaMap.put(listSource.getSourceName(),chineseToRoman(listSource.getSourceName(),"_"));
    sourceRuomaMap.put(listSource.getSourceDesc(),chineseToRoman(listSource.getSourceDesc(),""));
  }

  public String chineseToRoman (String chinese, String separator){
    String result = "";
    if (chinese != null && !chinese.trim().equalsIgnoreCase("")) {
      if(chinese.length() == chinese.getBytes(StandardCharsets.UTF_8).length){
        return chinese;
      }
      char[] chars = chinese.toCharArray();
      StringBuffer sb = new StringBuffer();
      for (int i = 0; i < chars.length; i++) {
        char c = chars[i];
        String string = String.valueOf(c);
        if (String.valueOf(c).matches("[\\u4E00-\\u9FA5]+")) {
          List<RefChinDict> romans = refChinDictMapper.getByChinWord(string) ;
          if(CollectionUtils.isNotEmpty(romans)){
            sb.append(romans.get(0).getRomanWord()).append(separator);
          } else {
            List<String> pinyins = Pinyin4jUtil.getLocalPinyin(string);
            if(CollectionUtils.isNotEmpty(pinyins)){
              sb.append(pinyins.get(0)).append(separator);
            }
          }
        } else {
          sb.append(string);
        }
      }

      result = sb.toString();
      if(result.endsWith(separator)){
        result = result.substring(0,result.length()-1);
      }
    }

    return result;
  }

  public static void main(String[] args) {
    MusicalWorkDistributionStatusTerritory mdt = transformToMDT();
    List<CrdRecord> crdRecordList = new ArrayList<>();
    crdRecordList.add(mdt);
    try{
      List<String> strings = CrdFileEncoder.encode(crdRecordList);
      System.out.println(strings);
    }catch (Exception e){
      e.printStackTrace();
    }
  }

  private Map<String,Long> initAmountMap(){
    Map<String,Long> map = new ConcurrentHashMap<>() ;
    map.put(WEA,initAmount);
    map.put(WEP,initAmount);
    map.put(WER,initAmount);
    return map;
  }

  private Map<String,Integer> initDistCategoryCode(){
    Map<String,Integer> map = new HashMap<>();
    map.put("R",20);
    map.put("T",21);
    map.put("D",30);
    map.put("DB",30);
    map.put("I",40);
    map.put("OD",40);
    map.put("C",50);
    map.put("M",70);
    map.put("F",80);
    map.put("O",90);
    map.put("G",90);
    map.put("K",90);

    return map;

  }

}
