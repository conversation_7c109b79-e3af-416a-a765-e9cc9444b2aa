package tw.org.must.must.core.parse.youtube.tsv.handle;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.core.annotation.Log;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.TsvCommonObject;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class YoutubeTsvRowReadHandle extends YoutubeTsvRowReadHandleAbstract {


    @Override
    protected int parseData(Map<String, String> map) {
        //数据行

        return 0;
    }
}
