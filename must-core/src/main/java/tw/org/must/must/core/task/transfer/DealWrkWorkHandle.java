package tw.org.must.must.core.task.transfer;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.common.vcp.CommonUtil;
import tw.org.must.must.model.agr.AgrWrkSip;
import tw.org.must.must.model.orcal.*;
import tw.org.must.must.model.wrk.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class DealWrkWorkHandle {

    OrcalWrkWork orcalWrkWork;

    List<OrcalWrkWorkArtistMerge> orcalWrkArtistMergeList;

    List<OrcalWrkIsrc> orcalWrkIsrcList;

    List<OrcalWrkTvSeries> orcalWrkTvSeriesList;

    List<OrcalWrkWorkComponent> orcalWrkWorkComponentList;

    List<OrcalWrkWorkIpShare> orcalWrkWorkIpShareList;

    List<OrcalWrkWorkRemark> orcalWrkWorkRemarkList;

    List<OrcalWrkWorkSource> orcalWrkWorkSourceList;

    List<OrcalWrkWorkTitle> orcalWrkWorkTitleList;

    List<OrcalWrkWorkRight> orcalWrkWorkRightList;

    List<OrcalWrkIswc> orcalWrkIswcList;

    List<OrcalAgrWrkSip> orcalAgrWrkSipList;

    List<OrcalWrkArtist> orcalWrkArtistList;

    List<OrcalWrkWorkAlbum> orcalWrkWorkAlbumList;

    List<Integer> orcalSipLinkIdList;


    List<WrkWorkArtistMerge> moneyWrkArtistMergeList;

    List<WrkIsrc> moneyWrkIsrcList;

    List<WrkTvSeries> moneyWrkTvSeriesList;

    List<WrkWorkComponent> moneyWrkWorkComponentList;

    List<WrkWorkIpShare> moneyWrkWorkIpShareList;

    List<WrkWorkRemark> moneyWrkWorkRemarkList;

    List<WrkWorkSource> moneyWrkWorkSourceList;

    List<WrkWorkTitle> moneyWrkWorkTitleList;

    List<WrkWorkRight> moneyWrkWorkRightList;

    List<WrkIswc> moneyWrkIswcList;

    List<AgrWrkSip> moneyAgrWrkSipList;

    List<Integer> moneySipLinkIdList;

    List<WrkArtist> moneyWrkArtistList;

    List<WrkWorkAlbum> moneyWrkWorkAlbumList;

    WrkWork moneyWrkWork;

    List<WrkWorkArtistMerge> moneyWrkWorkArtistMergeList;

    List<WrkWorkTitle> addWrkWorkTitleList;
    List<WrkWorkTitle> updWrkWorkTitleList;
    List<WrkWorkTitle> delWrkWorkTitleList;

    List<WrkWorkIpShare> addWrkWorkIpShareList;
    List<WrkWorkIpShare> updWrkWorkIpShareList;
    List<WrkWorkIpShare> delWrkWorkIpShareList;

    List<WrkWorkRemark> addWrkWorkRemarkList;
    List<WrkWorkRemark> updWrkWorkRemarkList;
    List<WrkWorkRemark> delWrkWorkRemarkList;

    List<WrkIsrc> addWrkIsrcList;
    List<WrkIsrc> updWrkIsrcList;
    List<WrkIsrc> delWrkIsrcList;

    List<WrkWorkSource> addWrkWorkSourceList;
    List<WrkWorkSource> updWrkWorkSourceList;
    List<WrkWorkSource> delWrkWorkSourceList;

    List<WrkWorkComponent> addWrkWorkComponentList;
    List<WrkWorkComponent> updWrkWorkComponentList;
    List<WrkWorkComponent> delWrkWorkComponentList;

    List<WrkWorkRight> addWrkWorkRightList;
    List<WrkWorkRight> updWrkWorkRightList;
    List<WrkWorkRight> delWrkWorkRightList;

    List<WrkArtist> addWrkArtistList;
    List<WrkArtist> updWrkArtistList;
    List<WrkArtist> delWrkArtistList;

    List<WrkWorkArtistMerge> addWrkWorkArtistMergeList;
    List<WrkWorkArtistMerge> updWrkWorkArtistMergeList;
    List<WrkWorkArtistMerge> delWrkWorkArtistMergeList;

    List<WrkWorkAlbum> addWrkWorkAlbumList;
    List<WrkWorkAlbum> updWrkWorkAlbumList;
    List<WrkWorkAlbum> delWrkWorkAlbumList;

    // wrkWork相关信息 新增、更新、删除
    public void dealWrkWork(Map<String, String> ipNameNoBaseNoMap) {

        addOrUpdateWrkWork();

        addOrUpdateWrkWorkTitle();

        addOrUpdateWrkWorkIpShare(ipNameNoBaseNoMap);

        addOrUpdateWrkWorkRemark();

        addOrUpdateWrkWorkIsrc();

        addOrUpdateWrkWorkSource();

        addOrUpdateWrkWorkComponent();

        addOrUpdateWrkWorkRight();

        addOrUpdateWrkArtist();

        addOrUpdateWrkArtistMerge();

        addOrUpdateWrkWorkAlbum();
    }

    private void addOrUpdateWrkWorkAlbum() {
        if(null != orcalWrkWorkAlbumList && orcalWrkWorkAlbumList.size()>0){
            if(null != moneyWrkWorkAlbumList && moneyWrkWorkAlbumList.size()>0){
                List<String> orcalWrkWorkAlbumKeyList = orcalWrkWorkAlbumList.stream().map(it -> it.getALBUM_NO() + it.getFIRST_PUB()).collect(Collectors.toList());// 这里需要注意如果orcal数据库是null，这里和下面的比较会存在错误
                List<String> moneyWrkWorkAlbumKeyList = moneyWrkWorkAlbumList.stream().map(it -> it.getAlbumNo() + it.getFirstPublish()).collect(Collectors.toList());

                Map<String, WrkWorkAlbum> moneyWrkWorkAlbumKeyMap = moneyWrkWorkAlbumList.stream().collect(Collectors.toMap(it -> it.getAlbumNo() + it.getFirstPublish(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalWrkWorkAlbumKeyList = new ArrayList<>(orcalWrkWorkAlbumKeyList);
                copyOrcalWrkWorkAlbumKeyList.retainAll(moneyWrkWorkAlbumKeyList);
                if(null != copyOrcalWrkWorkAlbumKeyList && copyOrcalWrkWorkAlbumKeyList.size()>0){
                    orcalWrkWorkAlbumList.forEach(orcalWrkWorkAlbum ->{
                        String key = orcalWrkWorkAlbum.getALBUM_NO() + orcalWrkWorkAlbum.getFIRST_PUB();
                        if(copyOrcalWrkWorkAlbumKeyList.contains(key)){
                            updWrkWorkAlbumList.add(orcalAndMoneyWrkWorkAlbumChange(orcalWrkWorkAlbum,moneyWrkWorkAlbumKeyMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalWrkWorkAlbumKeyList = new ArrayList<>(orcalWrkWorkAlbumKeyList);
                copyAddOrcalWrkWorkAlbumKeyList.removeAll(moneyWrkWorkAlbumKeyList);
                if(null != copyAddOrcalWrkWorkAlbumKeyList && copyAddOrcalWrkWorkAlbumKeyList.size()>0){
                    orcalWrkWorkAlbumList.forEach(orcalWrkWorkAlbum ->{
                        String key = orcalWrkWorkAlbum.getALBUM_NO() + orcalWrkWorkAlbum.getFIRST_PUB();
                        if(copyAddOrcalWrkWorkAlbumKeyList.contains(key)){
                            addWrkWorkAlbumList.add(orcalAndMoneyWrkWorkAlbumChange(orcalWrkWorkAlbum,null));
                        }
                    });
                }

                moneyWrkWorkAlbumKeyList.removeAll(orcalWrkWorkAlbumKeyList);
                if(null != moneyWrkWorkAlbumKeyList && moneyWrkWorkAlbumKeyList.size()>0){
                    moneyWrkWorkAlbumList.forEach(wrkWorkAlbum -> {
                        String key = wrkWorkAlbum.getAlbumNo()+wrkWorkAlbum.getFirstPublish();
                        if(moneyWrkWorkAlbumKeyList.contains(key)){
                            delWrkWorkAlbumList.add(wrkWorkAlbum);
                        }
                    });
                }

            }

            orcalWrkWorkAlbumList.forEach(orcalWrkWorkAlbum -> {
                WrkWorkAlbum wrkWorkAlbum = orcalAndMoneyWrkWorkAlbumChange(orcalWrkWorkAlbum,null);
                addWrkWorkAlbumList.add(wrkWorkAlbum);
            });

        }

    }

    private WrkWorkAlbum orcalAndMoneyWrkWorkAlbumChange(OrcalWrkWorkAlbum orcalWrkWorkAlbum, WrkWorkAlbum wrkWorkAlbum) {
        if(null == wrkWorkAlbum){
            wrkWorkAlbum = new WrkWorkAlbum();
            wrkWorkAlbum.init();
        }else{
            wrkWorkAlbum.setAmendTime(new Date());
        }

        wrkWorkAlbum.setWorkId(Long.valueOf(orcalWrkWorkAlbum.getWORKNUM()));
        wrkWorkAlbum.setWorkSocietyCode(orcalWrkWorkAlbum.getWORKNUM_SOCIETY());
        wrkWorkAlbum.setAlbumNo(orcalWrkWorkAlbum.getALBUM_NO());
        wrkWorkAlbum.setAlbumName(orcalWrkWorkAlbum.getALBUM_NO());
        wrkWorkAlbum.setFirstPublish(orcalWrkWorkAlbum.getFIRST_PUB());

        return wrkWorkAlbum;
    }

    private void addOrUpdateWrkArtistMerge() {
        if(null != orcalWrkArtistMergeList && orcalWrkArtistMergeList.size()>0){

            if(null != moneyWrkArtistMergeList && moneyWrkArtistMergeList.size()>0){

                List<String> orcalArtistMergeKeyList = orcalWrkArtistMergeList.stream().map(it -> it.getARTIST_ID() + it.getINPUT_SOC() + it.getORI_ARTIST() + it.getFIRST_PUB()).collect(Collectors.toList());
                List<String> moneyArtistMergeKeyList = moneyWrkArtistMergeList.stream().map(it -> it.getAritistId() + it.getInputSoc() + it.getOriAritist() + it.getFirstPub()).collect(Collectors.toList());

                Map<String, WrkWorkArtistMerge> wrkWorkArtistMergeMap = moneyWrkArtistMergeList.stream().collect(Collectors.toMap(it -> it.getAritistId() + it.getInputSoc() + it.getOriAritist() + it.getFirstPub(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalArtistMergeKeyList = new ArrayList<>(orcalArtistMergeKeyList);
                copyOrcalArtistMergeKeyList.retainAll(moneyArtistMergeKeyList);
                if(null != copyOrcalArtistMergeKeyList && copyOrcalArtistMergeKeyList.size()>0){
                    orcalWrkArtistMergeList.forEach(orcalWrkWorkArtistMerge -> {
                        String key = orcalWrkWorkArtistMerge.getARTIST_ID()+orcalWrkWorkArtistMerge.getINPUT_SOC()+orcalWrkWorkArtistMerge.getORI_ARTIST()+orcalWrkWorkArtistMerge.getFIRST_PUB();
                        if(copyOrcalArtistMergeKeyList.contains(key)){
                            updWrkWorkArtistMergeList.add(orcalAndMoneyArtistMergeChange(orcalWrkWorkArtistMerge,wrkWorkArtistMergeMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalArtistMergeKeyList = new ArrayList<>(orcalArtistMergeKeyList);
                copyAddOrcalArtistMergeKeyList.removeAll(moneyArtistMergeKeyList);
                if(null != copyAddOrcalArtistMergeKeyList && copyAddOrcalArtistMergeKeyList.size()>0){
                    orcalWrkArtistMergeList.forEach(orcalWrkWorkArtistMerge -> {
                        String key = orcalWrkWorkArtistMerge.getARTIST_ID()+orcalWrkWorkArtistMerge.getINPUT_SOC()+orcalWrkWorkArtistMerge.getORI_ARTIST()+orcalWrkWorkArtistMerge.getFIRST_PUB();
                        if(copyAddOrcalArtistMergeKeyList.contains(key)){
                            addWrkWorkArtistMergeList.add(orcalAndMoneyArtistMergeChange(orcalWrkWorkArtistMerge,null));
                        }
                    });
                }

                moneyArtistMergeKeyList.removeAll(orcalArtistMergeKeyList);
                if(null != moneyArtistMergeKeyList && moneyArtistMergeKeyList.size()>0){
                    moneyWrkArtistMergeList.forEach(wrkWorkArtistMerge -> {
                        String key = wrkWorkArtistMerge.getAritistId() + wrkWorkArtistMerge.getInputSoc() + wrkWorkArtistMerge.getOriAritist() + wrkWorkArtistMerge.getFirstPub();
                        if(moneyArtistMergeKeyList.contains(key)){
                            delWrkWorkArtistMergeList.add(wrkWorkArtistMerge);
                        }
                    });
                }

                return;
            }

            orcalWrkArtistMergeList.forEach(orcalWrkWorkArtistMerge -> {
                WrkWorkArtistMerge wrkWorkArtistMerge = orcalAndMoneyArtistMergeChange(orcalWrkWorkArtistMerge,null);
                addWrkWorkArtistMergeList.add(wrkWorkArtistMerge);
            });
        }

    }

    private WrkWorkArtistMerge orcalAndMoneyArtistMergeChange(OrcalWrkWorkArtistMerge orcalWrkWorkArtistMerge,WrkWorkArtistMerge wrkWorkArtistMerge) {
        if(null == wrkWorkArtistMerge || null == wrkWorkArtistMerge.getId()){
            wrkWorkArtistMerge = new WrkWorkArtistMerge();
            wrkWorkArtistMerge.init();
        }else{
            wrkWorkArtistMerge.setAmendTime(new Date());
        }
        Long artistId = orcalWrkWorkArtistMerge.getARTIST_ID().longValue();
        if(null != artistId)
        {
            wrkWorkArtistMerge.setAritistId(artistId);
        }
        String firstPub = orcalWrkWorkArtistMerge.getFIRST_PUB();
        if(StringUtils.isNotBlank(firstPub))
        {
            wrkWorkArtistMerge.setFirstPub(firstPub);
        }
        Integer inputSOC = orcalWrkWorkArtistMerge.getINPUT_SOC();
        if(null != inputSOC) {
            wrkWorkArtistMerge.setInputSoc(inputSOC);
        }
        String oriARTIST = orcalWrkWorkArtistMerge.getORI_ARTIST();
        if(StringUtils.isNotBlank(oriARTIST))
        {
            wrkWorkArtistMerge.setOriAritist(oriARTIST);
        }
        Long workId = orcalWrkWorkArtistMerge.getWORKNUM().longValue();
        if(null != workId)
        {
            wrkWorkArtistMerge.setWorkId(workId);
        }
        Integer workSoc = orcalWrkWorkArtistMerge.getWORKNUM_SOCIETY();
        if(null != workSoc) {
            wrkWorkArtistMerge.setWorkSocietyCode(workSoc);
        }
        wrkWorkArtistMerge.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkArtistMerge.getWORKNUM_SOCIETY(), orcalWrkWorkArtistMerge.getWORKNUM().longValue()));
        wrkWorkArtistMerge.setUniqueKeyMd5(Md5.getMd5(artistId+""));
        return wrkWorkArtistMerge;
    }

    private void addOrUpdateWrkArtist() {
        if(null != orcalWrkArtistList && orcalWrkArtistList.size()>0){
                if(null != moneyWrkArtistList && moneyWrkArtistList.size()>0){

                    // artist 可根据artistId来直接去重
                    List<String> orcalArtistKeyList = orcalWrkArtistList.stream().map(it -> it.getARTIST_ID()+"").collect(Collectors.toList());
//                    List<String> orcalArtistKeyList = orcalWrkArtistList.stream().map(it -> it.getIP_NAME_NO() + it.getREF_ARTIST_ID() + it.getARTIST_ID() + it.getROMAN_NAME()).collect(Collectors.toList());
//                    List<String> moneyArtistKeyList = moneyWrkArtistList.stream().map(it -> it.getIpNameNo() + it.getRefArtistId() + it.getOrcalArtistId() + it.getRomanName()).collect(Collectors.toList());
                    List<String> moneyArtistKeyList = moneyWrkArtistList.stream().map(it -> it.getOrcalArtistId()+"").collect(Collectors.toList());

                    Map<String, WrkArtist> wrkArtistMap = moneyWrkArtistList.stream().collect(Collectors.toMap(it -> it.getOrcalArtistId()+"", Function.identity(), (a, b) -> a));

                    List<String> copyOrcalArtistKeyList = new ArrayList<>(orcalArtistKeyList);
                    copyOrcalArtistKeyList.retainAll(moneyArtistKeyList);
                    if(null !=copyOrcalArtistKeyList && copyOrcalArtistKeyList.size()>0){
                        orcalWrkArtistList.forEach(orcalWrkArtist -> {
                            String key = orcalWrkArtist.getARTIST_ID()+"";
                            if(copyOrcalArtistKeyList.contains(key)){
                                updWrkArtistList.add(orcalAndMoneyArtistChange(orcalWrkArtist,wrkArtistMap.get(key)));
                            }
                        });
                    }

                    List<String> copyAddOrcalArtistKeyList = new ArrayList<>(orcalArtistKeyList);
                    copyAddOrcalArtistKeyList.removeAll(moneyArtistKeyList);
                    if(null != copyAddOrcalArtistKeyList && copyAddOrcalArtistKeyList.size()>0){
                        orcalWrkArtistList.forEach(orcalWrkArtist -> {
                            String key = orcalWrkArtist.getARTIST_ID()+"";
                            if(copyAddOrcalArtistKeyList.contains(key)){
                                addWrkArtistList.add(orcalAndMoneyArtistChange(orcalWrkArtist,null));
                            }
                        });
                    }

                    moneyArtistKeyList.removeAll(orcalArtistKeyList);
                    if(null != moneyArtistKeyList && moneyArtistKeyList.size()>0){
                        moneyWrkArtistList.forEach(wrkArtist -> {
                            String key = wrkArtist.getOrcalArtistId() + "";
                            if(moneyArtistKeyList.contains(key)){
                                delWrkArtistList.add(wrkArtist);
                            }
                        });
                    }

                    return ;
                }

            orcalWrkArtistList.forEach(orcalWrkArtist -> {
                addWrkArtistList.add(orcalAndMoneyArtistChange(orcalWrkArtist,null));
            });
        }


    }

    private WrkArtist orcalAndMoneyArtistChange(OrcalWrkArtist orcalWrkArtist,WrkArtist wrkArtist) {
        if(null == wrkArtist || null == wrkArtist.getId()){
            wrkArtist = new WrkArtist();
            wrkArtist.init();
        }else{
            wrkArtist.setAmendTime(new Date());
        }
        Long artistId = orcalWrkArtist.getARTIST_ID().longValue();
        wrkArtist.setOrcalArtistId(artistId);
        String chineseName = orcalWrkArtist.getCHINESE_NAME();
        wrkArtist.setChineseName(chineseName);
        String firstName = orcalWrkArtist.getFIRST_NAME();
        wrkArtist.setFirstName(firstName);
        String lastName = orcalWrkArtist.getLAST_NAME();
        wrkArtist.setLastName(lastName);
        wrkArtist.setName(lastName+firstName);
        String romanName = orcalWrkArtist.getROMAN_NAME();
        wrkArtist.setRomanName(romanName);
        String ipNameNo = orcalWrkArtist.getIP_NAME_NO();
        wrkArtist.setIpNameNo(ipNameNo);
        Long refArtistId = orcalWrkArtist.getREF_ARTIST_ID().longValue();
        wrkArtist.setRefArtistId(refArtistId);
        String isBand = orcalWrkArtist.getIS_BAND();
        wrkArtist.setIsBand(isBand);
        Integer tisN = orcalWrkArtist.getTIS_N();
        wrkArtist.setTisN(tisN);
        wrkArtist.setUniqueKeyMd5(Md5.getMd5(orcalWrkArtist.getARTIST_ID()+""));
        if (Objects.nonNull(wrkArtist.getRefArtistId())){
            wrkArtist.setRefArtistIdMd5(Md5.getMd5(wrkArtist.getRefArtistId()+""));
        }
        return wrkArtist;
    }

    private void addOrUpdateWrkWorkRight() {
        if(null != orcalWrkWorkRightList && orcalWrkWorkRightList.size()>0){

            if(null != moneyWrkWorkRightList && moneyWrkWorkRightList.size()>0){
                List<String> moneyWorkRightKeyList = moneyWrkWorkRightList.stream().map(it -> it.getRightType() + it.getShareType() + it.getSynIndicator() + it.getWorkDp() + it.getWorkSd()).collect(Collectors.toList());
                List<String> orcalWorkRightKeyList = orcalWrkWorkRightList.stream().map(it -> it.getRIGHT_TYPE() + it.getSHARE_TYPE() + it.getSYN_INDICATOR() + it.getWORK_DP() + it.getWORK_SD()).collect(Collectors.toList());

                Map<String, WrkWorkRight> moneyWorkRightKeyMap = moneyWrkWorkRightList.stream().collect(Collectors.toMap(it -> it.getRightType() + it.getShareType() + it.getSynIndicator() + it.getWorkDp() + it.getWorkSd(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalWorkRightKeyList = new ArrayList<>(orcalWorkRightKeyList);
                copyOrcalWorkRightKeyList.retainAll(moneyWorkRightKeyList);
                if(null != copyOrcalWorkRightKeyList && copyOrcalWorkRightKeyList.size()>0){
                    orcalWrkWorkRightList.forEach(orcalWrkWorkRight -> {
                        String key = orcalWrkWorkRight.getRIGHT_TYPE()+orcalWrkWorkRight.getSHARE_TYPE()+orcalWrkWorkRight.getSYN_INDICATOR()+orcalWrkWorkRight.getWORK_DP()+orcalWrkWorkRight.getWORK_SD();
                        if(copyOrcalWorkRightKeyList.contains(key)){
                            updWrkWorkRightList.add(orcalAndMoneyWorkRightChange(orcalWrkWorkRight,moneyWorkRightKeyMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalWorkRightKeyList = new ArrayList<>(orcalWorkRightKeyList);
                copyAddOrcalWorkRightKeyList.removeAll(moneyWorkRightKeyList);
                if(null != copyAddOrcalWorkRightKeyList && copyAddOrcalWorkRightKeyList.size()>0){
                    orcalWrkWorkRightList.forEach(orcalWrkWorkRight -> {
                        String key = orcalWrkWorkRight.getRIGHT_TYPE()+orcalWrkWorkRight.getSHARE_TYPE()+orcalWrkWorkRight.getSYN_INDICATOR()+orcalWrkWorkRight.getWORK_DP()+orcalWrkWorkRight.getWORK_SD();
                        if(copyAddOrcalWorkRightKeyList.contains(key)){
                            addWrkWorkRightList.add(orcalAndMoneyWorkRightChange(orcalWrkWorkRight,null));
                        }
                    });
                }

                moneyWorkRightKeyList.removeAll(orcalWorkRightKeyList);
                if(null != moneyWorkRightKeyList && moneyWorkRightKeyList.size()>0){
                    moneyWrkWorkRightList.forEach(wrkWorkRight -> {
                       String key = wrkWorkRight.getRightType()+wrkWorkRight.getShareType()+wrkWorkRight.getSynIndicator()+wrkWorkRight.getWorkDp()+wrkWorkRight.getWorkSd();
                       if(moneyWorkRightKeyList.contains(key)){
                           delWrkWorkRightList.add(wrkWorkRight);
                       }
                    });
                }

                return ;
            }

            orcalWrkWorkRightList.forEach(orcalWrkWorkRight -> {
                addWrkWorkRightList.add(orcalAndMoneyWorkRightChange(orcalWrkWorkRight,null));
            });
        }

    }

    private WrkWorkRight orcalAndMoneyWorkRightChange(OrcalWrkWorkRight orcalWrkWorkRight,WrkWorkRight wrkWorkRight) {
        if(null == wrkWorkRight || null == wrkWorkRight.getId()){
            wrkWorkRight = new WrkWorkRight();
            wrkWorkRight.init();
        }else{
            wrkWorkRight.setAmendTime(new Date());
        }

        String distributable = orcalWrkWorkRight.getDISTRIBUTABLE();
        Integer dist = null;
        if(StringUtils.isNotBlank(distributable)){
            if(distributable.equalsIgnoreCase("N"))
            {
                dist = 0;
            }
            if(distributable.equalsIgnoreCase("Y"))
            {
                dist = 1;
            }
        }
        wrkWorkRight.setDistributable(dist);
        wrkWorkRight.setRightType(orcalWrkWorkRight.getRIGHT_TYPE());
        wrkWorkRight.setShareType(orcalWrkWorkRight.getSHARE_TYPE());
        wrkWorkRight.setSynIndicator(orcalWrkWorkRight.getSYN_INDICATOR());
        wrkWorkRight.setWorkDp(orcalWrkWorkRight.getWORK_DP());
        wrkWorkRight.setWorkSd(orcalWrkWorkRight.getWORK_SD());
        wrkWorkRight.setWorkId(Long.valueOf(orcalWrkWorkRight.getWORKNUM()));
        wrkWorkRight.setWorkSociety(orcalWrkWorkRight.getWORKNUM_SOCIETY());
        wrkWorkRight.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkRight.getWORKNUM_SOCIETY(),Long.valueOf(orcalWrkWorkRight.getWORKNUM())));
        return wrkWorkRight;
    }

    private void addOrUpdateWrkWorkComponent() {
        if(null != orcalWrkWorkComponentList && orcalWrkWorkComponentList.size()>0){

            if(null != moneyWrkWorkComponentList && moneyWrkWorkComponentList.size()>0){
                List<String> orcalComponentKeyList = orcalWrkWorkComponentList.stream().map(it -> it.getUSAGE() + it.getSUB_TITLE_ID() + it.getCOMPONENT_WORKNUM() + it.getCOMP_WORKNUM_SOCIETY()).collect(Collectors.toList());
                List<String> moneyComponentKeyList = moneyWrkWorkComponentList.stream().map(it -> it.getUsageType() + it.getSubTitleId() + it.getComponentWorkId() + it.getComWorkSociety()).collect(Collectors.toList());

                Map<String, WrkWorkComponent> wrkWorkComponentMap = moneyWrkWorkComponentList.stream().collect(Collectors.toMap(it -> it.getUsageType() + it.getSubTitleId() + it.getComponentWorkId() + it.getComWorkSociety(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalComponentKeyList = new ArrayList<>(orcalComponentKeyList);
                copyOrcalComponentKeyList.retainAll(moneyComponentKeyList);
                if(null != copyOrcalComponentKeyList && copyOrcalComponentKeyList.size()>0){
                    orcalWrkWorkComponentList.forEach(orcalWrkWorkComponent -> {
                        String key = orcalWrkWorkComponent.getUSAGE() + orcalWrkWorkComponent.getSUB_TITLE_ID() + orcalWrkWorkComponent.getCOMPONENT_WORKNUM() + orcalWrkWorkComponent.getCOMP_WORKNUM_SOCIETY();
                        if(orcalWrkWorkComponentList.contains(key)){
                            updWrkWorkComponentList.add(orcalAndMoneyComponentChange(orcalWrkWorkComponent,wrkWorkComponentMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalComponentKeyList = new ArrayList<>(orcalComponentKeyList);
                copyAddOrcalComponentKeyList.removeAll(moneyComponentKeyList);
                if(null != copyAddOrcalComponentKeyList && copyAddOrcalComponentKeyList.size()>0){
                    orcalWrkWorkComponentList.forEach(orcalWrkWorkComponent -> {
                        String key = orcalWrkWorkComponent.getUSAGE() + orcalWrkWorkComponent.getSUB_TITLE_ID() + orcalWrkWorkComponent.getCOMPONENT_WORKNUM() + orcalWrkWorkComponent.getCOMP_WORKNUM_SOCIETY();
                        if(copyAddOrcalComponentKeyList.contains(key)){
                            addWrkWorkComponentList.add(orcalAndMoneyComponentChange(orcalWrkWorkComponent,null));
                        }
                    });
                }

                moneyComponentKeyList.removeAll(copyAddOrcalComponentKeyList);
                if(null != moneyComponentKeyList && moneyComponentKeyList.size()>0){
                    moneyWrkWorkComponentList.forEach(wrkWorkComponent -> {
                        String key = wrkWorkComponent.getUsageType() + wrkWorkComponent.getSubTitleId() + wrkWorkComponent.getComponentWorkId() + wrkWorkComponent.getComWorkSociety();
                        if(moneyComponentKeyList.contains(key)){
                            delWrkWorkComponentList.add(wrkWorkComponent);
                        }
                    });
                }

                return ;
            }

            orcalWrkWorkComponentList.forEach(orcalWrkWorkComponent -> {
                addWrkWorkComponentList.add(orcalAndMoneyComponentChange(orcalWrkWorkComponent,null));
            });
        }

    }

    private WrkWorkComponent orcalAndMoneyComponentChange(OrcalWrkWorkComponent orcalWrkWorkComponent,WrkWorkComponent wrkWorkComponent) {
        if(null == wrkWorkComponent || null == wrkWorkComponent.getId()){
            wrkWorkComponent = new WrkWorkComponent();
            wrkWorkComponent.init();
        }else{
            wrkWorkComponent.setAmendTime(new Date());
        }
        wrkWorkComponent.setWorkId(orcalWrkWorkComponent.getWORKNUM().longValue());
        wrkWorkComponent.setWorkSocietyCode(orcalWrkWorkComponent.getWORKNUM_SOCIETY());
        wrkWorkComponent.setSubTitleId(orcalWrkWorkComponent.getSUB_TITLE_ID().longValue());
        wrkWorkComponent.setComponentWorkId(orcalWrkWorkComponent.getCOMPONENT_WORKNUM().longValue());
        wrkWorkComponent.setComWorkSociety(orcalWrkWorkComponent.getCOMP_WORKNUM_SOCIETY());
        wrkWorkComponent.setDurationM(orcalWrkWorkComponent.getDURATION_MIN());
        wrkWorkComponent.setDurationS(orcalWrkWorkComponent.getDURATION_SEC());
        wrkWorkComponent.setUsageType(orcalWrkWorkComponent.getUSAGE());
        wrkWorkComponent.setAdjStatusFlag(orcalWrkWorkComponent.getADJ_STATUS_FLAG());
        wrkWorkComponent.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkComponent.getWORKNUM_SOCIETY(), orcalWrkWorkComponent.getWORKNUM().longValue()));
        return wrkWorkComponent;
    }

    private void addOrUpdateWrkWorkSource() {
        if(null != orcalWrkWorkSourceList && orcalWrkWorkSourceList.size()>0){

            if(null != moneyWrkWorkSourceList && moneyWrkWorkSourceList.size()>0){
                List<String> orcalSourceKeyList = orcalWrkWorkSourceList.stream().map(it -> it.getINPUT_SOC() + it.getNOTIFY_SOURCE() + it.getSOURCE_TYPE()).collect(Collectors.toList());
                List<String> moneySourceKeyList = moneyWrkWorkSourceList.stream().map(it -> it.getInputSoc() + it.getNotifySouceId() + it.getSourceType()).collect(Collectors.toList());

                Map<String, WrkWorkSource> wrkWorkSourceMap = moneyWrkWorkSourceList.stream().collect(Collectors.toMap(it -> it.getInputSoc() + it.getNotifySouceId() + it.getSourceType(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalSourceKeyList = new ArrayList<>(orcalSourceKeyList);
                copyOrcalSourceKeyList.retainAll(moneySourceKeyList);
                if(null != copyOrcalSourceKeyList && copyOrcalSourceKeyList.size()>0){
                    orcalWrkWorkSourceList.forEach(orcalWrkWorkSource -> {
                        String key = orcalWrkWorkSource.getINPUT_SOC()+orcalWrkWorkSource.getNOTIFY_SOURCE()+orcalWrkWorkSource.getSOURCE_TYPE();
                        if(copyOrcalSourceKeyList.contains(key)){
                            updWrkWorkSourceList.add(orcalAndMoneySourceChange(orcalWrkWorkSource,wrkWorkSourceMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalSourceKeyList = new ArrayList<>(orcalSourceKeyList);
                copyAddOrcalSourceKeyList.removeAll(moneySourceKeyList);
                if(null != copyAddOrcalSourceKeyList && copyAddOrcalSourceKeyList.size()>0){
                    orcalWrkWorkSourceList.forEach(orcalWrkWorkSource -> {
                        String key = orcalWrkWorkSource.getINPUT_SOC()+orcalWrkWorkSource.getNOTIFY_SOURCE()+orcalWrkWorkSource.getSOURCE_TYPE();
                        if(copyAddOrcalSourceKeyList.contains(key)){
                            updWrkWorkSourceList.add(orcalAndMoneySourceChange(orcalWrkWorkSource,null));
                        }
                    });
                }

                moneySourceKeyList.removeAll(orcalSourceKeyList);

                if(null != moneySourceKeyList && moneySourceKeyList.size()>0){
                    moneyWrkWorkSourceList.forEach(wrkWorkSource -> {
                        String key = wrkWorkSource.getInputSoc()+wrkWorkSource.getNotifySouceId()+wrkWorkSource.getSourceType();
                        if(moneySourceKeyList.contains(key))
                        {
                            delWrkWorkSourceList.add(wrkWorkSource);
                        }
                    });
                }

                return;

            }

            orcalWrkWorkSourceList.forEach(orcalWrkWorkSource -> {
                addWrkWorkSourceList.add(orcalAndMoneySourceChange(orcalWrkWorkSource,null));
            });
        }

    }

    private WrkWorkSource orcalAndMoneySourceChange(OrcalWrkWorkSource orcalWrkWorkSource,WrkWorkSource wrkWorkSource) {
        if(null == wrkWorkSource || null == wrkWorkSource.getId()){
            wrkWorkSource = new WrkWorkSource();
            wrkWorkSource.init();
        }else{
            wrkWorkSource.setAmendTime(new Date());
        }
        wrkWorkSource.setWorkId(orcalWrkWorkSource.getWORKNUM().longValue());
        wrkWorkSource.setWorkSocietyCode(orcalWrkWorkSource.getWORKNUM_SOCIETY());
        wrkWorkSource.setSourceType(orcalWrkWorkSource.getSOURCE_TYPE());
        wrkWorkSource.setNotifyDate(orcalWrkWorkSource.getNOTIFY_DATE());
        wrkWorkSource.setNotifySouceId(orcalWrkWorkSource.getNOTIFY_SOURCE());
        wrkWorkSource.setInputSoc(orcalWrkWorkSource.getINPUT_SOC());
        wrkWorkSource.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkSource.getWORKNUM_SOCIETY(), orcalWrkWorkSource.getWORKNUM().longValue()));
        return wrkWorkSource;
    }

    private void addOrUpdateWrkWorkIsrc() {
        if(null != orcalWrkIsrcList && orcalWrkIsrcList.size()>0){

            if(null != moneyWrkIsrcList && moneyWrkIsrcList.size()>0){
                List<String> orcalIsrcKeyList = orcalWrkIsrcList.stream().map(it -> it.getISRC() + it.getINPUT_SOC()).collect(Collectors.toList());
                List<String> moneyIsrcKeyList = moneyWrkIsrcList.stream().map(it -> it.getIsrc() + it.getInputSoc()).collect(Collectors.toList());

                Map<String, WrkIsrc> orcalWrkIsrcMap = moneyWrkIsrcList.stream().collect(Collectors.toMap(it -> it.getIsrc() + it.getInputSoc(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalIsrcKeyList = new ArrayList<>(orcalIsrcKeyList);
                copyOrcalIsrcKeyList.retainAll(moneyIsrcKeyList);
                if(null != copyOrcalIsrcKeyList && copyOrcalIsrcKeyList.size()>0){
                    orcalWrkIsrcList.forEach(orcalWrkIsrc -> {
                        String key = orcalWrkIsrc.getISRC()+orcalWrkIsrc.getINPUT_SOC();
                        if(copyOrcalIsrcKeyList.contains(key)){
                            updWrkIsrcList.add(orcalAndMoneyWrkIsrcChange(orcalWrkIsrc,orcalWrkIsrcMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalIsrcKeyList = new ArrayList<>(orcalIsrcKeyList);
                copyAddOrcalIsrcKeyList.removeAll(moneyIsrcKeyList);
                if(null != moneyIsrcKeyList && moneyIsrcKeyList.size()>0){
                    orcalWrkIsrcList.forEach(orcalWrkIsrc -> {
                        String key = orcalWrkIsrc.getISRC()+orcalWrkIsrc.getINPUT_SOC();
                        if(copyAddOrcalIsrcKeyList.contains(key)){
                            addWrkIsrcList.add(orcalAndMoneyWrkIsrcChange(orcalWrkIsrc,null));
                        }
                    });
                }

                moneyIsrcKeyList.removeAll(orcalIsrcKeyList);
                if(null != moneyIsrcKeyList && moneyIsrcKeyList.size()>0){
                    moneyWrkIsrcList.forEach(wrkIsrc -> {
                        String key = wrkIsrc.getIsrc()+wrkIsrc.getInputSoc();
                        if(moneyIsrcKeyList.contains(key)){
                            delWrkIsrcList.add(wrkIsrc);
                        }
                    });
                }
                return;
            }

            orcalWrkIsrcList.forEach(orcalWrkIsrc -> {
                WrkIsrc wrkIsrc = orcalAndMoneyWrkIsrcChange(orcalWrkIsrc,null);
                addWrkIsrcList.add(wrkIsrc);
            });
        }

    }

    private WrkIsrc orcalAndMoneyWrkIsrcChange(OrcalWrkIsrc orcalWrkIsrc,WrkIsrc wrkIsrc) {
        if(null == wrkIsrc || null == wrkIsrc.getId()){
            wrkIsrc = new WrkIsrc();
            wrkIsrc.init();
        }else{
            wrkIsrc.setAmendTime(new Date());
        }
        wrkIsrc.setInputSoc(orcalWrkIsrc.getINPUT_SOC());
        wrkIsrc.setIsrc(orcalWrkIsrc.getISRC());
        wrkIsrc.setWorkId(orcalWrkIsrc.getWORKNUM().longValue());
        wrkIsrc.setWorkSociety(orcalWrkIsrc.getWORKNUM_SOCIETY());
        wrkIsrc.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkIsrc.getWORKNUM_SOCIETY(), orcalWrkIsrc.getWORKNUM().longValue()));
        return wrkIsrc;
    }

    private void addOrUpdateWrkWorkRemark() {
        if(null != orcalWrkWorkRemarkList && orcalWrkWorkRemarkList.size()>0){

            if(null != moneyWrkWorkRemarkList && moneyWrkWorkRemarkList.size()>0){

                List<String> orcalRemarkKeyList = orcalWrkWorkRemarkList.stream().map(it -> it.getINPUT_SOC() + it.getREMARK()).collect(Collectors.toList());
                List<String> moneyRemarkKeyList = moneyWrkWorkRemarkList.stream().map(it -> it.getInputSoc() + it.getRemark()).collect(Collectors.toList());

                Map<String, WrkWorkRemark> orcalWrkWorkRemarkMap = moneyWrkWorkRemarkList.stream().collect(Collectors.toMap(it -> it.getInputSoc() + it.getRemark(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalRemarkKeyList = new ArrayList<>(orcalRemarkKeyList);
                copyOrcalRemarkKeyList.retainAll(moneyRemarkKeyList);
                if(null != copyOrcalRemarkKeyList && copyOrcalRemarkKeyList.size()>0){
                    orcalWrkWorkRemarkList.forEach(orcalWrkWorkRemark -> {
                        String key = orcalWrkWorkRemark.getINPUT_SOC()+orcalWrkWorkRemark.getREMARK();
                        if(copyOrcalRemarkKeyList.contains(key)){
                            updWrkWorkRemarkList.add(orcalAndMoneyWorkRemarkChange(orcalWrkWorkRemark,orcalWrkWorkRemarkMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalRemarkKeyList = new ArrayList<>(orcalRemarkKeyList);
                copyAddOrcalRemarkKeyList.removeAll(moneyRemarkKeyList);
                if(null != copyAddOrcalRemarkKeyList && copyAddOrcalRemarkKeyList.size()>0){
                    orcalWrkWorkRemarkList.forEach(orcalWrkWorkRemark -> {
                        String key = orcalWrkWorkRemark.getINPUT_SOC()+orcalWrkWorkRemark.getREMARK();
                        if(copyAddOrcalRemarkKeyList.contains(key)){
                            addWrkWorkRemarkList.add(orcalAndMoneyWorkRemarkChange(orcalWrkWorkRemark,null));
                        }
                    });
                }

                moneyRemarkKeyList.removeAll(orcalRemarkKeyList);
                if(null != moneyRemarkKeyList && moneyRemarkKeyList.size()>0){
                    moneyWrkWorkRemarkList.forEach(wrkWorkRemark -> {
                        String key = wrkWorkRemark.getInputSoc() + wrkWorkRemark.getRemark();
                        if(moneyRemarkKeyList.contains(key)){
                            delWrkWorkRemarkList.add(wrkWorkRemark);
                        }
                    });
                }

                return ;
            }

            orcalWrkWorkRemarkList.forEach(orcalWrkWorkRemark -> {
                addWrkWorkRemarkList.add(orcalAndMoneyWorkRemarkChange(orcalWrkWorkRemark,null));
            });
        }
    }

    private WrkWorkRemark orcalAndMoneyWorkRemarkChange(OrcalWrkWorkRemark orcalWrkWorkRemark,WrkWorkRemark wrkWorkRemark) {
        if(null == wrkWorkRemark || null == wrkWorkRemark.getId()){
            wrkWorkRemark = new WrkWorkRemark();
            wrkWorkRemark.init();
        }else{
            wrkWorkRemark.setAmendTime(new Date());
        }
        wrkWorkRemark.setInputSoc(orcalWrkWorkRemark.getINPUT_SOC());
        wrkWorkRemark.setRemark(orcalWrkWorkRemark.getREMARK());
        wrkWorkRemark.setWorkId(orcalWrkWorkRemark.getWORKNUM().longValue());
        wrkWorkRemark.setWorkSocietyCode(orcalWrkWorkRemark.getWORKNUM_SOCIETY());
        wrkWorkRemark.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkRemark.getWORKNUM_SOCIETY(), orcalWrkWorkRemark.getWORKNUM().longValue()));
        return wrkWorkRemark;
    }

    private WrkWorkIpShare orcalAndMoneyWrkIpShareChange(Map<String, String> ipNameNoBaseNoMap, OrcalWrkWorkIpShare orcalWrkWorkIpShare,WrkWorkIpShare wrkWorkIpShare) {
        if(null == wrkWorkIpShare || null == wrkWorkIpShare.getId()){
            wrkWorkIpShare = new WrkWorkIpShare();
            wrkWorkIpShare.init();
        }else{
            wrkWorkIpShare.setAmendTime(new Date());
        }

        wrkWorkIpShare.setAgrNo(orcalWrkWorkIpShare.getAGR_NO());
        wrkWorkIpShare.setWorkId(orcalWrkWorkIpShare.getWORKNUM().longValue());
        wrkWorkIpShare.setWorkSocietyCode(orcalWrkWorkIpShare.getWORKNUM_SOCIETY());
        String ip_NAME_NO = orcalWrkWorkIpShare.getIP_NAME_NO();
        if (StringUtils.isNotBlank(ip_NAME_NO)) {
            wrkWorkIpShare.setIpNameNo(orcalWrkWorkIpShare.getIP_NAME_NO());
            String ipBaseNo = ipNameNoBaseNoMap.get(ip_NAME_NO);
            if(StringUtils.isNotBlank(ipBaseNo)){
                wrkWorkIpShare.setIpBaseNo(ipBaseNo);
            }
        }
        wrkWorkIpShare.setRightType(orcalWrkWorkIpShare.getRIGHT_TYPE());
        wrkWorkIpShare.setIpShare(orcalWrkWorkIpShare.getIP_SHARE());
        wrkWorkIpShare.setWorkIpRole(orcalWrkWorkIpShare.getWORK_IP_ROLE());
        wrkWorkIpShare.setSd(orcalWrkWorkIpShare.getSD());
        wrkWorkIpShare.setGroupIndicator(orcalWrkWorkIpShare.getGROUP_INDICATOR());
        Integer sipLinkId = orcalWrkWorkIpShare.getSIP_LINK_ID();
        if (null != sipLinkId){
            wrkWorkIpShare.setSipLinkId(orcalWrkWorkIpShare.getSIP_LINK_ID().longValue());
        }
        else{
            wrkWorkIpShare.setSipLinkId(0L);
        }
        wrkWorkIpShare.setDummyNameRoman(orcalWrkWorkIpShare.getDUMMY_NAME_ROMAN());
        wrkWorkIpShare.setOipLinkId(orcalWrkWorkIpShare.getOIP_LINK_ID() == null ? 0L : orcalWrkWorkIpShare.getOIP_LINK_ID().longValue());
        wrkWorkIpShare.setIpType(orcalWrkWorkIpShare.getIP_TYPE());
        wrkWorkIpShare.setOrgWriterShare(orcalWrkWorkIpShare.getORG_WRITER_SHARE());
        wrkWorkIpShare.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkIpShare.getWORKNUM_SOCIETY(), orcalWrkWorkIpShare.getWORKNUM().longValue()));
        return wrkWorkIpShare;
    }

    private void addOrUpdateWrkWorkIpShare(Map<String, String> ipNameNoBaseNoMap) {
        if(null != orcalWrkWorkIpShareList && orcalWrkWorkIpShareList.size()>0){
            // TODO TEST
//            delWrkWorkIpShareList.addAll(moneyWrkWorkIpShareList);
//            moneyWrkWorkIpShareList = new ArrayList<>();
            if(null != moneyWrkWorkIpShareList && moneyWrkWorkIpShareList.size()>0){
                orcalWrkWorkIpShareList.stream().forEach(orcalWrkWorkIpShare -> {
                    orcalWrkWorkIpShare.setOIP_LINK_ID(orcalWrkWorkIpShare.getOIP_LINK_ID()==null?0:orcalWrkWorkIpShare.getOIP_LINK_ID());
                });
                moneyWrkWorkIpShareList.stream().forEach(moneyWrkWorkIpShare -> {
                    moneyWrkWorkIpShare.setOipLinkId(moneyWrkWorkIpShare.getOipLinkId()==null?0L:moneyWrkWorkIpShare.getOipLinkId());
                });

                List<String> orcalIpShareKeyList = orcalWrkWorkIpShareList.stream().map(it ->it.getWORK_IP_ROLE()+it.getOIP_LINK_ID()+ it.getIP_NAME_NO() + it.getRIGHT_TYPE() + it.getGROUP_INDICATOR()).collect(Collectors.toList());
                List<String> moneyIpShareKeyList = moneyWrkWorkIpShareList.stream().map(it ->it.getWorkIpRole()+it.getOipLinkId() + it.getIpNameNo() + it.getRightType() + it.getGroupIndicator()).collect(Collectors.toList());

                Map<String, WrkWorkIpShare> wrkWorkIpShareMap = moneyWrkWorkIpShareList.stream().collect(Collectors.toMap(it ->it.getWorkIpRole()+ it.getOipLinkId() + it.getIpNameNo() + it.getRightType() + it.getGroupIndicator(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalIpShareKeyList = new ArrayList<>(orcalIpShareKeyList);
                copyOrcalIpShareKeyList.retainAll(moneyIpShareKeyList);
                if(null != copyOrcalIpShareKeyList && copyOrcalIpShareKeyList.size()>0){
                    orcalWrkWorkIpShareList.forEach(orcalWrkWorkIpShare -> {
                        String key = createIpShareKey(orcalWrkWorkIpShare);
                        if(copyOrcalIpShareKeyList.contains(key)){
                            updWrkWorkIpShareList.add(orcalAndMoneyWrkIpShareChange(ipNameNoBaseNoMap, orcalWrkWorkIpShare,wrkWorkIpShareMap.get(key)));
                        }
                    });
                }

                List<String> copyAddOrcalIpShareKeyList = new ArrayList<>(orcalIpShareKeyList);
                copyAddOrcalIpShareKeyList.removeAll(moneyIpShareKeyList);
                if(null != copyAddOrcalIpShareKeyList && copyAddOrcalIpShareKeyList.size()>0){
                    orcalWrkWorkIpShareList.forEach(orcalWrkWorkIpShare -> {
                        String key = createIpShareKey(orcalWrkWorkIpShare);
                        if(copyAddOrcalIpShareKeyList.contains(key)){
                            addWrkWorkIpShareList.add(orcalAndMoneyWrkIpShareChange(ipNameNoBaseNoMap,orcalWrkWorkIpShare,null));
                        }
                    });
                }

                moneyIpShareKeyList.removeAll(orcalIpShareKeyList);
                if(null != moneyIpShareKeyList && moneyIpShareKeyList.size()>0){
                    moneyWrkWorkIpShareList.forEach(wrkWorkIpShare -> {
                        String key = wrkWorkIpShare.getWorkIpRole()+ wrkWorkIpShare.getOipLinkId() + wrkWorkIpShare.getIpNameNo() + wrkWorkIpShare.getRightType() + wrkWorkIpShare.getGroupIndicator();
                        if(moneyIpShareKeyList.contains(key)){
                            delWrkWorkIpShareList.add(wrkWorkIpShare);
                        }
                    });
                }
                return ;
            }

            orcalWrkWorkIpShareList.forEach(orcalWrkWorkIpShare -> {
                WrkWorkIpShare wrkWorkIpShare = orcalAndMoneyWrkIpShareChange(ipNameNoBaseNoMap, orcalWrkWorkIpShare,null);
                addWrkWorkIpShareList.add(wrkWorkIpShare);
            });
        }

    }

    private String createIpShareKey(OrcalWrkWorkIpShare orcalWrkWorkIpShare) {
        return orcalWrkWorkIpShare.getWORK_IP_ROLE()+orcalWrkWorkIpShare.getOIP_LINK_ID() + orcalWrkWorkIpShare.getIP_NAME_NO() + orcalWrkWorkIpShare.getRIGHT_TYPE() + orcalWrkWorkIpShare.getGROUP_INDICATOR();
    }

    private void addOrUpdateWrkWorkTitle() {
        if(null != orcalWrkWorkTitleList && orcalWrkWorkTitleList.size()>0){
            Map<Integer, OrcalWrkTvSeries> orcalWrkTvSeriesMap = new HashMap<>();
            if(null != orcalWrkTvSeriesList && orcalWrkTvSeriesList.size()>0){
                orcalWrkTvSeriesMap = orcalWrkTvSeriesList.stream().collect(Collectors.toMap(OrcalWrkTvSeries::getSUB_TITLE_ID, Function.identity(), (a, b) -> a));
            }
            Map<Integer, OrcalWrkTvSeries> finalOrcalWrkTvSeriesMap = orcalWrkTvSeriesMap;

            if(null != moneyWrkWorkTitleList && moneyWrkWorkTitleList.size()>0){

                List<String> orcalTitleKeyList = orcalWrkWorkTitleList.stream().map(it -> it.getSUB_TITLE_ID() + it.getC_TITLE() + it.getE_TITLE() + it.getLANGUAGE_CODE()).collect(Collectors.toList());
                List<String> moneyTitleKeyList = moneyWrkWorkTitleList.stream().map(it -> it.getSubTitleId() + it.getTitle() + it.getTitleEn() + it.getLanguageCode()).collect(Collectors.toList());

                Map<String, WrkWorkTitle> orcalWrkWorkTitleMap = moneyWrkWorkTitleList.stream().collect(Collectors.toMap(it -> it.getSubTitleId() + it.getTitle() + it.getTitleEn() + it.getLanguageCode(), Function.identity(), (a, b) -> a));

                List<String> copyOrcalTitleKeyList = new ArrayList<>(orcalTitleKeyList);
                copyOrcalTitleKeyList.retainAll(moneyTitleKeyList); // 更新操作 保留已经存在的title
                if(null != copyOrcalTitleKeyList && copyOrcalTitleKeyList.size()>0){
                    orcalWrkWorkTitleList.forEach(orcalWrkWorkTitle -> {
                        String titleKey = createTitleKey(orcalWrkWorkTitle);
                        // 需要更新
                        if(copyOrcalTitleKeyList.contains(titleKey)){
                            WrkWorkTitle wrkWorkTitle = orcalWrkWorkTitleMap.get(titleKey);
                            updWrkWorkTitleList.add(orcalAndMoneyWorkTitleChange(finalOrcalWrkTvSeriesMap, orcalWrkWorkTitle,wrkWorkTitle));
                        }
                    });
                }

                List<String> copyOrcalTitleKeyListForAdd = new ArrayList<>(orcalTitleKeyList);
                copyOrcalTitleKeyListForAdd.removeAll(moneyTitleKeyList); // 新增操作 删除已经存在的title
                if(null != copyOrcalTitleKeyList && copyOrcalTitleKeyList.size()>0){
                    orcalWrkWorkTitleList.forEach(orcalWrkWorkTitle -> {
                        String titleKey = createTitleKey(orcalWrkWorkTitle);
                        // 需要新增
                        if(copyOrcalTitleKeyListForAdd.contains(titleKey)){
                            WrkWorkTitle wrkWorkTitle = orcalAndMoneyWorkTitleChange(finalOrcalWrkTvSeriesMap, orcalWrkWorkTitle,null);
                            addWrkWorkTitleList.add(wrkWorkTitle);
                        }
                    });
                }

                moneyTitleKeyList.removeAll(orcalTitleKeyList); // 需要删除的
                if(null != moneyTitleKeyList && moneyTitleKeyList.size()>0){
                    moneyWrkWorkTitleList.forEach(wrkWorkTitle -> {
                        String wrkWorkTitleKey = wrkWorkTitle.getSubTitleId() + wrkWorkTitle.getTitle() + wrkWorkTitle.getTitleEn() + wrkWorkTitle.getLanguageCode();
                        if(moneyTitleKeyList.contains(wrkWorkTitleKey)){
                            delWrkWorkTitleList.add(wrkWorkTitle);
                        }
                    });
                }

                return;
            }
//            Map<Integer, OrcalWrkTvSeries> finalOrcalWrkTvSeriesMap = orcalWrkTvSeriesMap;
            orcalWrkWorkTitleList.forEach(orcalWrkWorkTitle -> {
                WrkWorkTitle wrkWorkTitle = orcalAndMoneyWorkTitleChange(finalOrcalWrkTvSeriesMap, orcalWrkWorkTitle,null);
                addWrkWorkTitleList.add(wrkWorkTitle);
            });
        }
    }

    private WrkWorkTitle orcalAndMoneyWorkTitleChange(Map<Integer, OrcalWrkTvSeries> finalOrcalWrkTvSeriesMap, OrcalWrkWorkTitle orcalWrkWorkTitle,WrkWorkTitle wrkWorkTitle) {
        if(null == wrkWorkTitle || null == wrkWorkTitle.getId()){
            wrkWorkTitle = new WrkWorkTitle();
            wrkWorkTitle.init();
        }else{
            wrkWorkTitle.setAmendTime(new Date());
        }
        if (orcalWrkWorkTitle.getWORKNUM() != null){
            wrkWorkTitle.setWorkId(orcalWrkWorkTitle.getWORKNUM().longValue());
        }
        if (orcalWrkWorkTitle.getWORKNUM_SOCIETY() != null)
        {
            wrkWorkTitle.setWorkSocietyCode(orcalWrkWorkTitle.getWORKNUM_SOCIETY());
        }
        if (orcalWrkWorkTitle.getSUB_TITLE_ID() != null)
        {
            Integer subTitleId = orcalWrkWorkTitle.getSUB_TITLE_ID();
            OrcalWrkTvSeries orcalWrkTvSeries = finalOrcalWrkTvSeriesMap.get(subTitleId);
            if(null != orcalWrkTvSeries){
                wrkWorkTitle.setEpisodeNo(orcalWrkTvSeries.getE_ESP_NO());
            }

            wrkWorkTitle.setSubTitleId(subTitleId.longValue());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getE_TITLE()))
        {
            wrkWorkTitle.setTitleEn(orcalWrkWorkTitle.getE_TITLE());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getC_TITLE()))
        {
            wrkWorkTitle.setTitle(orcalWrkWorkTitle.getC_TITLE());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getTITLE_CODE()))
        {
            wrkWorkTitle.setTitleType(orcalWrkWorkTitle.getTITLE_CODE());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getGENRE_DETAIL()))
        {
            wrkWorkTitle.setGenreCode(orcalWrkWorkTitle.getGENRE_DETAIL());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getLANGUAGE_CODE()))
        {
            wrkWorkTitle.setLanguageCode(orcalWrkWorkTitle.getLANGUAGE_CODE());
        }
        if (null != orcalWrkWorkTitle.getDURATION_MIN())
        {
            wrkWorkTitle.setDurationM(orcalWrkWorkTitle.getDURATION_MIN());
        }
        if (null != orcalWrkWorkTitle.getDURATION_SEC())
        {
            wrkWorkTitle.setDurationS(orcalWrkWorkTitle.getDURATION_SEC());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getGENRE_DTL_FLAG()))
        {
            wrkWorkTitle.setGenerDtlFlag(orcalWrkWorkTitle.getGENRE_DTL_FLAG());
        }
        if (StringUtils.isNotBlank(orcalWrkWorkTitle.getTRANSFER()))
        {
            wrkWorkTitle.setTransfer(orcalWrkWorkTitle.getTRANSFER());
        }
        if (null != orcalWrkWorkTitle.getINPUT_SOC())
        {
            wrkWorkTitle.setInputSoc(orcalWrkWorkTitle.getINPUT_SOC());
        }
        wrkWorkTitle.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkTitle.getWORKNUM_SOCIETY(), orcalWrkWorkTitle.getWORKNUM().longValue()));
        return wrkWorkTitle;
    }

    private String createTitleKey(OrcalWrkWorkTitle orcalWrkWorkTitle) {
        return orcalWrkWorkTitle.getSUB_TITLE_ID() + orcalWrkWorkTitle.getC_TITLE() + orcalWrkWorkTitle.getE_TITLE() + orcalWrkWorkTitle.getLANGUAGE_CODE();
    }

    private void addOrUpdateWrkWork() {
        if(null == moneyWrkWork){
            moneyWrkWork = new WrkWork();
            moneyWrkWork.init();
        }else{
            moneyWrkWork.setAmendTime(new Date());
        }
        orcalAndMoneyWrkWorkChange();
    }

    private void orcalAndMoneyWrkWorkChange() {
        Long workNum = orcalWrkWork.getWORKNUM() == null ? null : orcalWrkWork.getWORKNUM().longValue();
        moneyWrkWork.setWorkId(workNum);
        Integer workNumSoc = orcalWrkWork.getWORKNUM_SOCIETY();
        moneyWrkWork.setWorkSocietyCode(workNumSoc);
        String orcalWorkType = orcalWrkWork.getWorkType();
        moneyWrkWork.setWorkType(orcalWorkType);
        String director = orcalWrkWork.getDIRECTOR();
        moneyWrkWork.setDirector(director);
        Long refWorkId = orcalWrkWork.getREF_WORKNUM() == null ? null : orcalWrkWork.getREF_WORKNUM().longValue();
        moneyWrkWork.setRefWorkId(refWorkId);
        moneyWrkWork.setRefWorkSociety(orcalWrkWork.getREF_WORKNUM_SOCIETY());
        String synInd = orcalWrkWork.getRIGHT_SYN_INDICATOR();
        if (StringUtils.isNotBlank(synInd)) {
            if ("Y".equals(synInd)) {
                moneyWrkWork.setRightSynIndicator(1);
            } else {
                moneyWrkWork.setRightSynIndicator(0);
            }
        } else {
            moneyWrkWork.setRightSynIndicator(0);
        }
        moneyWrkWork.setFirstCreateDate(orcalWrkWork.getFIRST_CREATE_DATE() == null ? null
                : new java.sql.Timestamp(orcalWrkWork.getFIRST_CREATE_DATE().getTime()));
        moneyWrkWork.setCueNo(orcalWrkWork.getCUE_NO());
        String productCompany = orcalWrkWork.getPRODUCT_COMPANY();
        moneyWrkWork.setProductCompany(productCompany);
        Integer productionYear = orcalWrkWork.getPRODUCTION_YR();
        moneyWrkWork.setProductionYear(productionYear);
        String societyWorkCode = orcalWrkWork.getSOCIETY_WORK_CODE();
        moneyWrkWork.setSocietyWorkCode(societyWorkCode);
        Integer isan = orcalWrkWork.getISAN();
        moneyWrkWork.setIsan(isan);
        moneyWrkWork.setPublishAirDate(orcalWrkWork.getPUBLISH_AIR_DATE() == null ? null
                : new java.sql.Timestamp(orcalWrkWork.getPUBLISH_AIR_DATE().getTime()));
        String productionNo = orcalWrkWork.getPRODUCTION_NO();
        moneyWrkWork.setProductionNo(productionNo);
        String performLanguage = orcalWrkWork.getPERFORM_LANGUAGE();
        moneyWrkWork.setPerformLanguage(performLanguage);
        String amendUser = orcalWrkWork.getLST_AMEND_USER();
        moneyWrkWork.setAmendUser(amendUser);
        String distFlag = orcalWrkWork.getDIST_FLAG();
        if (StringUtils.isNotBlank(distFlag)) {
            if ("Y".equals(distFlag)) {
                moneyWrkWork.setDistFlag(1);
            } else {
                moneyWrkWork.setDistFlag(0);
            }
        } else {
            moneyWrkWork.setDistFlag(0);
        }
        String remark = orcalWrkWork.getREMARK();
        moneyWrkWork.setRemark(remark);
        String local = orcalWrkWork.getBELONG_TO();
        moneyWrkWork.setLocal(local);
        moneyWrkWork.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(workNumSoc, workNum.longValue()));
        if (null != orcalWrkIswcList && orcalWrkIswcList.size()>0) {
            moneyWrkWork.setISWC(orcalWrkIswcList.get(0).getISWC());
        }
    }

    public void init() {
        addWrkWorkTitleList = new ArrayList<>();
        updWrkWorkTitleList= new ArrayList<>();
        delWrkWorkTitleList= new ArrayList<>();

        addWrkWorkIpShareList = new ArrayList<>();
        updWrkWorkIpShareList= new ArrayList<>();
        delWrkWorkIpShareList= new ArrayList<>();

         addWrkWorkRemarkList= new ArrayList<>();
         updWrkWorkRemarkList= new ArrayList<>();
         delWrkWorkRemarkList= new ArrayList<>();

        addWrkIsrcList= new ArrayList<>();
        updWrkIsrcList= new ArrayList<>();
        delWrkIsrcList= new ArrayList<>();

        addWrkWorkSourceList= new ArrayList<>();
        updWrkWorkSourceList= new ArrayList<>();
        delWrkWorkSourceList= new ArrayList<>();

        addWrkWorkComponentList= new ArrayList<>();
        updWrkWorkComponentList= new ArrayList<>();
        delWrkWorkComponentList= new ArrayList<>();

        addWrkWorkRightList= new ArrayList<>();
        updWrkWorkRightList= new ArrayList<>();
        delWrkWorkRightList= new ArrayList<>();

        addWrkArtistList= new ArrayList<>();
        updWrkArtistList= new ArrayList<>();
        delWrkArtistList= new ArrayList<>();

        addWrkWorkArtistMergeList= new ArrayList<>();
        updWrkWorkArtistMergeList= new ArrayList<>();
        delWrkWorkArtistMergeList= new ArrayList<>();

        addWrkWorkAlbumList = new ArrayList<>();
        updWrkWorkAlbumList = new ArrayList<>();
        delWrkWorkAlbumList = new ArrayList<>();

    }

}
