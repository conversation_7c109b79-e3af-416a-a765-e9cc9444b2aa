package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimReportSummary;

import java.util.List;

@Repository
public interface ClaimReportSummaryMapper extends BaseMapper<ClaimReportSummary> {


    List<ClaimReportSummary> generateReportSummaryByClaimResultCcid(@Param("headerId") Long headerId,@Param("fileBaseIds")List<Long> fileBaseIds);

    List<ClaimReportSummary> generateReportSummaryByMatchDone(@Param("fileBaseIds") List<Long> fileBaseIds);
}