package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

@Table(name = "`dist_distribution_check`")
public class DistDistributionCheck extends BaseEntity {

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 错误编码
     */
    @Column(name = "`error_code`")
    private String errorCode;

    /**
     * LST\WRK\DIS\
     */
    @Column(name = "`error_type`")
    private String errorType;

    /**
     * 
     */
    @Column(name = "`item_soc`")
    private Integer itemSoc;

    /**
     * 
     */
    @Column(name = "`from_soc`")
    private Integer fromSoc;

    /**
     * 错误内容
     */
    @Column(name = "`error_content`")
    private String errorContent;

    /**
     * 错误是否处理，Y  or N
     */
    @Column(name = "`checked`")
    private String checked;

    /**
     * 错误项id
     */
    @Column(name = "`item_id`")
    private String itemId;

    /**
     * 
     */
    @Column(name = "`error_ext_json`")
    private String errorExtJson;

    @Transient
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public Integer getItemSoc() {
        return itemSoc;
    }

    public void setItemSoc(Integer itemSoc) {
        this.itemSoc = itemSoc;
    }

    public Integer getFromSoc() {
        return fromSoc;
    }

    public void setFromSoc(Integer fromSoc) {
        this.fromSoc = fromSoc;
    }

    public String getErrorContent() {
        return errorContent;
    }

    public void setErrorContent(String errorContent) {
        this.errorContent = errorContent;
    }

    public String getChecked() {
        return checked;
    }

    public void setChecked(String checked) {
        this.checked = checked;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getErrorExtJson() {
        return errorExtJson;
    }

    public void setErrorExtJson(String errorExtJson) {
        this.errorExtJson = errorExtJson;
    }



}