package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimMinimaInfo;

import java.util.List;

@Repository
public interface ClaimMinimaInfoMapper extends BaseMapper<ClaimMinimaInfo> {

    List<String> getProductShortNameWithSetId(@Param("setId") Long setId);


}