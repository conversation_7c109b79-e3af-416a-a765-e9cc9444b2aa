package tw.org.must.must.core.parse;

import com.firstbrave.api.base.CwrRecord;
import com.firstbrave.api.base.CwrTransactionRecord;
import com.firstbrave.api.cwr.vo.*;
import com.firstbrave.api.parser.CwrFileDecoder;
import com.firstbrave.api.parser.CwrFileEncoder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.route.spi.SPITimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.exception.CustomException;
import tw.org.must.must.core.service.cwr.*;
import tw.org.must.must.core.service.mbr.MbrIpAgreementTerritoryService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.wrk.*;
import tw.org.must.must.model.cwr.*;
import tw.org.must.must.model.mbr.MbrIpAgreementTerritory;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.wrk.WrkArtist;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;
import tw.org.must.must.model.wrk.vo.WorkSocietyVo;

import java.nio.charset.MalformedInputException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ParseCwrService {

    @Autowired
    private CwrWorkService cwrWorkService;

    @Autowired
    private CwrWorkTitleService cwrWorkTitleService;

    @Autowired
    private CwrPublisherService cwrPublisherService;

    @Autowired
    private CwrPublisherTerritoryService cwrPublisherTerritoryService;

    @Autowired
    private CwrWriterService cwrWriterService;

    @Autowired
    private CwrWriterTerritoryService cwrWriterTerritoryService;

    @Autowired
    private CwrArtistService cwrArtistService;

    @Autowired
    private CwrPublisherWriterService cwrPublisherWriterService;

    @Autowired
    private WrkWorkService wrkWorkService;

    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;

    @Autowired
    private WrkArtistService wrkArtistService;

    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;
    @Autowired
    private MbrIpNameService mbrIpNameService;
    @Autowired
    private MbrIpAgreementTerritoryService mbrIpAgreementTerritoryService ;

    final String ASSCII_REGX = "\\A\\p{ASCII}*\\z";
    /**
     * 解析AVR文件
     *
     * @param file
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public String parse(Long fId,String file) {
        StringBuffer errorMsg = new StringBuffer();
        Path path = Paths.get(file);
        if (!path.toFile().exists()) {
            errorMsg.append("cwr 文件不存在");
            return errorMsg.toString();
        }
        try {
            List<String> lines = Files.readAllLines(path);
            List<CwrRecord> records = CwrFileDecoder.decode(new LinkedList<>(lines));
            if (!records.isEmpty()) {
                insertDb(fId,records);
            }
            return errorMsg.toString();
        } catch (MustException e){
            errorMsg.append(e.getMessage());
            e.printStackTrace();
        }catch (MalformedInputException e){
            errorMsg.append(String.format("文件不符合CWR文件规则，请确认后重新上传---%s",e.getClass().getSimpleName() + e.getMessage())+"！");
            e.printStackTrace();
        }catch (Exception e) {
            errorMsg.append(e.getMessage());
            e.printStackTrace();
        }
        return errorMsg.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertDb(Long fId, List<CwrRecord> records) {
        TransmissionHeader hdr = null;
        Work work = null;
        RecordingDetail rec = null;
        AlternateTitle alt = null;
        PerformingArtist per = null;
        Publisher spu = null;
        PublisherTerritoryOfControl spt = null;
        Writer swr = null;
        WriterTerritoryOfControl swt = null;
        PublisherForWriter pwr = null;

        List<CwrWork> works = new ArrayList<>();
        List<CwrWorkTitle> titles = new ArrayList<>();
        List<CwrPublisher> publishers = new ArrayList<>();
        List<CwrPublisherTerritory> publisherTerritories = new ArrayList<>();
        List<CwrWriter> writers = new ArrayList<>();
        List<CwrWriterTerritory> writerTerritories = new ArrayList<>();
        List<CwrArtist> artists = new ArrayList<>();
        List<CwrPublisherWriter> cwrPublisherWriters = new ArrayList<>();

        for (CwrRecord record : records) {
            if (record instanceof TransmissionHeader) {
                hdr = (TransmissionHeader) record;
            } else if (record instanceof Work) {
                work = (Work) record;
            } else if (record instanceof RecordingDetail) {
                rec = (RecordingDetail) record;
                works.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), rec));
            } else if (record instanceof AlternateTitle) {
                alt = (AlternateTitle) record;
                titles.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), alt));
            } else if (record instanceof PerformingArtist) {
                per = (PerformingArtist) record;
                artists.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), per));
            } else if (record instanceof Publisher) {
                spu = (Publisher) record;
                publishers.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), spu));
            } else if (record instanceof PublisherTerritoryOfControl) {
                spt = (PublisherTerritoryOfControl) record;
                publisherTerritories.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), spt));
            } else if (record instanceof Writer) {
                swr = (Writer) record;
                writers.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), swr));
            } else if (record instanceof WriterTerritoryOfControl) {
                swt = (WriterTerritoryOfControl) record;
                writerTerritories.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), swt));
            } else if (record instanceof PublisherForWriter) {
                pwr = (PublisherForWriter) record;
                cwrPublisherWriters.add(BeanToBeanCwr.transform(Objects.requireNonNull(hdr), Objects.requireNonNull(work), pwr));
            }
        }
        if (works.isEmpty()) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(),"按解析规则无可入库作品数据！");
        }
        works.stream().forEach(cwrWork-> cwrWork.setfId(fId));
        titles.stream().forEach(title-> title.setfId(fId));
        publishers.stream().forEach(publisher-> publisher.setfId(fId));
        publisherTerritories.stream().forEach(publisherTerritorie-> publisherTerritorie.setfId(fId));
        writers.stream().forEach(writer-> writer.setfId(fId));
        writerTerritories.stream().forEach(writerTerritorie-> writerTerritorie.setfId(fId));
        artists.stream().forEach(artist-> artist.setfId(fId));
        cwrPublisherWriters.forEach(cwrPublisherWriter-> cwrPublisherWriter.setfId(fId));
        cwrWorkService.addList(works);
        cwrWorkTitleService.addList(titles);
        cwrPublisherService.addList(publishers);
        cwrPublisherTerritoryService.addList(publisherTerritories);
        cwrWriterService.addList(writers);
        cwrWriterTerritoryService.addList(writerTerritories);
        cwrArtistService.addList(artists);
        cwrPublisherWriterService.addList(cwrPublisherWriters);
    }

    /***
    * @Description:  cwr 生成报告 FIXME  --逻辑待梳理
     *
    * @Param: [workIdWorkSocietyCodeList]
    * @return: java.util.List<java.lang.String>
    * @Author: hanDa
    * @Date: 2020/9/24 14:24
     * NWR_Transaction_Record
     * [{Controlled_Publisher_Info}]  SPU ,SPT ,OPT
     * [{Other_Publisher_Information}] NPN,OPT
     * [{Controlled_Writer_Information}]   SWR\NWN\SWT\PWR
     * [{Other_Writer_Information}]     OWR
     * [{ALT_Alternate_Title}]       ALT
     * [NAT_Non-Roman_Alphabet_Title]  NAT
     * [Information_for_Excerpts]     EWT/NET/NOW
     * [Information_for_Versions] [{PER_Performing_Artist}]    PER
     * [{NPR_Performing Artist_in_Non-Roman_Alphabet}]    NPR
     * [REC_Recording_Information]              REC
     * [ORN_Work_Origin]                   ORN
     * [{Instrumentation_Information}]
     * [{Information_for_Components }]
     * [{ARI_Additional_Related_Information}]
     * [{XRF_Work ID Cross Reference }]
     * 文档结构，参考 crw11-1991r2  ,6.1
    */
    public List<String> generate(List<WorkSocietyVo> workIdWorkSocietyCodeList) throws IllegalAccessException {
        int groupId = 1;
        int transactionSequenceNo = 0;

        List<CwrRecord> records = new ArrayList<>();
        TransmissionHeader hdr = BeanToBeanCwr.transformToHdr();
        records.add(hdr);
        GroupHeader grh = BeanToBeanCwr.transformToGrh(groupId);
        records.add(grh);
        for (WorkSocietyVo workIdWorkSocietyCode : workIdWorkSocietyCodeList) {
            int recordSequenceNo = 0;
            //NWR_Transaction_Record ，先临时存着，后面要根据这块调整顺序
            List<CwrTransactionRecord> t_workRecords = new ArrayList<>();

            Long workId = workIdWorkSocietyCode.getWorkId();
            Integer workSocietyCode = workIdWorkSocietyCode.getWorkSocietyCode();
            WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkId(workId, workSocietyCode);
            if (wrkWork == null) {
                throw new CustomException("该作品不存在", HttpStatus.BAD_REQUEST);
            }

            List<WrkWorkTitle> aWorkTitles = wrkWorkTitleService.getWrkWorkTitleByWrkId(workId, workSocietyCode);
            List<WrkWorkTitle> mainWorkTitles = aWorkTitles.stream().filter(title -> title.getSubTitleId() == 0).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(mainWorkTitles)){
                WrkWorkTitle wrkWorkTitle = mainWorkTitles.get(0) ;
                String title = wrkWorkTitle.getTitle() ;
                title = StringUtils.isNotEmpty(title) ? title : wrkWorkTitle.getTitleEn();
                wrkWork.setTitle(title);
                wrkWork.setGenre(wrkWorkTitle.getGenreCode());
            }
            //Work  NWR
            Work work = BeanToBeanCwr.transformToWork(wrkWork, transactionSequenceNo,
                    0);
            records.add(work);

            //SPU
           //每個NWR , 應該要按次序有 SPU, SPT 等( *圖2), 而 FILE 中NWR 後就是 REC 是不對的,
            //每一行會有一個Record Prefix (1-19) 數字, NWR 為 0, 之後遞增, 不可重複 (*圖1)


            boolean hasOtTitle = false;

            //AlternateTitle
            for (WrkWorkTitle title : aWorkTitles) {
                if ("OT".equals(title.getTitleType())) {
                    hasOtTitle = true;
                }

                //非roman字符的标题要存入NonRomanAlphabetTitle 节点中
                if (StringUtils.isNotBlank(title.getLanguageCode())
                        && !StringUtils.equalsAnyIgnoreCase(title.getLanguageCode(),"ENG") && StringUtils.isNotBlank(title.getTitle())){
                    //NAT

                    NonRomanAlphabetTitle nonRomanAlphabetTitle =BeanToBeanCwr.transformToNonRomanAlphabetTitle(title,transactionSequenceNo,recordSequenceNo);
                    t_workRecords.add(nonRomanAlphabetTitle);
                }else{
                    //ALT
                    AlternateTitle alternateTitle = BeanToBeanCwr.transformToAlternateTitle(title, transactionSequenceNo,
                            recordSequenceNo);
                    t_workRecords.add(alternateTitle);
                }


                //recordSequenceNo++;
            }
            if (!hasOtTitle) {
                throw new CustomException("该作品原标题不存在", HttpStatus.BAD_REQUEST);
            }
            //PerformingArtist
            List<WrkArtist> waList =  wrkArtistService.getWorkArtist(workId,
                    workSocietyCode);
            if (CollectionUtils.isNotEmpty(waList)){
                for (WrkArtist wrkArtist : waList){
                    if(StringUtils.isBlank(wrkArtist.getName()) ){
                        continue;
                    }
                    //TODO  判断是否是全英文
                    boolean isAscii =StringUtils.isNotBlank(wrkArtist.getName())  ? wrkArtist.getName().matches(ASSCII_REGX) : false;
                    if(isAscii){

                        //PER
                        PerformingArtist performingArtist = BeanToBeanCwr.transformToPerformingArtist(wrkArtist, transactionSequenceNo,
                                recordSequenceNo);
                        t_workRecords.add(performingArtist);
                    }else{
                        //NPR
                        NonRomanAlphabetPerformanceData nonRomanAlphabetPerformanceData =
                                BeanToBeanCwr.transformToNonRomanAlphabetPerformanceData(wrkArtist,transactionSequenceNo,recordSequenceNo);
                        t_workRecords.add(nonRomanAlphabetPerformanceData);
                    }


                }
            }
            //E:Publisher; 非E:Writer ;从ipshare中导出
            List<WrkWorkIpShare> wrkWorkIpShareList = wrkWorkIpShareService.getWrkWorkIpShareByWrkId(workId,
                    workSocietyCode);
            if (CollectionUtils.isNotEmpty(wrkWorkIpShareList)){
                List<WrkWorkIpShare> publisherIpshareList = wrkWorkIpShareList.stream().filter(wrkWorkIpShare -> Constants.CWR_PUBLISHER_TYPE.contains(wrkWorkIpShare.getWorkIpRole())).collect(Collectors.toList());
                List<WrkWorkIpShare> writerIpshareList = wrkWorkIpShareList.stream().filter(wrkWorkIpShare -> Constants.WORK_IP_ROLE_ORIGNIAL.contains(wrkWorkIpShare.getWorkIpRole())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(publisherIpshareList)){
                    Map<String,List<WrkWorkIpShare>> publisherIpShareMap = publisherIpshareList.stream().
                            filter(wrkWorkIpShare -> StringUtils.isNotBlank(wrkWorkIpShare.getIpNameNo())).
                            collect(Collectors.groupingBy(WrkWorkIpShare::getIpNameNo));
                    for (Map.Entry<String,List<WrkWorkIpShare>> publisherIpshare : publisherIpShareMap.entrySet()){
                        String ipNameNo = publisherIpshare.getKey();
                        List<WrkWorkIpShare> wrkWorkIpShares = publisherIpshare.getValue();
                        MbrIpName mbrIpName = null;
                        if (StringUtils.isNotBlank(ipNameNo )){
                            mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
                        }

                        WrkWorkIpShare wrkWorkIpShare = new WrkWorkIpShare();
                        wrkWorkIpShare.setRightType("ZYN");
                        wrkWorkIpShare.setIpNameNo(ipNameNo);
                        wrkWorkIpShare.setIpBaseNo(publisherIpshare.getValue().get(0).getIpBaseNo());
                        wrkWorkIpShare.setWorkSocietyCode(workSocietyCode);
                        wrkWorkIpShare.setWorkIpRole(publisherIpshare.getValue().get(0).getWorkIpRole());
                        WrkWorkIpShare wrkWorkIpShare1 = wrkWorkIpShareService.getWrkWorkIpShareSocietyCode(wrkWorkIpShare);
                        //NPN SPU
                        boolean isAscii =StringUtils.isNotBlank(mbrIpName.getName())  ? mbrIpName.getName().matches(ASSCII_REGX) : false;
                        Publisher publisher = BeanToBeanCwr.transformToPublisher(mbrIpName, wrkWorkIpShare1,wrkWorkIpShares, transactionSequenceNo,
                                recordSequenceNo,isAscii);
                        t_workRecords.add(publisher);
                    }
                }
                if (CollectionUtils.isNotEmpty(writerIpshareList)){
                    Map<String,List<WrkWorkIpShare>> writerIpShareMap = writerIpshareList.stream().
                            filter(wrkWorkIpShare -> StringUtils.isNotBlank(wrkWorkIpShare.getIpNameNo()))
                            .collect(Collectors.groupingBy(WrkWorkIpShare::getIpNameNo));

                    for (Map.Entry<String,List<WrkWorkIpShare>> writerIpshare : writerIpShareMap.entrySet()){
                        String ipNameNo = writerIpshare.getKey();
                        List<WrkWorkIpShare> wrkWorkIpShares = writerIpshare.getValue();
                        MbrIpName mbrIpName = null;
                        if (StringUtils.isNotBlank(ipNameNo )){
                            mbrIpName = mbrIpNameService.getIpNameByIpNameNo(ipNameNo);
                        }

                        WrkWorkIpShare wrkWorkIpShare = new WrkWorkIpShare();
                        wrkWorkIpShare.setRightType("ZYN");
                        wrkWorkIpShare.setIpNameNo(ipNameNo);
                        wrkWorkIpShare.setIpBaseNo(writerIpshare.getValue().get(0).getIpBaseNo());
                        WrkWorkIpShare wrkWorkIpShare1 = wrkWorkIpShareService.getWrkWorkIpShareSocietyCode(wrkWorkIpShare);
                        Writer writer = BeanToBeanCwr.transformToWriter(mbrIpName, wrkWorkIpShare1,wrkWorkIpShares, transactionSequenceNo,
                                recordSequenceNo);
                        t_workRecords.add(writer);
                        Set<String> ipBaseNoSet = new HashSet<>() ;
                        String ipBaseNo = writerIpshare.getValue().get(0).getIpBaseNo();
                        if(StringUtils.isNotEmpty(ipBaseNo)){
                            ipBaseNoSet.add(ipBaseNo) ;
                        }else {
                            List<String> ipBaseNoList =  mbrIpNameService.selectIpNoByNameNo(ipNameNo) ;
                            if(CollectionUtils.isNotEmpty(ipBaseNoList)){
                                ipBaseNoSet.addAll(ipBaseNoList) ;
                            }
                        }
                        List<MbrIpAgreementTerritory> territorys =  mbrIpAgreementTerritoryService.selectMapByIpBaseNo(ipBaseNoSet.stream().collect(Collectors.toList()));
                        MbrIpAgreementTerritory at = CollectionUtils.isNotEmpty(territorys) ? territorys.get(0) : null ;
                        //SWT
                        WriterTerritoryOfControl swt =  BeanToBeanCwr.transformToSwt(wrkWorkIpShareList,mbrIpName,at,transactionSequenceNo,recordSequenceNo) ;
                        t_workRecords.add(swt);
                    }
                }
            }
            //RecordingDetail  REC
            RecordingDetail recordingDetail = BeanToBeanCwr.transformToRecordingDetail(wrkWork, transactionSequenceNo,
                    recordSequenceNo);
            t_workRecords.add(recordingDetail);
            //调整节点写入顺序，重新排序
            List<CwrRecord> cwrRecords = workRecordSorting(t_workRecords);


            transactionSequenceNo++;
            recordSequenceNo = 0;
            records.addAll(cwrRecords);
        }



        GroupTrailer grt = BeanToBeanCwr.transformToGrt(groupId, records.size(), transactionSequenceNo);
        records.add(grt);
        TransmissionTrailer trl = BeanToBeanCwr.transformToTrl(1, records.size() + 1, transactionSequenceNo);
        records.add(trl);
        return CwrFileEncoder.encode(records);
    }

    /**
     * 作品下的节点排序，文档要求按节点顺序
     * NWR_Transaction_Record
     * [{Controlled_Publisher_Info}]  SPU ,SPT ,OPT
     * [{Other_Publisher_Information}] NPN,OPT
     * [{Controlled_Writer_Information}]   SWR\NWN\SWT\PWR
     * [{Other_Writer_Information}]     OWR
     * [{ALT_Alternate_Title}]       ALT
     * [NAT_Non-Roman_Alphabet_Title]  NAT
     * [Information_for_Excerpts]     EWT/NET/NOW
     * [Information_for_Versions] [{PER_Performing_Artist}]    PER
     * [{NPR_Performing Artist_in_Non-Roman_Alphabet}]    NPR
     * [REC_Recording_Information]              REC
     * [ORN_Work_Origin]                   ORN
     * [{Instrumentation_Information}] INS,IND
     * [{Information_for_Components }]
     * [{ARI_Additional_Related_Information}] ARI
     * [{XRF_Work ID Cross Reference }]  XRF
     * @param t_workRecords
     * @return
     */
    private List<CwrRecord> workRecordSorting(List<CwrTransactionRecord> t_workRecords) {

        List<CwrRecord> newList =new ArrayList<>();

        Map<String, List<CwrTransactionRecord>> collect = t_workRecords.stream()
                .filter(item->item.getRecordType() !=null).collect(Collectors.groupingBy(CwrRecord::getRecordType));
        //每一行會有一個Record Prefix (1-19) 數字, NWR 為 0, 之後遞增, 不可重複 (*圖1)
        int index = 1;
        //作品下的节点排序，文档要求按节点顺序 ,所以按顺序先遍历，不存在的节点放到最后
        String[] recordType=new String[]{
            "SPU","SPT","OPT","NPN","OPT","SWR","NWN","SWT","PWR","OWR"
                ,"ALT","NAT","EWT","NET","NOW","PER","NPR","REC","ORN","INS","IND","ARI"
                ,"XRF"

        };
        for (String type : recordType) {
            if (collect.containsKey(type)){
                List<CwrTransactionRecord> cwrRecords = collect.get(type);
                for (CwrTransactionRecord cwrRecord : cwrRecords) {

                    //重新设置序号
                    cwrRecord.setRecordSequenceNo(index);
                    newList.add(cwrRecord);
                    index++;
                }

                collect.remove(type);
            }
        }

        //未处理到的追加到最后
        for (Map.Entry<String, List<CwrTransactionRecord>> stringListEntry : collect.entrySet()) {
            List<CwrTransactionRecord> value = stringListEntry.getValue();
            for (CwrTransactionRecord cwrTransactionRecord : value) {
                cwrTransactionRecord.setRecordSequenceNo(index);
                newList.add(cwrTransactionRecord);
                index++;
            }
        }




        return newList;
    }
}
