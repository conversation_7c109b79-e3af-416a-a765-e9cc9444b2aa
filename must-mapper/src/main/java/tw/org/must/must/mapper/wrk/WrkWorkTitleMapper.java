package tw.org.must.must.mapper.wrk;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.wrk.WrkWorkTitle;
import tw.org.must.must.model.wrk.vo.WrkWorkTitleVO;

import java.util.Date;
import java.util.List;


@Repository
public interface WrkWorkTitleMapper extends BaseMapper<WrkWorkTitle> {

    Long saveWrkWorkTitleReceiveId(WrkWorkTitle wwt);

    List<WrkWorkTitleVO> getSynWrkWorkTitleByTime(Long id);

    List<WrkWorkTitleVO> getWrkWorkTitleVoByWorkIdAndCode(Long workId, Integer workSocietyCode);
    List<WrkWorkTitleVO> getWrkWorkTitleVoByGreaterWorkId(@Param("id")Long id);


    List<WrkWorkTitleVO> getSynWrkWorkTitleGtId(@Param("id")Long id);

    List<WrkWorkTitleVO> getWrkWorkTitleByGtAmendTime(@Param("date") Date date);

    void saveWrkWorkTitleEs(List<Integer> list);

    List<WrkWorkTitleVO> selectForMissData(@Param("offset") int offset,@Param("pagesize") int pagesize);

    @Select("select id,work_id,work_society_code, title,title_en,sub_title_id from wrk_work_title where id > #{start} order by id asc limit #{size} ")
    List<WrkWorkTitle> getTitleForMatch(long start, int size);
}