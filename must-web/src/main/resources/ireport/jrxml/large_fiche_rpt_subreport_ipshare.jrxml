<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_fiche_rpt_subreport1" language="groovy" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="ef0c9446-12e2-4311-bca5-7cf34e5ed7ca">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="ipRole" class="java.lang.String"/>
	<field name="ipName" class="java.lang.String"/>
	<field name="ipNameNo" class="java.lang.String"/>
	<field name="soc1" class="java.lang.String"/>
	<field name="part1" class="java.math.BigDecimal"/>
	<field name="soc2" class="java.lang.String"/>
	<field name="part2" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="9" y="1" width="35" height="10" uuid="0c79c4e9-0c63-49fa-811d-ae4e954c22a0"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipRole}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="50" y="1" width="102" height="10" uuid="8043a151-c462-419d-aa87-75d3783a5481"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="277" y="1" width="35" height="10" uuid="49a8d4fa-b9fc-45e2-afa5-3352f3b5de8f"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{part1}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="322" y="1" width="40" height="10" uuid="c9f721b4-ae76-4587-8f17-8dccbb78356d"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soc2}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="368" y="1" width="42" height="10" uuid="686a036c-9b28-47be-816b-eaaee3e366d3"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{part2}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="167" y="1" width="57" height="10" isRemoveLineWhenBlank="true" uuid="80a3a1a2-f809-4583-bf67-e64f83a1b7a5"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipNameNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="232" y="1" width="39" height="10" uuid="01e9c4ba-2803-4655-b096-871ad7a37746"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soc1}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
