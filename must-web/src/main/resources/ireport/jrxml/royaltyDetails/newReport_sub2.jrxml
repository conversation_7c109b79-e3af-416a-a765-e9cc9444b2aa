<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 9.0.0.final using JasperReports Library version 6.21.0-4f56c4f36cd19e17675219a9ac4692d5f0f13b06  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PPersonal_sub" pageWidth="940" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d2a213c6-641d-4c51-899c-ec4ad88834aa">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="ipName" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="ipNameNo" class="java.lang.String"/>
	<field name="society" class="java.lang.String"/>
	<field name="tv" class="java.lang.String"/>
	<field name="radio" class="java.lang.String"/>
	<field name="concert" class="java.lang.String"/>
	<field name="karaoke" class="java.lang.String"/>
	<field name="airline" class="java.lang.String"/>
	<field name="others" class="java.lang.String"/>
	<field name="total" class="java.lang.String"/>
	<field name="remitShare" class="java.lang.String"/>
	<field name="originalDistNo" class="java.lang.String"/>
	<field name="reasonForFeeInError" class="java.lang.String"/>
	<field name="returnShare" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="140" height="14" isPrintWhenDetailOverflows="true" uuid="eb8fdfed-a48f-46f8-8644-f194ca83d568">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="285" y="0" width="50" height="14" uuid="990b4112-73d8-42a1-949d-5f8ce04b1e56">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="140" y="0" width="90" height="14" uuid="5f947b0d-0d6e-46b7-86c2-aedf75d75063">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ipNameNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="210" y="0" width="45" height="14" uuid="48d25d26-3f50-46c3-88e7-9c3241e377c5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{society}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="340" y="0" width="74" height="14" uuid="9220963b-951b-4b9e-bc3c-1da30635dcc2">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remitShare}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
