package tw.org.must.must.mapper.list;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataDsp;
import tw.org.must.must.model.list.ListMatchDataDspMatchHistory;

@Repository
public interface ListMatchDataDspMatchHistoryMapper extends BaseMapper<ListMatchDataDspMatchHistory> {

	void insertDuplicate(List<ListMatchDataDspMatchHistory> listMatchDataDspMatchHistoryList);

	List<ListMatchDataDspMatchHistory> getRollbackList();

	void insertBatchHistory0912(List<ListMatchDataDspMatchHistory> listMatchDataDspMatchHistoryList) ;

}