package tw.org.must.must.model.dist.vo;

import lombok.Data;
import tw.org.must.must.model.dist.DistAutopayNetPaySociety;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
/**
 * @Author: hanDa
 * @Date: 2021/3/29 15:54
 * @Version:1.0
 * @Description:
 */
@Data
public class IncomeSocietyNetVo {
    List<DistAutopayNetPaySociety> distAutopayNetPaySocietyList;

    /**1
     * 支付金额
     */
    @Column(name = "`pay_amount`")
    private BigDecimal payAmount;

    /**1
     * 本地收款金额  正数
     */
    @Column(name = "`local_taxable_income`")
    private BigDecimal localTaxableIncome;

    /**1
     * 海外收款金额  正数
     */
    @Column(name = "`overseas_taxable_income`")
    private BigDecimal overseasTaxableIncome;

    /**1
     * 废弃---所得税
     */
    @Column(name = "`withheld_tax`")
    private BigDecimal withheldTax;

    /**1
     * 管理费  负数
     */
    @Column(name = "`commission_amount`")
    private BigDecimal commissionAmount;

    /**1
     * --废弃，扣除费用，deduction
     */
    @Column(name = "`non_taxable_income`")
    private BigDecimal nonTaxableIncome;


    /**1
     * 预付款，正数
     */
    @Column(name = "`advance`")
    private BigDecimal advance;

    private BigDecimal royaltyAmount;

    private BigDecimal deduction;
}