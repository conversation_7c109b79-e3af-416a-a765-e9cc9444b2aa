<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PPersonal_sub" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d2a213c6-641d-4c51-899c-ec4ad88834aa">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="IP" class="java.lang.String"/>
	<field name="status" class="java.lang.String"/>
	<field name="ipNameNo" class="java.lang.String"/>
	<field name="society" class="java.lang.String"/>
	<field name="share" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="21" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="280" height="20" uuid="eb8fdfed-a48f-46f8-8644-f194ca83d568"/>
				<textFieldExpression><![CDATA[$F{IP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="0" width="50" height="20" uuid="990b4112-73d8-42a1-949d-5f8ce04b1e56"/>
				<textFieldExpression><![CDATA[$F{status}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="0" width="120" height="20" uuid="5f947b0d-0d6e-46b7-86c2-aedf75d75063"/>
				<textFieldExpression><![CDATA[$F{ipNameNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="0" width="45" height="20" uuid="48d25d26-3f50-46c3-88e7-9c3241e377c5"/>
				<textFieldExpression><![CDATA[$F{society}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="495" y="0" width="100" height="20" uuid="ae7929a7-80c2-432c-bb7b-790a24630067"/>
				<textFieldExpression><![CDATA[$F{share}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
