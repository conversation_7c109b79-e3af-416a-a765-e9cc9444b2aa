package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListBasicCombinedToDataMapping;
import tw.org.must.must.model.list.ListBasicFileCombinedMapping;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public interface ListBasicFileCombinedMappingMapper extends BaseMapper<ListBasicFileCombinedMapping> {

    BigDecimal selectTotalCountByBaseId(@Param("baseId") Long baseId);

    Integer insertPerformTimeOrUpdateList(@Param("performTimeMap") Map<String, Date> performTimeMap) ;

    List<ListBasicFileCombinedMapping> selectPerformTimeByKeys(@Param("set") Set<String> set);

    Integer insertCombinedToDataMapping(List<ListBasicCombinedToDataMapping> list);

    void truncateTable();
}
