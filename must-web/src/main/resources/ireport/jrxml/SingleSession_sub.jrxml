<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Blank_sub" pageWidth="1000" pageHeight="842" whenNoDataType="BlankPage" columnWidth="960" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="c70e9e46-d52c-403a-ae72-720f250d94dc">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="workId" class="java.lang.String"/>
	<field name="workSocietyCode" class="java.lang.String"/>
	<field name="title" class="java.lang.String"/>
	<field name="performNumber" class="java.lang.String"/>
	<field name="composer" class="java.lang.Object"/>
	<field name="lyricist" class="java.lang.Object"/>
	<field name="line" class="java.lang.String"/>
	<field name="composer_x" class="java.lang.String"/>
	<field name="lyricist_x" class="java.lang.String"/>
	<variable name="sort" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$V{index}.valueOf(1)]]></variableExpression>
		<initialValueExpression><![CDATA[1]]></initialValueExpression>
	</variable>
	<variable name="index" class="java.lang.Integer"/>
	<detail>
		<band height="29" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<elementGroup/>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="-20" y="0" width="100" height="20" uuid="36aafe5a-209e-4610-95ee-4f7ee0aa790e"/>
				<textElement>
					<font fontName="华文宋体" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{workId}+"/"+$F{workSocietyCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="70" y="0" width="100" height="20" uuid="7567092c-bb1d-498c-9068-a797e4b041d9"/>
				<textElement>
					<font fontName="华文宋体" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{sort}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="100" y="0" width="120" height="20" uuid="fb31ec71-ea7c-43b3-9558-564b66730a17"/>
				<textElement>
					<font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{title}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="870" y="0" width="90" height="20" uuid="e2f40286-bd22-4373-899e-a56fc4d6a5fd"/>
				<textFieldExpression><![CDATA[$F{performNumber}]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="true" >
				<reportElement stretchType="RelativeToBandHeight" x="250" y="0" width="140" height="28" isPrintWhenDetailOverflows="true" uuid="1c35d277-b9e9-4881-80aa-e52993283bce"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("lyricist")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"SingleSession_sub_child.jasper"]]>
				</subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true" >
				<reportElement x="220" y="0" width="100"  height="20" uuid="6f05a18a-1c81-4dbf-aeb5-67e338deca01">
					<printWhenExpression><![CDATA[ !$F{lyricist_x}.equals("no")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{lyricist_x} + "  作詞者"]]></textFieldExpression>
			</textField>
		</band>
		<band height="29">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="220" y="1" width="50" height="20" isPrintWhenDetailOverflows="true" uuid="76dd437a-5542-497a-a413-4f57265b9468">
					<printWhenExpression><![CDATA[ !$F{composer_x}.equals("no")]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" size="8" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[ $F{composer_x} + "  作曲者"]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="250" y="1" width="140" height="28" isPrintWhenDetailOverflows="true" uuid="cf26cdff-0a32-4065-a02a-545fc0a3443a">
					<printWhenExpression><![CDATA[!$F{composer_x}.equals("no") ]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("composer")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}+"SingleSession_sub_child2.jasper"]]></subreportExpression>
			</subreport>
		</band>
		<band height="20">
			<staticText>
				<reportElement x="-20" y="1" width="960" height="15" uuid="4292eb55-ef0b-4a04-8ba4-53e831626c2d"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------]]></text>
			</staticText>
		</band>
	</detail>
</jasperReport>
