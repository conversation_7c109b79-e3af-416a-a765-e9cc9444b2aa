package tw.org.must.must.model.dist;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Table;

import tw.org.must.must.common.base.BaseEntity;

@Table(name = "`dist_calc_work`")
public class DistCalcWork extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`work_id`")
    private Long workId;

    /**
     * 
     */
    @Column(name = "`work_society_code`")
    private Integer workSocietyCode;

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`list_file_id`")
    private Long listFileId;

    /**
     * 
     */
    @Column(name = "`list_file_data_id`")
    private Long listFileDataId;

    /**
     * 分配类型P/I/O/M
     */
    @Column(name = "`dist_type`")
    private String distType;

    /**
     * 
     */
    @Column(name = "`list_parent_md5`")
    private String listParentMd5;

    /**
     * 音乐积点
     */
    @Column(name = "`music_point`")
    private BigDecimal musicPoint;

    /**
     * 音乐金额
     */
    @Column(name = "`music_amount`")
    private BigDecimal musicAmount;

    /**
     * tv,radio....
     */
    @Column(name = "`list_file_type`")
    private String listFileType;

    /**
     * 是否SD
     */
    @Column(name = "`sd`")
    private String sd;

    /**
     * 用途，根据该值和时长计算积点
     */
    @Column(name = "`usage`")
    private String usage;

    /**
     * 时长，秒
     */
    @Column(name = "`duration`")
    private Long duration;

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public Long getListFileId() {
        return listFileId;
    }

    public void setListFileId(Long listFileId) {
        this.listFileId = listFileId;
    }

    public Long getListFileDataId() {
        return listFileDataId;
    }

    public void setListFileDataId(Long listFileDataId) {
        this.listFileDataId = listFileDataId;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType;
    }

    public String getListParentMd5() {
        return listParentMd5;
    }

    public void setListParentMd5(String listParentMd5) {
        this.listParentMd5 = listParentMd5;
    }

 

    public BigDecimal getMusicPoint() {
		return musicPoint;
	}

	public void setMusicPoint(BigDecimal musicPoint) {
		this.musicPoint = musicPoint;
	}

	public BigDecimal getMusicAmount() {
		return musicAmount;
	}

	public void setMusicAmount(BigDecimal musicAmount) {
		this.musicAmount = musicAmount;
	}

	public String getListFileType() {
        return listFileType;
    }

    public void setListFileType(String listFileType) {
        this.listFileType = listFileType;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }



}