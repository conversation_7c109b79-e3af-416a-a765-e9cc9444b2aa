package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`dist_mechanical_sc_sales`")
public class DistMechanicalScSales extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`sc_id`")
    private Long scId;

    /**
     * 
     */
    @Column(name = "`period_start`")
    private Date periodStart;

    /**
     * 
     */
    @Column(name = "`period_end`")
    private Date periodEnd;

    /**
     * 销售编号
     */
    @Column(name = "`sales_no`")
    private String salesNo;

    /**
     * 
     */
    @Column(name = "`country`")
    private String country;

    /**
     * 单价
     */
    @Column(name = "`unit_price`")
    private String unitPrice;

    /**
     * 支付单价
     */
    @Column(name = "`unit_sold`")
    private String unitSold;

    /**
     * 税率
     */
    @Column(name = "`roy_rate`")
    private String royRate;

    /**
     * 支付金额
     */
    @Column(name = "`sc_roy`")
    private String scRoy;

    /**
     * 销售作品数
     */
    @Column(name = "`sales_work_count`")
    private Integer salesWorkCount;

    public Long getScId() {
        return scId;
    }

    public void setScId(Long scId) {
        this.scId = scId;
    }

    public Date getPeriodStart() {
        return periodStart;
    }

    public void setPeriodStart(Date periodStart) {
        this.periodStart = periodStart;
    }

    public Date getPeriodEnd() {
        return periodEnd;
    }

    public void setPeriodEnd(Date periodEnd) {
        this.periodEnd = periodEnd;
    }

    public String getSalesNo() {
        return salesNo;
    }

    public void setSalesNo(String salesNo) {
        this.salesNo = salesNo;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getUnitSold() {
        return unitSold;
    }

    public void setUnitSold(String unitSold) {
        this.unitSold = unitSold;
    }

    public String getRoyRate() {
        return royRate;
    }

    public void setRoyRate(String royRate) {
        this.royRate = royRate;
    }

    public String getScRoy() {
        return scRoy;
    }

    public void setScRoy(String scRoy) {
        this.scRoy = scRoy;
    }

    public Integer getSalesWorkCount() {
        return salesWorkCount;
    }

    public void setSalesWorkCount(Integer salesWorkCount) {
        this.salesWorkCount = salesWorkCount;
    }



}