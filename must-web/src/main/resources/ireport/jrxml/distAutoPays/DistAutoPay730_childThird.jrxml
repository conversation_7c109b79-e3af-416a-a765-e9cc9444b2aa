<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="730_child2" pageWidth="660" pageHeight="842" columnWidth="660" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="89738e6a-019f-456b-97a5-68ba0605a72a">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="name" class="java.lang.String"/>
	<field name="number" class="java.lang.String"/>
	<field name="year" class="java.lang.String"/>
	<field name="performRoyalties" class="java.lang.String"/>
	<field name="allocation" class="java.lang.String"/>
	<field name="royaltiesAdj" class="java.lang.String"/>
	<field name="royaltiesAdj2" class="java.lang.String"/>
	<field name="memberRetain" class="java.lang.String"/>
	<field name="commissionRate" class="java.lang.String"/>
	<field name="commissionAmount" class="java.lang.String"/>
	<field name="taxableRate" class="java.lang.String"/>
	<field name="taxableAmount" class="java.lang.String"/>
	<field name="reciprocalRate" class="java.lang.String"/>
	<field name="reciprocalAmount" class="java.lang.String"/>
	<field name="royaltyAmount" class="java.lang.String"/>
	<field name="salesTaxRate" class="java.lang.String"/>
	<field name="salesTaxAmount" class="java.lang.String"/>
	<field name="firstDescribe" class="java.lang.String"/>
	<field name="distributionDescribe" class="java.lang.String"/>
	<field name="memberDescribe" class="java.lang.String"/>
	<field name="royalDescribe" class="java.lang.String"/>
	<field name="additionalCommissionAmount" class="java.lang.String"/>
	<field name="deduction" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="175" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="400" height="20" uuid="40481fb0-2f13-4f9d-b9da-de5b179e51f6"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{firstDescribe}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="20" width="400" height="20" uuid="6a12357b-e8d6-45fa-88ea-eff246ca2951"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[ROYALTIES ADJUSTMENT 權利金調整]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="50" width="320" height="20" uuid="72987ca1-7836-45ac-afa8-9ecdb23e45c5"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[LESS: ADMIN. FEE 扣除管理費]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="70" width="320" height="20" uuid="8517d36c-121f-4119-a41f-fed00dbf4bd4"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[LESS:ADD. ADMIN. FEE 加扣管理費]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="90" width="320" height="20" uuid="72987ca1-7836-45ac-afa8-9ecdb23e45c5"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[LESS: TAX 所得稅 ]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="130" width="400" height="20" uuid="8517d36c-121f-4119-a41f-fed00dbf4bd4"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[所得稅調整]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="150" width="400" height="25" uuid="8517d36c-121f-4119-a41f-fed00dbf4bd4"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[$F{royalDescribe}]]></text>
			</staticText>
			<textField>
				<reportElement x="320" y="90" width="80" height="20" uuid="40481fb0-2f13-4f9d-b9da-de5b179e51f6"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["@"+$F{taxableRate}+"%"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="320" y="50" width="80" height="20" uuid="40481fb0-2f13-4f9d-b9da-de5b179e51f6"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["@"+$F{commissionRate}+"%"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="0" width="120" height="20" uuid="40481fb0-2f13-4f9d-b9da-de5b179e51f6"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{royaltyAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="20" width="120" height="20" uuid="6a12357b-e8d6-45fa-88ea-eff246ca2951"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{royaltiesAdj}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="20" width="120" height="20" uuid="6a12357b-e8d6-45fa-88ea-eff246ca2951"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{performRoyalties} + $F{royaltiesAdj}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="50" width="120" height="20" forecolor="#FF0000" uuid="0c4e14ac-19bb-480a-ae6c-217e95c597ec"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["("+$F{commissionAmount}+")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="90" width="120" height="20" forecolor="#FF0000" uuid="43115bcf-9623-4e0d-a384-97442b1a3bd6"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["("+$F{taxableAmount}+")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="70" width="120" height="20" forecolor="#FF0000" uuid="a369f1ab-cbe0-4044-a3f8-cc0abd1d0112"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{additionalCommissionAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="110" width="120" height="20" uuid="a369f1ab-cbe0-4044-a3f8-cc0abd1d0112"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{commissionAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="130" width="120" height="20" forecolor="#FF0000" uuid="a369f1ab-cbe0-4044-a3f8-cc0abd1d0112"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{deduction}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="520" y="150" width="120" height="25" uuid="a369f1ab-cbe0-4044-a3f8-cc0abd1d0112"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{performRoyalties}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
