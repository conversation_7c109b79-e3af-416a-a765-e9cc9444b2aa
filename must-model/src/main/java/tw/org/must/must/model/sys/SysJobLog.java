package tw.org.must.must.model.sys;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description:
 * @author: handa
 * @time: 2019/12/4 18:47
 */
@Table(name = "sys_job_log")
public class SysJobLog extends BaseEntity {

    @Column(name = "job_id")
    private String jobId;

    @Column(name = "job_name")
    private  String jobName;

    @Column(name = "job_name_en")
    private String jobNameEn;

    @Column(name = "job_time")
    private Date jobTime;

    @Column(name = "job_elapsed_time")
    private Long jobElapsedTime;

    @Column(name = "status")
    private Integer status;

    @Column(name = "log_path")
    private String logPath;

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getJobNameEn() {
        return jobNameEn;
    }

    public void setJobNameEn(String jobNameEn) {
        this.jobNameEn = jobNameEn;
    }

    public Date getJobTime() {
        return jobTime;
    }

    public void setJobTime(Date jobTime) {
        this.jobTime = jobTime;
    }

    public Long getJobElapsedTime() {
        return jobElapsedTime;
    }

    public void setJobElapsedTime(Long jobElapsedTime) {
        this.jobElapsedTime = jobElapsedTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }
}
