package tw.org.must.must.model.dist;

import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;

import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_param_info`")
public class DistParamInfo extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3989271043096931897L;

	/**
	 * 分配编号
	 */
	@Column(name = "dist_no")
	private String distNo;

	/**
	 * 要分配的数据开始时间
	 */
	@Column(name = "dist_list_start_time")
	private Date distListStartTime;

	/**
	 * 要分配的数据的结束时间
	 */
	@Column(name = "dist_list_end_time")
	private Date distListEndTime;

	/**
	 * 基准货币
	 */
	@Column(name = "base_currency")
	private String baseCurrency;

	@Transient
	private String baseCurrencyName;

	/**
	 * 基准汇率
	 */
	@Column(name = "base_exchange_rate")
	private BigDecimal baseExchangeRate;

	/**
	 * 备注
	 */
	@Column(name = "remark")
	private String remark;

	/**
	 * 年度跟后面的local_limit相关
	 */
	@Column(name = "tax_year")
	private String taxYear;

	/**
	 * 基准税的阈值
	 */
	@Column(name = "local_limit")
	private BigDecimal localLimit;

	/**
	 * 基准税之下的比例
	 */
	@Column(name = "below_limit_rate")
	private BigDecimal belowLimitRate;

	/**
	 * 基准税之上的比例
	 */
	@Column(name = "above_limit_rate")
	private BigDecimal aboveLimitRate;

	/**
	 * 管理费 百分比形式收取
	 */
	@Column(name = "commission")
	private BigDecimal commission;

	/**
	 * 调整分配的税率
	 */
	@Column(name = "adj")
	private BigDecimal adj;

	/**
	 * must的会员海外国籍收取的费率
	 */
	@Column(name = "overseas")
	private BigDecimal overseas;

	/**
	 * 针对准会员收取的额外费用，计算的时候与管理费用会相加
	 */
	@Column(name = "extra")
	private BigDecimal extra;

	/**
	 * 姊妹协会税率
	 */
	@Column(name = "society_tax_rate")
	private BigDecimal societyTaxRate;

	@Column(name = "sales_tax_rate")
	private BigDecimal salesTaxRate;

	/**
	 * 是否需要upa的分配
	 */
	@Column(name = "upa_dist")
	private String upaDist;

	/**
	 * 配状态，0，分配参数配置，1分配计算开始，2拉取分配作品数据，3分配计算中，4计算完成，5分配完成，6支付生成，7关闭分配 (因定时任务，没有锁定状态，现在原有状态后加1/2，标识执行中/执行失败)
	 */
	@Column(name = "dist_status")
	private Integer distStatus;

	@Column(name = "create_user_id")
	private Long createUserId;

	@Column(name = "update_user_id")
	private Long updateUserId;

	@Column(name = "update_user_name")
	private String updateUserName;

	@Column(name = "create_user_name")
	private String createUserName;

	@Column(name = "dist_date")
	private Date distDate;

	@Column(name = "out_file")
	private String outFile;

	@Column(name = "last_run_start")
	private Date lastRunStart;

	@Column(name = "last_run_end")
	private Date lastRunEnd;

	@Transient
	private Integer scale ;

	public String getOutFile() {
		return outFile;
	}

	public void setOutFile(String outFile) {
		this.outFile = outFile;
	}

	public String getBaseCurrencyName() {
		return baseCurrencyName;
	}

	public void setBaseCurrencyName(String baseCurrencyName) {
		this.baseCurrencyName = baseCurrencyName;
	}

	public Date getDistDate() {
		return distDate;
	}

	public void setDistDate(Date distDate) {
		this.distDate = distDate;
	}

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public Date getDistListStartTime() {
		return distListStartTime;
	}

	public void setDistListStartTime(Date distListStartTime) {
		this.distListStartTime = distListStartTime;
	}

	public Date getDistListEndTime() {
		return distListEndTime;
	}

	public void setDistListEndTime(Date distListEndTime) {
		this.distListEndTime = distListEndTime;
	}

	public String getBaseCurrency() {
		return baseCurrency;
	}

	public void setBaseCurrency(String baseCurrency) {
		this.baseCurrency = baseCurrency;
	}

	public BigDecimal getBaseExchangeRate() {
		return baseExchangeRate;
	}

	public void setBaseExchangeRate(BigDecimal baseExchangeRate) {
		this.baseExchangeRate = baseExchangeRate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getTaxYear() {
		return taxYear;
	}

	public void setTaxYear(String taxYear) {
		this.taxYear = taxYear;
	}

	public BigDecimal getLocalLimit() {
		return localLimit;
	}

	public void setLocalLimit(BigDecimal localLimit) {
		this.localLimit = localLimit;
	}

	public BigDecimal getBelowLimitRate() {
		return belowLimitRate;
	}

	public void setBelowLimitRate(BigDecimal belowLimitRate) {
		this.belowLimitRate = belowLimitRate;
	}

	public BigDecimal getAboveLimitRate() {
		return aboveLimitRate;
	}

	public void setAboveLimitRate(BigDecimal aboveLimitRate) {
		this.aboveLimitRate = aboveLimitRate;
	}

	public BigDecimal getCommission() {
		return commission;
	}

	public void setCommission(BigDecimal commission) {
		this.commission = commission;
	}

	public BigDecimal getAdj() {
		return adj;
	}

	public void setAdj(BigDecimal adj) {
		this.adj = adj;
	}

	public BigDecimal getOverseas() {
		return overseas;
	}

	public void setOverseas(BigDecimal overseas) {
		this.overseas = overseas;
	}

	public BigDecimal getExtra() {
		return extra;
	}

	public void setExtra(BigDecimal extra) {
		this.extra = extra;
	}

	public BigDecimal getSocietyTaxRate() {
		return societyTaxRate;
	}

	public void setSocietyTaxRate(BigDecimal societyTaxRate) {
		this.societyTaxRate = societyTaxRate;
	}

	public String getUpaDist() {
		return upaDist;
	}

	public void setUpaDist(String upaDist) {
		this.upaDist = upaDist;
	}

	public Integer getDistStatus() {
		return distStatus;
	}

	public void setDistStatus(Integer distStatus) {
		this.distStatus = distStatus;
	}

	public BigDecimal getSalesTaxRate() {
		return salesTaxRate;
	}

	public void setSalesTaxRate(BigDecimal salesTaxRate) {
		this.salesTaxRate = salesTaxRate;
	}

	public Long getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	public Long getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public Date getLastRunStart() {
		return lastRunStart;
	}

	public void setLastRunStart(Date lastRunStart) {
		this.lastRunStart = lastRunStart;
	}

	public Date getLastRunEnd() {
		return lastRunEnd;
	}

	public void setLastRunEnd(Date lastRunEnd) {
		this.lastRunEnd = lastRunEnd;
	}

	public int getScale(){
		if(scale != null)return scale;
		else if(StringUtils.equals(this.baseCurrency,"TWD")) scale = 0;
		else if(StringUtils.equals(this.baseCurrency,"USD")) scale = 2;
		else scale = 6;
		return scale;
	}
}