package tw.org.must.must.mapper.wrk;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.wrk.WrkWorkIswcStatus;

import java.util.List;

@Repository
public interface WrkWorkIswcStatusMapper extends BaseMapper<WrkWorkIswcStatus> {

    Integer insertOrUpDate(List<WrkWorkIswcStatus> list);
    Integer insertOrUpDateForResult(List<WrkWorkIswcStatus> list);
    Integer insertOrUpDateForUpdateResult(List<WrkWorkIswcStatus> list);


}