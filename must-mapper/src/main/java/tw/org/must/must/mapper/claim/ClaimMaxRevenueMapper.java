package tw.org.must.must.mapper.claim;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimMaxRevenue;

import java.util.List;

@Repository
public interface ClaimMaxRevenueMapper extends BaseMapper<ClaimMaxRevenue> {
    //获取全部数据
    List<ClaimMaxRevenue> getClaimMaxRevenueList();


    //
    int addClaimMaxRevenue(ClaimMaxRevenue claimMaxRevenue);
}