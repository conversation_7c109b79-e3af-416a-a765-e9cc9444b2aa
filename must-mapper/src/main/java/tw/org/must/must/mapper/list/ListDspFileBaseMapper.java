package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListDspFileBase;

import java.util.Date;
import java.util.List;

@Repository
public interface ListDspFileBaseMapper extends BaseMapper<ListDspFileBase> {

//    @Select("SELECT fb.* FROM list_dsp_file_base fb INNER JOIN claim_minima_info m ON fb.claim_minima_info_id=m.id WHERE m.claim_set_id=#{claimSetId}")
    List<Long> selectDspFileBaseIds(@Param("claimSetId") Long claimSetId,@Param("listFileStartTime") Date listFileStartTime,
                                    @Param("listFileEndTime") Date listFileEndTime);

}