<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="730_sub" pageWidth="800" pageHeight="600" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="6a5a2365-15da-4757-a88a-845c985b874a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="societyName" class="java.lang.String"/>
	<field name="distNo" class="java.lang.String"/>
	<field name="payAbleTo" class="java.lang.String"/>
	<field name="royalties" class="java.lang.String"/>
	<field name="payAbleAmount" class="java.lang.String"/>
	<field name="taxRate" class="java.lang.String"/>
	<field name="admissionRate" class="java.lang.String"/>
	<field name="reciprocalRate" class="java.lang.String"/>
	<field name="taxAmount" class="java.lang.String"/>
	<field name="admissionAmount" class="java.lang.String"/>
	<field name="reciprocalAmount" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="88" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="100" height="20" uuid="f085cff4-d0b6-4591-a7d3-4004c3ba6857"/>
				<textFieldExpression><![CDATA[$F{societyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="105" y="0" width="90" height="20" uuid="c9be0270-6d3d-41b5-b520-57cc69c8ef73"/>
				<textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="200" y="0" width="100" height="20" uuid="51f2b72e-d1f4-4256-8d49-063e6bae9e02"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{payAbleTo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="0" width="90" height="20" uuid="b35ce080-1999-496a-815d-ae286bc48dab"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $F{royalties}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="0" width="80" height="20" uuid="60450310-ddfa-4456-ba8d-204848f93cd0"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["$" + $F{payAbleAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="160" y="20" width="140" height="20" uuid="cc883053-4604-4d72-939d-c70982df1b04"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["TAX RATE @ " + $F{taxRate} + "%:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="160" y="40" width="140" height="20" uuid="7d9406bc-fa68-4562-9348-cd7dcf9724c2"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["ADMINISTRATION FEE @ " + $F{admissionRate} + "%:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="160" y="60" width="140" height="20" uuid="c606c931-cb7c-4802-9538-f56030a3fed3"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["RECIPROCAL FEE @ " + $F{reciprocalRate} + "%:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="20" width="90" height="20" uuid="9da564b9-1884-408c-8c11-974ab645d66c"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" + $F{taxAmount} + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="40" width="90" height="20" uuid="34d64d91-54b6-42d5-8069-2a00fd076311"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" +$F{admissionAmount}+ ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="305" y="60" width="90" height="20" uuid="542fe37c-6957-46ef-80e1-74384a2a37e8"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["($" +$F{reciprocalAmount}+ ")"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
