package tw.org.must.must.model.dist.vo.v750;

import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @Author: hanDa
 * @Date: 2021/5/20 16:10
 * @Version:1.0
 * @Description:
 */
@Data
public class Detail750 {
    private String paNameNo;
    private String paName;
    private String bankName;
    private String bankCode;
    private Long branchNo;
    private String accountNumber;
    private String payAmount;
    private String payMethod;
    private String payDescription;
    private List<AccountDetail750> detailList;

    public String getPayAmount() {
        return "$".concat(payAmount);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Detail750 detail750 = (Detail750) o;
        return Objects.equals(paNameNo, detail750.paNameNo) && Objects.equals(paName, detail750.paName) && Objects.equals(bankName, detail750.bankName) && Objects.equals(bankCode, detail750.bankCode) && Objects.equals(branchNo, detail750.branchNo) && Objects.equals(accountNumber, detail750.accountNumber) && Objects.equals(payAmount, detail750.payAmount) && Objects.equals(payMethod, detail750.payMethod) && Objects.equals(payDescription, detail750.payDescription) && Objects.equals(detailList, detail750.detailList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(paNameNo, paName, bankName, bankCode, branchNo, accountNumber);
    }
}