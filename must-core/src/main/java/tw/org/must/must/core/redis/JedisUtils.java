package tw.org.must.must.core.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

@Component
public class JedisUtils {

    public static RedisTemplate<String, Object> redisTemplate;

    /**
     * 对redis字符串类型数据操作
     */
    public static ValueOperations<String, Object> valOpsStr;

    /**
     * 对无序集合类型的数据操作
     */
    public static SetOperations<String, Object> valOpsSet;

    /**
     * 对有序集合类型的数据操作
     */
    public static ZSetOperations<String, Object> valOpsZSet;

    /**
     * 对链表类型的数据操作
     */
    public static ListOperations<String, Object> valOpsList;

    /**
     * 对hash类型的数据操作
     */
    public static HashOperations<String, String, Object> valOpsHash;


    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        JedisUtils.redisTemplate = redisTemplate;
    }

    @Autowired
    public void setValOpsStr(ValueOperations<String, Object> valOpsStr) {
        JedisUtils.valOpsStr = valOpsStr;
    }

    @Autowired
    public void setValOpsSet(SetOperations<String, Object> valOpsSet) {
        JedisUtils.valOpsSet = valOpsSet;
    }

    @Autowired
    public void setValOpsZSet(ZSetOperations<String, Object> valOpsZSet) {
        JedisUtils.valOpsZSet = valOpsZSet;
    }

    @Autowired
    public void setValOpsList(ListOperations<String, Object> valOpsList) {
        JedisUtils.valOpsList = valOpsList;
    }

    @Autowired
    public void setValOpsHash(HashOperations<String, String, Object> valOpsHash) {
        JedisUtils.valOpsHash = valOpsHash;
    }


    public static RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    public static ValueOperations<String, Object> getValOpsStr() {
        return valOpsStr;
    }

    public static SetOperations<String, Object> getValOpsSet() {
        return valOpsSet;
    }

    public static ZSetOperations<String, Object> getValOpsZSet() {
        return valOpsZSet;
    }

    public static ListOperations<String, Object> getValOpsList() {
        return valOpsList;
    }

    public static HashOperations<String, String, Object> getValOpsHash() {
        return valOpsHash;
    }
}
