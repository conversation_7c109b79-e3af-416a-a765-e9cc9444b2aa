package tw.org.must.must.mapper.list;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataOverseasUniq;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseas;

import java.util.List;
import java.util.Set;

@Repository
public interface ListMatchDataOverseasUniqMapper extends BaseMapper<ListMatchDataOverseasUniq> {
    List<ListMatchDataOverseasUniq> listRemittedWork(@Param("workUniqueKey") String workUniqueKey);
    Set<String> existWorkUniqueKeysAfterDel(@Param("relationWorkUniqueKeySet") Set<String> relationWorkUniqueKeySet);

    List<ListMatchDataOverseasUniq> getListMatchDataOverseasUniqCheckList(@Param("dataUniqueKey") String dataUniqueKey,
                                                                          @Param("batch") Integer batch,
                                                                          @Param("fids") List<Long> fids,
                                                                          @Param("remitSociety") Integer remitSociety,
                                                                          @Param("originalTitle") String originalTitle,
                                                                          @Param("authorComposer") String authorComposer,
                                                                          @Param("matchWorkTitle") String matchWorkTitle,
                                                                          @Param("matchWorkId") Long matchWorkId,
                                                                          @Param("matchWorkSoc") Integer matchWorkSoc,
                                                                          @Param("status") Integer status);

    List<ListMatchDataOverseasUniq> getListMatchDataOverseasUniqIpCheckList(@Param("matchWorkId") Long matchWorkId,
                                                                            @Param("matchWorkSoc") Integer matchWorkSoc,
                                                                            @Param("matchWorkTitle") String matchWorkTitle,
                                                                            @Param("distributedIp") String distributedIp,
                                                                            @Param("distributedIpName") String distributedIpName,
                                                                            @Param("matchIpSoc") Integer matchIpSoc,
                                                                            @Param("remitSoc") Integer remitSoc,
                                                                            @Param("remitIpName") String remitIpName,
                                                                            @Param("remitWorkTitle") String remitWorkTitle,
                                                                            @Param("dataUniqueKey") String dataUniqueKey,
                                                                            @Param("batch") Long batch,
                                                                            @Param("fids") List<Long> fids,
                                                                            @Param("rejectCode") Integer rejectCode,
                                                                            @Param("status") Integer status);

    @Select("SELECT * FROM list_match_data_overseas_uniq WHERE id > #{id} and status = 0 order by id limit 1000")
    List<ListMatchDataOverseasUniq> getUnmatchedListMatchDataOverseasUniq(Long id);

}