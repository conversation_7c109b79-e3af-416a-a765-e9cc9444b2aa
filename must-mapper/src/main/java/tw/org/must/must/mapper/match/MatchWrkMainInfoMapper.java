package tw.org.must.must.mapper.match;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWrkMainInfo;
import tw.org.must.must.model.match.vo.MatchWrkMainInfoVo;

import java.util.List;

@Repository
public interface MatchWrkMainInfoMapper extends BaseMapper<MatchWrkMainInfo> {

	Integer insertOrUpdateByList(List<MatchWrkMainInfo> matchWrkMainInfoList);

	List<MatchWrkMainInfoVo> getMatchWrkMainInfoList(MatchWrkMainInfoVo matchWrkMainInfoVo);

}