package tw.org.must.must.model.new_list.vo;

import java.util.Date;
import java.util.List;

/**
 * P2类型（新一般清单）自定义抽样日期请求参数VO
 */
//@ApiModel(description = "自定义抽样日期请求参数")
public class List2CustomSampleDatesVo {

    /**
     * 抽样规则基础ID
     */
    //@ApiModelProperty(value = "抽样规则基础ID", required = true, example = "1")
    private Long sampleRuleBaseId;

    /**
     * 自定义抽样日期列表
     */
    //@ApiModelProperty(value = "自定义抽样日期列表", required = true)
    private List<Date> customSampleDates;

    public Long getSampleRuleBaseId() {
        return sampleRuleBaseId;
    }

    public void setSampleRuleBaseId(Long sampleRuleBaseId) {
        this.sampleRuleBaseId = sampleRuleBaseId;
    }

    public List<Date> getCustomSampleDates() {
        return customSampleDates;
    }

    public void setCustomSampleDates(List<Date> customSampleDates) {
        this.customSampleDates = customSampleDates;
    }

    @Override
    public String toString() {
        return "List2CustomSampleDatesVo{" +
                "sampleRuleBaseId=" + sampleRuleBaseId +
                ", customSampleDates=" + customSampleDates +
                '}';
    }
}
