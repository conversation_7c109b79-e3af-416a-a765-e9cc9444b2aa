<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.16.0.final using JasperReports Library version 6.16.0-48579d909b7943b64690c65c71e07e0b80981928  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="batch_total_summary_sub" pageWidth="837" pageHeight="593" orientation="Landscape" columnWidth="837" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fb506072-2bad-45c1-964e-a63c3177c298">
	<property name="com.jaspersoft.studio.unit." value="cm"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/dist/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="producerNo" class="java.lang.String"/>
	<field name="name" class="java.lang.String"/>
	<field name="sub2" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="125" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="12" y="0" width="45" height="11" forecolor="#080008" uuid="864bf22b-90a2-406d-b32d-5c46341f4c7e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="cm"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[PRODUCER:]]></text>
			</staticText>
			<textField textAdjust="StretchHeight">
				<reportElement x="57" y="0" width="30" height="11" uuid="53c03dec-b393-4771-be31-53a7c5637743">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{producerNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="87" y="0" width="200" height="11" uuid="e2ba6e73-e82d-4ba2-9173-d7df048a28b3">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="12" y="24" width="25" height="11" forecolor="#080008" uuid="8fd4fb7b-c165-4d2d-8b6f-8458bc0999ce">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[BATCH]]></text>
			</staticText>
			<staticText>
				<reportElement x="12" y="33" width="25" height="11" forecolor="#080008" uuid="7889eaaa-f3b4-4311-b5e4-05dd655abea3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="40" y="33" width="17" height="11" forecolor="#080008" uuid="c431a498-a60e-4399-9cea-1bc4c2ca8bb6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[CUR]]></text>
			</staticText>
			<staticText>
				<reportElement x="80" y="24" width="56" height="11" forecolor="#080008" uuid="4a8405a0-f340-46bf-8c90-8f82ec1bb89b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[SOCIETY CODE/]]></text>
			</staticText>
			<staticText>
				<reportElement x="80" y="33" width="56" height="11" forecolor="#080008" uuid="01cf5d5b-cd99-431a-a97b-7e1033d05f2d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[PA NAME NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="147" y="24" width="56" height="11" forecolor="#080008" uuid="a6a9004b-8681-4657-80e8-c290807e16eb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[SOCIETY NAME/]]></text>
			</staticText>
			<staticText>
				<reportElement x="147" y="33" width="56" height="11" forecolor="#080008" uuid="e6df6a52-dce6-427b-88c2-cb85ea454732">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[P NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="326" y="24" width="19" height="11" forecolor="#080008" uuid="341d76c9-b7f3-4417-a1f5-2989e41fd3b5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[MBR/]]></text>
			</staticText>
			<staticText>
				<reportElement x="326" y="33" width="19" height="11" forecolor="#080008" uuid="d1977562-958f-410e-9168-23167942839a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[SOC]]></text>
			</staticText>
			<staticText>
				<reportElement x="358" y="24" width="25" height="11" forecolor="#080008" uuid="3ec1942a-de5e-4ced-ba08-b0b0e506f650">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[COMM]]></text>
			</staticText>
			<staticText>
				<reportElement x="358" y="33" width="25" height="11" forecolor="#080008" uuid="713ea394-c56f-4876-ae41-c15560d0e50b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[RATE]]></text>
			</staticText>
			<staticText>
				<reportElement x="389" y="33" width="36" height="11" forecolor="#080008" uuid="3310202e-ccc0-4dca-8a17-6a360aeede47">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[TAX RATE]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="33" width="42" height="11" forecolor="#080008" uuid="94317e00-c748-4627-a76f-dea0377d1294">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[ROYALTIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="500" y="33" width="48" height="11" forecolor="#080008" uuid="3bb871bc-d11d-4a6c-97a2-4e75afc5272e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[COMMISSION]]></text>
			</staticText>
			<staticText>
				<reportElement x="579" y="33" width="36" height="11" forecolor="#080008" uuid="e0763c29-ab3f-4a91-890b-ced325975852">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[TAXABLE]]></text>
			</staticText>
			<staticText>
				<reportElement x="661" y="33" width="17" height="11" forecolor="#080008" uuid="59f5c3e8-fd16-4dc0-b909-7dd17be80b37">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[TAX]]></text>
			</staticText>
			<staticText>
				<reportElement x="704" y="33" width="39" height="11" forecolor="#080008" uuid="ce3722ea-7159-444a-8a0e-4a7586304071">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
					<property name="com.jaspersoft.studio.unit.width" value="cm"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="华文宋体" size="7" isBold="true"/>
				</textElement>
				<text><![CDATA[NET TOTAL]]></text>
			</staticText>
			<line>
				<reportElement x="12" y="44" width="736" height="1" uuid="7a49a473-5184-4584-a8c5-69a54dd01f48"/>
			</line>
			<subreport>
				<reportElement x="0" y="55" width="821" height="65" uuid="37a9681e-a830-43bb-8b3a-929c9b90d815">
					<property name="com.jaspersoft.studio.unit.y" value="cm"/>
					<property name="com.jaspersoft.studio.unit.x" value="cm"/>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("sub2")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "batch_total_summary_sub2.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
