package tw.org.must.must.core.parse;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.enums.DeletedEnum;
import tw.org.must.must.common.enums.IsShowEnum;
import tw.org.must.must.common.util.BeanCopyUtil;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.core.service.claim.ClaimAppleMusicUsageService;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.claim.ClaimSetInfoService;
import tw.org.must.must.core.service.dsp.DspListUniqueKeyHandler;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.claim.ClaimSetInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListFileQueue;

import java.io.*;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * AppleMusic  解析tsv类型文件
 * 任务分发
 * 2019-12-05
 */
@Service
public class ParseAppleMusicService extends DspListUniqueKeyHandler {

    private Logger logger = LoggerFactory.getLogger(ParseAppleMusicService.class);

    private final ListDspFileBaseService listDspFileBaseService;
    private final ClaimAppleMusicUsageService claimAppleMusicUsageService;
    private final ListDspFileDataMappingService listDspFileDataMappingService;


    private Map<String, String> productMap = new HashMap<>();
    private List<ListDspFileDataMapping> lddmProductList = new ArrayList<>();

    @Autowired
    public ParseAppleMusicService(ListDspFileBaseService listDspFileBaseService, ClaimAppleMusicUsageService claimAppleMusicUsageService, ListDspFileDataMappingService listDspFileDataMappingService) {
        this.listDspFileBaseService = listDspFileBaseService;
        this.claimAppleMusicUsageService = claimAppleMusicUsageService;
        this.listDspFileDataMappingService = listDspFileDataMappingService;
    }

    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;


    @Autowired
    private ClaimSetInfoService claimSetInfoService;

    String usageStartDate = null;
    String usageEndDate = null;
    String profileVersion = null;

    private static ListFileQueue listFileQueue;

    private Map<Long, BigDecimal> fileBaseIdTotalClickNumMap;

    private void init() {
        listFileQueue = null;
        usageStartDate = null;
        usageEndDate = null;
        profileVersion = null;
        productMap.clear();
        lddmProductList.clear();
        fileBaseIdTotalClickNumMap = new HashMap<>();
    }

    public void parse(ListFileQueue lfq) throws Exception {
        init();


        usageStartDate = DateUtils.tryFormatDate(lfq.getStartTime(),"yyyy-MM-dd");
        usageEndDate = DateUtils.tryFormatDate(lfq.getEndTime(),"yyyy-MM-dd");
        //从文件名尝试获取开始和结束时间 AppleMusic_Usage_2017Q3_TWN_v1.tsv
        if (usageStartDate ==null || usageEndDate ==null){

            Date[] quarterByFileName = DateUtils.getQuarterByFileName(lfq.getFileName());
            if (quarterByFileName !=null && quarterByFileName.length ==2){
                usageStartDate = DateUtils.tryFormatDate( quarterByFileName[0],"yyyy-MM-dd");
                usageEndDate = DateUtils.tryFormatDate( quarterByFileName[1],"yyyy-MM-dd");
            }

        }
        listFileQueue = lfq;
        parse(lfq.getFilePath());
    }


    /**
     * 单元测试使用
     *
     * @param filepath
     * @throws Exception
     */
    public void parseTest(String filepath) throws Exception {

        listFileQueue = new ListFileQueue();
        listFileQueue.setId(185L);
        FileInputStream fileInputStream = new FileInputStream(new File(filepath));
        InputStreamReader isr = new InputStreamReader(fileInputStream);// InputStreamReader 是字节流通向字符流的桥梁,
        BufferedReader br = new BufferedReader(isr);// 从字符输入流中读取文件中的内容,封装了一个new InputStreamReader的对象
        String line;
        Map<String, String> titleMap = new HashMap<>();
        int i = 0;
        while ((line = br.readLine()) != null) {
            i++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            logger.info("========ParseAppleMusicService处理第{}行, line: {}", i, line);
            this.dealLine(line, titleMap);

            // 每1000行进行处理一次
        }

    }



    /**
     * AppleMusic  解析tsv类型文件
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常
     */
    public void parse(String filepath) throws Exception {
        String fileName = listFileQueue.getFileName();



        FileInputStream fileInputStream = new FileInputStream(new File(filepath));
        InputStreamReader isr = new InputStreamReader(fileInputStream);// InputStreamReader 是字节流通向字符流的桥梁,
        BufferedReader br = new BufferedReader(isr);// 从字符输入流中读取文件中的内容,封装了一个new InputStreamReader的对象
        String line;
        productMap = new HashMap<>();
        lddmProductList = new ArrayList<>();
        Map<String, String> titleMap = new HashMap<>();
        logger.info("文件{}数据处理开始......", fileName);
        int i = 0;
        Map<Long, String> fileBaseTable = new HashMap<>(); // <fileBaseId, tableName> 存放对应的临时表表名
        Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap = new HashMap<>(); // <fileBaseId, <mapping.id, mapping.clickNumber>>
        while ((line = br.readLine()) != null) {
            i++;
            if (StringUtils.isBlank(line)) {
                continue;
            }

            if (i % Constants.BATCH_SIZE_10000 == 0) {
                logger.info("========ParseAppleMusicService处理第{}行, line: {}", i, line);
            }
            this.dealLine(line, titleMap);

            // 每1000行进行处理一次
            if (null != lddmProductList && lddmProductList.size() > Constants.BATCH_SIZE_1000) {
                saveDspFileDataMappingTemp(fileBaseTable, fileBaseIdPlayCountMap);
                lddmProductList.clear();
            }
        }
        if (lddmProductList.size() > 0) {
            saveDspFileDataMappingTemp(fileBaseTable, fileBaseIdPlayCountMap);
            lddmProductList.clear();
        }
        // 数据排序后同步到正式表，删除临时表
        logger.info("zhy_test, 开始排序同步至正式表");
        if (fileBaseTable.size() > 0) {
            syncOfficialTable(fileBaseTable, fileBaseIdPlayCountMap);
        }
        // 更新文件状态
        logger.info("zhy_test, 更新文件狀態");
        List<ListDspFileBase> listDspFileBaseByQueueId = listDspFileBaseService.getListDspFileBaseByQueueId(listFileQueue.getId());
        if (listDspFileBaseByQueueId != null && listDspFileBaseByQueueId.size() > 0) {
            //同步base表中mapping的数量
            listDspFileDataMappingService.updateFileBaseCountByDspFileBaseInfo(listDspFileBaseByQueueId, fileBaseIdTotalClickNumMap);
            listFileQueue.setStatus(2);
        } else {
            listFileQueue.setStatus(3);
            listFileQueue.setDescription("未查詢到對應的文件，文件隊列id為：" + listFileQueue.getId());
        }
        logger.info("文件" + fileName + "数据处理完毕......");
    }

    /**
     * 插入临时表
     *
     * @param fileBaseTable
     * @param fileBaseIdPlayCountMap
     */
    protected void saveDspFileDataMappingTemp(Map<Long, String> fileBaseTable, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) {
        Map<Long, List<ListDspFileDataMapping>> listMap = lddmProductList.stream().collect(Collectors.groupingBy(ListDspFileDataMapping::getFileBaseId));
        if (listMap.isEmpty()) {
            return;
        }

        for (Long fileBaseId : listMap.keySet()) {
            //不同临时表可以多线程执行
            List<ListDspFileDataMapping> listDspFileDataMappings = listMap.get(fileBaseId);
            if (fileBaseTable.containsKey(fileBaseId)) {
                listDspFileDataMappingService.addListTemporary(fileBaseTable.get(fileBaseId), listDspFileDataMappings);
            } else {
                String temporaryTable = listDspFileDataMappingService.createTemporaryTable(fileBaseId);
                fileBaseTable.put(fileBaseId, temporaryTable);
                listDspFileDataMappingService.addListTemporary(temporaryTable, listDspFileDataMappings);
            }
            List<DspDataTemp> transfer = transfer(listDspFileDataMappings);
            List<DspDataTemp> orDefault = fileBaseIdPlayCountMap.getOrDefault(fileBaseId, new ArrayList<>());
            orDefault.addAll(transfer);
            fileBaseIdPlayCountMap.put(fileBaseId, orDefault);
        }
    }

    /**
     * 排序同步至正式表
     *
     * @param fileBaseTable
     * @param fileBaseIdPlayCountMap
     * @throws InterruptedException
     */
    protected void syncOfficialTable(Map<Long, String> fileBaseTable, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(fileBaseTable.size());
        AtomicBoolean isFailed = new AtomicBoolean(false); // 用于判断异步线程中是否执行失败
        String msg = ""; // 用于记录异步线程中执行失败抛出的异常信息
        ReentrantLock lock = new ReentrantLock(true); // 公平锁，同一张表的写锁（排他锁），用来顺序（唯一）插入mapping数据
        long startTime = System.currentTimeMillis();
        for (Long fileBaseId : fileBaseTable.keySet()) {
            List<DspDataTemp> dspDataTemps = fileBaseIdPlayCountMap.get(fileBaseId);
            if (CollectionUtils.isEmpty(dspDataTemps)) {
                continue;
            }
            String tableName = fileBaseTable.get(fileBaseId);
            // 异步方法里不共用事务
            executorService.execute(() -> {
                try {
                    long listSize = 0;
                    long a = System.currentTimeMillis();
                    List<Long> collect = dspDataTemps.stream().sorted(Comparator.comparing(DspDataTemp::getWorkPrice, Comparator.reverseOrder()).thenComparing(DspDataTemp::getClickNumber, Comparator.reverseOrder())).map(DspDataTemp::getId).collect(Collectors.toList());
                    long b = System.currentTimeMillis() - a;
                    logger.info("临时表【{}】，workPrice, clickNumber倒叙排序耗时：【{}】", tableName, b > 1000 ? (b / 1000) + " 秒" : b + " 毫秒");
                    List<List<Long>> partition = Lists.partition(collect, 100000); //控制每次查询和插入量
                    for (List<Long> ids : partition) {
                        logger.info("临时表【{}】，查询id个数：【{}】", tableName, ids.size());
                        List<ListDspFileDataMapping> listDspFileDataMappings = listDspFileDataMappingService.selectTemporaryByIds(ids, tableName);
                        try {
                            lock.lock();
                            logger.debug("临时表【{}】，开始插入正式表, 待插入 size:【{}】", tableName, listDspFileDataMappings.size());
                            listDspFileDataMappingService.addList(listDspFileDataMappings);
                            listSize += listDspFileDataMappings.size();
                            logger.info("临时表【{}】，开始插入正式表, 已插入 size:【{} / {}】", tableName, listSize, collect.size());
                        } catch (Exception e) {
                            logger.error("tzk002, msg: ", e);
                        } finally {
                            lock.unlock();
                        }
                    }
                    // 跟新base的关于mapping的个数
                    ListDspFileBase base = new ListDspFileBase();
                    base.setId(fileBaseId);
                    base.setListFileTotal(listSize);
                    base.init();
                    logger.info("更新base: {}", JSON.toJSONString(base));
                    listDspFileBaseService.updateSelective(base);
                    logger.info("更新base完成: {}", JSON.toJSONString(base));
                } catch (Exception e) {
                    logger.error("排序同步至正式表失败", e);
                    isFailed.set(true);
                }
            });
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {
            if(isFailed.get()) {
                throw new MustException(String.format("临时表排序同步至正式表失败, msg: ", msg));
            }
        }
        for (String tableName : fileBaseTable.values()) {
            // 需要等待异步线程执行完后，才能删除表，否则会出现查询的时候表没了，导致程序卡死
            logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】", tableName);
            listDspFileDataMappingService.dropTemporaryTable(tableName);
            logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】成功！", tableName);
        }
        logger.info("lock end");
        long consuming = System.currentTimeMillis() - startTime;
        logger.info("fid:【{}】, 临时表->正式表完成, time consuming：【{}】", fileBaseTable.keySet(), consuming > 1000 ? (consuming / 1000) + " 秒" : consuming + " 毫秒");
    }

    public static void main(String[] args) throws InterruptedException {
        Date[] quarterByFileName = DateUtils.getQuarterByFileName("AppleMusic_Usage_2017Q1_TWN_v1.tsv");
        String s = DateUtils.formatDate(quarterByFileName[0]);
        String s1 = DateUtils.formatDate(quarterByFileName[1]);
        System.out.println(s);
        System.out.println(s1);
    }

    public static void test() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        AtomicBoolean flag = new AtomicBoolean(false);
        for (int i = 1; i < 10; i++) {
            int finalI = i;
            executorService.execute(() -> {
                try {
                    TimeUnit.SECONDS.sleep(finalI);
                    System.out.println(1000 / finalI);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    flag.set(true);
                    throw new RuntimeException("执行失败，" + e.getMessage());
                } finally {
                }
            });

        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {
            System.out.println("isTerminated...");
            if(flag.get()) {
                System.out.println("检测到有线程执行失败，中断所有线程并返回异常!");
                executorService.shutdownNow();
                throw new RuntimeException("zzzzdfasdf");
            }
            TimeUnit.SECONDS.sleep(1);
        }
        System.out.println("END");
    }

    private void dealLine(String line, Map<String, String> titleMap) {
        if (line.startsWith("#")) {
            dealWithString(line, titleMap);
        } else {
            String[] currentLineSplit = StringUtils.splitPreserveAllTokens(line,"\t");
            String lineName = currentLineSplit[0];
            String lineTitle = titleMap.get(lineName);
            String[] lineTitleSplit = lineTitle.split("\t");
            if (currentLineSplit.length != lineTitleSplit.length) {
                logger.info("===========当前line,与标题长度不一致===========");
                logger.info("line：{}", line);
                logger.info("title: {}", lineTitle);
                return;
            }

            Map<String, String> map = getMapOfSplit(lineTitleSplit, currentLineSplit);

            this.parseData(map);
        }
    }

    private Map<String, String> getMapOfSplit(String[] titleSplit, String[] lineSplit) {
        Map<String, String> map = new HashMap<>();
        titleSplit[0] = titleSplit[0].replace("#", "");
        for (int i = 0; i < titleSplit.length; i++) {
            String value = null;
            // 当标题与数据的长度不一致，忽略缺失数据（多余数据）
            if (i < lineSplit.length) {
                value = lineSplit[i];
            }
            map.put(titleSplit[i], value);
        }
        return map;
    }

    /**
     * 将map的key 封装到list中
     *
     * @param line
     * @param titleMap
     * @return
     */

    private void dealWithString(String line, Map<String, String> titleMap) {
        String[] split = line.split("\t");
        String name = split[0];
        String replaceName = name.replace("#", "");
        if (StringUtils.isNotBlank(replaceName)) {
            titleMap.put(replaceName, line);
        }
    }

    protected void parseData(Map<String, String> map) {

        if (map.containsKey("SY01")) {
            dealTitle(map);
        } else if (map.containsKey("Track ISRC")) {
            dealEachDetailData(map);
        } else if (map.containsKey("UsageStartDate")) {

            //20211215 客户要求，文件要按季度来，但是如果没有初始化时间则从文件内容中获取
//            if (usageStartDate ==null){
                usageStartDate = map.get("UsageStartDate");
//            }

//            if (usageEndDate ==null){
                usageEndDate = map.get("UsageEndDate");
//            }


            profileVersion = map.get("ProfileVersion");
        }

    }

    private void dealEachDetailData(Map<String, String> map) {
        Set<String> keySet = productMap.keySet();
        ListDspFileDataMapping listDspFileDataMapping = createListDspFileDataMapping(map);
        keySet.forEach(productKey -> {
            String product = productKey + " Unit Count";
            // 取出每个产品对应的unit Count
            String countValue = map.get(product);
            // 不为null 新增一条mapping记录; 是null 跳過
//            if (StringUtils.isBlank(countValue) || "0".equals(countValue)) {
//                return;
//            }
            if (StringUtils.isBlank(countValue)) {
                return;
            }
            BigDecimal count = new BigDecimal(countValue);
            //解决apple click number 0数据也入库问题
            if (count.compareTo(BigDecimal.ZERO)==0){
                return;
            }

            Long fileBaseId = Long.valueOf(productMap.get(productKey));
            ListDspFileDataMapping lddm = BeanCopyUtil.copyImplSerializable(listDspFileDataMapping);
            lddm.setFileBaseId(fileBaseId);
            lddm.setProduct(productKey);
            lddm.setUsage(productMap.get(productKey + "UseType"));
            lddm.setClickNumber(count);
            if(listFileQueue.getMatchMark().equals("N")){
                lddm.setStatus(5);
            } else {
				lddm.setStatus(0);
			}
            fileBaseIdTotalClickNumMap.put(fileBaseId, fileBaseIdTotalClickNumMap.getOrDefault(fileBaseId, BigDecimal.ZERO).add(count));
            lddmProductList.add(lddm);
        });
    }

    private ListDspFileDataMapping createListDspFileDataMapping(Map<String, String> map) {
        ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
        listDspFileDataMapping.init();
        JSONObject object = new JSONObject();
        Map<String, String> extMap = new HashMap<>();
        String trackDSPID = map.get("Track DSP ID");
        listDspFileDataMapping.setBusinessId(trackDSPID);
        listDspFileDataMapping.setResourceId(trackDSPID);
        extMap.put("TrackDSPID", trackDSPID);
        String track_isrc = map.get("Track ISRC");
        listDspFileDataMapping.setIsrc(track_isrc);
        String track_title = map.get("Track Title");
        listDspFileDataMapping.setTitle(track_title);
        String track_primary_artist = map.get("Track Primary Artist").replace(",", "；");
        listDspFileDataMapping.setWorkArtist(track_primary_artist);
        String track_duration = map.get("Track Duration");
        listDspFileDataMapping.setDurationStr(track_duration);
        /**
         * 处理成分钟,秒
         */
        Pattern pattern = Pattern.compile("PT(?<H>\\d+)H(?<M>\\d+)M(?<S>\\d+)S");
        Matcher m = pattern.matcher(track_duration);
        if (m.find()) {
            String m1 = m.group("M");
            String s1 = m.group("S");
            listDspFileDataMapping.setDurationS(Integer.valueOf(s1));
            listDspFileDataMapping.setDurationM(Integer.valueOf(m1));
        }
        String composerAuthor = map.get("ComposerAuthor").replace("|", "；");//作曲者
        listDspFileDataMapping.setAuthor(composerAuthor);
        listDspFileDataMapping.setComposer(composerAuthor);

        String arranger = map.get("Arranger");
        extMap.put("Arranger", arranger);

        String publisher = map.get("Publisher");
        listDspFileDataMapping.setPublisher(publisher);

        String content_provider = map.get("Content Provider");
        extMap.put("ContentProvider", content_provider);

        String iswc = map.get("ISWC");
        listDspFileDataMapping.setIswc(iswc);

        String album_dsp_id = map.get("Album DSP ID");
        extMap.put("AlbumDSPID", album_dsp_id);

        String album_upc = map.get("Album UPC");
        extMap.put("AlbumUPC", album_upc);

        String album_title = map.get("Album Title");
        listDspFileDataMapping.setAlbumTitle(album_title);

        String transaction_id = map.get("Transaction ID");
        listDspFileDataMapping.setReleaseId(transaction_id);

        String trbc = map.get("Total Royalty-Bearing Count");
        extMap.put("TotalRoyaltyBearingCount", trbc);
        String trwc = map.get("Total Royalty-Waived Count");
        extMap.put("TotalRoyaltyWaivedCount", trwc);

        String content_type = map.get("Content Type");
        listDspFileDataMapping.setReleaseType(content_type);

        String record_label = map.get("Record Label");
        listDspFileDataMapping.setCompany(record_label);


        String content_provider_track_id = map.get("Content Provider Track ID");
        extMap.put("ContentProviderTrackID", content_provider_track_id);
//        listDspFileDataMapping.setBusinessId(content_provider_track_id);

        String content_provider_album_id = map.get("Content Provider Album ID");
        extMap.put("ContentProviderAlbumID", content_provider_album_id);
//        listDspFileDataMapping.setReleaseId(content_provider_album_id);

        listDspFileDataMapping.setListFileStartTime(dateOfStr(usageStartDate));
        listDspFileDataMapping.setListFileEndTime(dateOfStr(usageEndDate));
        object.put(trackDSPID, extMap);
        listDspFileDataMapping.setExtJson(object.toString());

        setUniqueKey(listDspFileDataMapping);
        return listDspFileDataMapping;
    }

    private void dealTitle(Map<String, String> map) {
        Long listFileQueueId = listFileQueue.getId();
        String ServiceDescription = map.get("ServiceDescription").trim();
        // 根据productKey和queueId去base表查询，如果不存在则新增，存在则不处理
        ListDspFileBase listDspFileBase = listDspFileBaseService.getListDspFileBaseByProductAndQueueId(ServiceDescription, listFileQueueId);
        if (null == listDspFileBase) {
            listDspFileBase = new ListDspFileBase();
            listDspFileBase.init();
            listDspFileBase.setDist(0);
            listDspFileBase.setDistStatus(0);
            listDspFileBase.setFilePath(listFileQueue.getFilePath());
            listDspFileBase.setDspCompany("AppleMusic");
            listDspFileBase.setProductName(ServiceDescription);
            listDspFileBase.setCurrency(map.get("Currency"));
            listDspFileBase.setFileName(listFileQueue.getFileName());
            listDspFileBase.setFilePath(listFileQueue.getFilePath());
            listDspFileBase.setListFileStartTime(dateOfStr(usageStartDate));
            listDspFileBase.setListFileEndTime(dateOfStr(usageEndDate));
            listDspFileBase.setFileStatus(0);
            listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
            String numberOfUsage = map.get("NumberOfUsage");
            if (StringUtils.isNotBlank(numberOfUsage)) {
                listDspFileBase.setListTotalClickCount(new BigDecimal(numberOfUsage));
            }
            String numberOfSubscribers = map.get("NumberOfSubscribers");
            if (StringUtils.isNotBlank(numberOfSubscribers)) {
                listDspFileBase.setListSubscribeCount(new BigDecimal(numberOfSubscribers));
            }
            String netRevenue = map.get("NetRevenue");
            listDspFileBase.setListDistTotalRoy(new BigDecimal(netRevenue));
            listDspFileBase.setListTotalRoy(new BigDecimal(netRevenue));


            listDspFileBase.setFileQuenceId(listFileQueueId);
            listDspFileBase.setCommercialModel(map.get("CommercialModel"));
            String extJson = listFileQueue.getExtJson();
            JSONObject claimSetInfoObject = JSONObject.fromObject(extJson);
            Long claimSetInfoId = claimSetInfoObject.getLong("claimSetInfoId");
            if (null != claimSetInfoId) {
                // 去claimSetInfoId
                ClaimSetInfo claimSetInfo = claimSetInfoService.getById(claimSetInfoId);
                // 去claimMinimaInfo中查询
                List<ClaimMinimaInfo> cmifList = claimMinimaInfoService.getClaimMinimaInfoBySetInfoId(claimSetInfoId);
                if (cmifList.size() > 0) {
                    List<String> productShortNameList = cmifList.stream().map(it -> it.getProductShortName().trim()).collect(Collectors.toList());
                    // 判断数据库中的产品是否存在
                    if (!productShortNameList.contains(ServiceDescription)) {
                        // 不存在 新增ClaimMinimaInfo
                        ClaimMinimaInfo cmi = new ClaimMinimaInfo();
                        cmi.init();

                        cmi.setProductShortName(ServiceDescription);
                        cmi.setProductFullName(ServiceDescription);
                        cmi.setCompnyName(claimSetInfo.getCompany());
                        cmi.setClaimSetId(claimSetInfoId);
                        claimMinimaInfoService.add(cmi);
                    }
                    ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId(ServiceDescription, claimSetInfoId);
                    // 写入fileBase
                    listDspFileBase.setClaimMinimaInfoId(claimMinimaInfo.getId());
                }
            }
            //写入base表
            listDspFileBase.setIsShow(IsShowEnum.show.code());
            listDspFileBase.setDeleted(DeletedEnum.normal.code());
            listDspFileBase.setUseType(map.get("UseType"));
            listDspFileBase.setMatchMark(listFileQueue.getMatchMark());
            listDspFileBaseService.add(listDspFileBase);
        }
        productMap.put(ServiceDescription, listDspFileBase.getId() + "");
        productMap.put(ServiceDescription + "UseType", map.get("UseType"));
    }

    /**
     * 将map的key 封装到list中
     *
     * @param line
     * @param headerMap
     * @return
     */
    private static List<String> dealWithStringOld(String line, Map<String, String> headerMap) {
        String[] split = line.split("\t");
        List<String> listMapKey = new ArrayList<>();
        for (String s : split) {
            listMapKey.add(s);
        }
        return listMapKey;
    }


    /**
     * @param split
     * @param listMapKey 里面存的是 map的key
     * @return
     */
    private static Map<String, String> getMapOfSplitOld(String[] split, List<String> listMapKey) {
        Map<String, String> map = new LinkedHashMap<>();
        for (int i = 0; i < listMapKey.size(); i++) {
            map.put(listMapKey.get(i), split[i]);
        }
        return map;
    }

    private Date dateOfStr(String date) {
        if(date == null){
            return null;
        }

        DateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return format1.parse(date);
        } catch (ParseException e) {
//            e.printStackTrace();
            logger.error("日期转换出错，{}",date);
        }
        return null;
    }

    @Override
    public String getCompanyIdentification() {
        return "apple";
    }
}
