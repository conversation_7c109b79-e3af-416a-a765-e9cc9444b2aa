package tw.org.must.must.model.sys;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`sys_dict_item`")
public class SysDictItem extends BaseEntity {

    /**
     * 字典id
     */
    @Column(name = "`dict_id`")
    private Long dictId;

    /**
     * 字典项文本
     */
    @Column(name = "`item_text`")
    private String itemText;

    /**
     * 字典项值
     */
    @Column(name = "`item_value`")
    private String itemValue;

    /**
     * 描述
     */
    @Column(name = "`description`")
    private String description;

    /**
     * 排序
     */
    @Column(name = "`sort_order`")
    private Integer sortOrder;

    /**
     * 状态（1启用 0不启用）
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * 
     */
    @Column(name = "`create_by`")
    private String createBy;

    /**
     * 
     */
    @Column(name = "`update_by`")
    private String updateBy;

    /**
     * 类型；如：0：其他；1：行业分类；2：来源分类；3：数据类型
     */
    @Column(name = "`type`")
    private String type;

    public Long getDictId() {
        return dictId;
    }

    public void setDictId(Long dictId) {
        this.dictId = dictId;
    }

    public String getItemText() {
        return itemText;
    }

    public void setItemText(String itemText) {
        this.itemText = itemText;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }



}