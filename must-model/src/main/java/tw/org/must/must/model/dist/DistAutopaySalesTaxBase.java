package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_autopay_sales_tax_base`")
public class DistAutopaySalesTaxBase extends BaseEntity {

    /**
     * 创建用户
     */
    @Column(name = "`create_user`")
    private String createUser;

    /**
     * 营业税率
     */
    @Column(name = "`tax_rate`")
    private BigDecimal taxRate;

    /**
     * 是否提交标志，C：commit，P:process
     */
    @Column(name = "`flag_commit`")
    private String flagCommit;

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getFlagCommit() {
        return flagCommit;
    }

    public void setFlagCommit(String flagCommit) {
        this.flagCommit = flagCommit;
    }



}