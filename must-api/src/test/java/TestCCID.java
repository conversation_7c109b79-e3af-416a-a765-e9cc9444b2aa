import java.lang.reflect.InvocationTargetException;

import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.ccidv13.vo.HeaderRecordV13;
import com.firstbrave.api.ccidv13.vo.InvoiceDetailRecordV13;
import com.firstbrave.api.ccidv13.vo.TrailerRecordV13;
import com.firstbrave.api.parser.CCIDFileDecoder;
import com.firstbrave.api.parser.CCIDFileEncoder;

public class TestCCID {
	public static void main(String[] args) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException, InstantiationException {
		
		
	
		CCIDFileEncoder encoder = new CCIDFileEncoder();
		CCIDFileDecoder decoder = new CCIDFileDecoder();
		String headerRecord = "HD|13|20190222|MUSTPR|Spotify|2016000001|TW|20160101|20160131|TWD|TWD|100000|";
		CCIDRecordPrefix decode2 = decoder.decode(HeaderRecordV13.class, headerRecord);
		
		String invoiceDetailRecord = "ID|ORI|132355804|||Free|000A243HMT63QEeKbCDxEb|000A243HMT63QEeKbCDxEb|GBAYE9400061||C-1022513140|Street Spirit (Fade Out)|LSS|ST|1||M|0001|0001|0000|10000||A|2500|00|00|00|7500|0000|00|10000|0000|0000|0000|0000|0000|0000|0001|0000|0000|0000|00|00|00|00|10000|00|00|00";
		
		CCIDRecordPrefix decode = decoder.decode(InvoiceDetailRecordV13.class, invoiceDetailRecord);
		
		
		
		HeaderRecordV13 hr = new HeaderRecordV13();
		hr.setRecordType("HD");
		hr.setVersion("13");
		//頭部
		try {
			
			System.out.println(encoder.code(decode2));
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//內容
		
		
		
		try {
			
			InvoiceDetailRecordV13 invoiceDetail = new InvoiceDetailRecordV13();
			System.out.println(encoder.code(decode));
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		//內容
		
		
	try {
			
		TrailerRecordV13 trailerRecord = new TrailerRecordV13();
		System.out.println(encoder.code(trailerRecord));
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//尾部
	}
}
