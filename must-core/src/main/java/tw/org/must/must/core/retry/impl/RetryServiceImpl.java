package tw.org.must.must.core.retry.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.enums.DBTypeEnum;
import tw.org.must.must.core.datasource.DBContextHolder;
import tw.org.must.must.core.retry.RetryService;
import tw.org.must.must.core.service.common.AgrAgreementCommonService;
import tw.org.must.must.core.service.common.MbrMemberMembershipCommonService;
import tw.org.must.must.core.service.common.MoneyCommonService;
import tw.org.must.must.core.service.common.WrkWorkCommonService;
import tw.org.must.must.core.service.es.ESService;
import tw.org.must.must.core.service.orcal.*;
import tw.org.must.must.core.task.transfer.DealAgrHandle;
import tw.org.must.must.core.task.transfer.DealMbrIpHandle;
import tw.org.must.must.core.task.transfer.DealMbrMemberShipHandle;
import tw.org.must.must.core.task.transfer.DealWrkWorkHandle;
import tw.org.must.must.model.orcal.OrcalAgrContent;
import tw.org.must.must.model.orcal.OrcalMbrIpName;
import tw.org.must.must.model.orcal.OrcalMbrMemberMembership;
import tw.org.must.must.model.orcal.OrcalWrkWork;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
@Component
public class RetryServiceImpl implements RetryService {

    @Autowired
    private OrcalCommonService orcalCommonService;

    @Autowired
    private MoneyCommonService moneyCommonService;

    @Autowired
    private WrkWorkCommonService wrkWorkCommonService;

    @Autowired
    private OrcalWrkWorkService orcalWrkWorkService;

    @Autowired
    private ESService eSService;

    @Autowired
    private OrcalAgrContentService orcalAgrContentService;

    @Autowired
    private AgrAgreementCommonService agrAgreementCommonService;

    @Autowired
    private OrcalMbrIpNameService orcalMbrIpNameService;

    @Autowired
    private OrcalMbrMemberMembershipService orcalMbrMemberMembershipService;

    @Autowired
    private MbrMemberMembershipCommonService mbrMemberMembershipCommonService;

    private ThreadLocal<DealWrkWorkHandle> orcalWrkWorkHandleThreadLocal = new ThreadLocal<>();
    private ThreadLocal<DealAgrHandle> orcalDealHandleThreadLocal = new ThreadLocal<>();
    private ThreadLocal<DealMbrIpHandle> orcalDealMbrIpHandle = new ThreadLocal<>();
    private ThreadLocal<DealMbrMemberShipHandle> orcalDealMbrMemberShipHandle = new ThreadLocal<>();

    private ThreadLocal<Map<String,String>> ipNameNoAndBaseNoMapThreadLocal = new ThreadLocal<>();

    @Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 3000, multiplier = 1, maxDelay = 10000))
    @Override
    public String syncWrkWorkData(String lastWrkWorkAmendTime, int start, int end) {
        DBContextHolder.set(DBTypeEnum.ORCAL);
        List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNum(lastWrkWorkAmendTime,start,end);
        if(CollectionUtils.isEmpty(selectByDateByRowNum)){
            return "";
        }
        selectByDateByRowNum.forEach(orcalWrkWork -> {
            // 处理数据
            log.info("当前作品id："+orcalWrkWork.getWORKNUM()+"当前作品soc:"+orcalWrkWork.getWORKNUM_SOCIETY()+"开始同步");
            long startTime = System.currentTimeMillis();
            syncAddWorkData(orcalWrkWork);
            long endTime = System.currentTimeMillis();
            log.info("当前作品id："+orcalWrkWork.getWORKNUM()+"当前作品soc:"+orcalWrkWork.getWORKNUM_SOCIETY()+"结束同步，耗时 = "+(endTime-startTime) + " ms");
        });
        lastWrkWorkAmendTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(selectByDateByRowNum.get(selectByDateByRowNum.size() - 1).getLAST_AMEND_DATE());
        return lastWrkWorkAmendTime;
    }

    @Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 6000, multiplier = 1, maxDelay = 10000))
    @Override
    public String syncAddAgrData(String lastAgrAmendTime, int start, int end) {
        DBContextHolder.set(DBTypeEnum.ORCAL);
        List<OrcalAgrContent> oacList = orcalAgrContentService.selectByDateByRowNum(lastAgrAmendTime,start,end);
        log.info("transferAddAgr---此次一共新增/修改合约："+oacList.size()+"条！");
        if(null == oacList || oacList.size()<1){
            return "";
        }
        AtomicInteger count = new AtomicInteger();
        oacList.forEach(orcalAgrContent -> {
            count.getAndIncrement();
            log.info("当前为第"+count+"个，合约编号为："+orcalAgrContent.getAGR_NO());
            syncAgrDate(orcalAgrContent);
        });
        lastAgrAmendTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(oacList.get(oacList.size() - 1).getAMEND_TIME());
        return lastAgrAmendTime;
    }

    @Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 9000, multiplier = 1, maxDelay = 10000))
    @Override
    public String syncMbrIpName(String lastMbrIpNameAmendTime, int start, int end) {
        DBContextHolder.set(DBTypeEnum.ORCAL);
        List<OrcalMbrIpName> orcalMbrIpNameList = orcalMbrIpNameService.selectByDateByRowNum(lastMbrIpNameAmendTime,start,end);
        log.info("transferAddMbrIP---此次一共新增/修改mbrIpName一共："+orcalMbrIpNameList.size()+"条！");
        if(null == orcalMbrIpNameList || orcalMbrIpNameList.size()<1){
            log.info("根据时间为："+lastMbrIpNameAmendTime+";查无数据！");
            return "";
        }
        AtomicInteger count = new AtomicInteger();
        orcalMbrIpNameList.forEach(orcalMbrIpName -> {
            count.getAndIncrement();
            log.info("当前为第"+count+"个，transferAddMbrIP IpNameNo ："+orcalMbrIpName.getIP_NAME_NO());
            syncMbrIpNameData(orcalMbrIpName);
        });

        lastMbrIpNameAmendTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orcalMbrIpNameList.get(orcalMbrIpNameList.size() - 1).getAMEND_TIME());
        return lastMbrIpNameAmendTime;
    }

    @Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 12000, multiplier = 1, maxDelay = 10000))
    @Override
    public String syncMbrMemberShip(String lastMbrMemberAmendTime, int start, int end) {

        DBContextHolder.set(DBTypeEnum.ORCAL);
        List<OrcalMbrMemberMembership> orcalMbrMemberMembershipList = orcalMbrMemberMembershipService.getMbrMemberShipList(lastMbrMemberAmendTime);
        log.info("transferAddMbrMember---此次新增/修改mbrMember一共："+orcalMbrMemberMembershipList.size()+"条！");
        if(null == orcalMbrMemberMembershipList || orcalMbrMemberMembershipList.size()<1){
            log.info("根据时间为："+orcalMbrMemberMembershipList+";查无数据！");
            return "";
        }
        AtomicInteger count = new AtomicInteger();
        orcalMbrMemberMembershipList.forEach(orcalMbrMemberMembership -> {
            count.getAndIncrement();
            log.info("当前为第"+count+"个，transferAddMbrMember--- 开始同步(新增/修改)的会员ipBaseNo为："+orcalMbrMemberMembership.getIP_BASE_NO());
            syncMemberShipData(orcalMbrMemberMembership);
        });

        lastMbrMemberAmendTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(orcalMbrMemberMembershipList.get(orcalMbrMemberMembershipList.size() - 1).getLST_AMEND_DATE());
        return lastMbrMemberAmendTime;
    }

    private void syncMemberShipData(OrcalMbrMemberMembership orcalMbrMemberMembership) {

        // 1、创建handle
        DealMbrMemberShipHandle dealMbrMemberShipHandle = orcalDealMbrMemberShipHandle.get();
        if(dealMbrMemberShipHandle == null){
            dealMbrMemberShipHandle = new DealMbrMemberShipHandle();
        }

        // 2、根据orcal 查询 Orcal数据库 mbrMemberShip 相关数据
        orcalCommonService.getOrcalMbrMemberShipData(dealMbrMemberShipHandle,orcalMbrMemberMembership);

        // 3、根据orcal 查询 Money数据库 mbrMemberShip 相关数据
        moneyCommonService.getMoneyMbrMemberShipData(dealMbrMemberShipHandle,orcalMbrMemberMembership);

        // 4、初始化handle中的数据
        dealMbrMemberShipHandle.initMbrMemberShipData();

        // 5、处理handle 处理数据  将orcal数据于money数据进行比对
        dealMbrMemberShipHandle.dealMbrMemberShipData();

        // 6、将handle中的数据入库  比对完后的数据写入数据库
        writeMbrMemberShipByHandle(dealMbrMemberShipHandle);

        // 7、清空
        orcalDealMbrMemberShipHandle.remove();
    }

    private void writeMbrMemberShipByHandle(DealMbrMemberShipHandle dealMbrMemberShipHandle) {

        log.info("Money新增了{}条MbrMemberInfo ",dealMbrMemberShipHandle.getMemberInfoList().size());
        log.info("Money新增了{}条MbrMemberAddress",dealMbrMemberShipHandle.getMbrMemberAddressList().size());
        log.info("Money新增了{}条MbrMemberContact",dealMbrMemberShipHandle.getMbrMemberContactList().size());
        log.info("Money新增了{}条MbrMemberRemark",dealMbrMemberShipHandle.getMbrMemberRemarkList().size());
        log.info("Money新增了{}条MbrMemberSuccessor",dealMbrMemberShipHandle.getMbrMemberSuccessorList().size());
        log.info("Money新增了{}条MbrMemberVisa",dealMbrMemberShipHandle.getMbrMemberVisaList().size());
        log.info("Money新增了{}条MbrMemberBankAcc ",dealMbrMemberShipHandle.getMbrMemberBankAccList().size());
        log.info("Money新增了{}条MbrMemberUpaHistory",dealMbrMemberShipHandle.getMbrMemberUpaHistoryList().size());
        log.info("Money新增了{}条MbrMemberBankAccDtl",dealMbrMemberShipHandle.getMbrMemberBankAccDtlList().size());

        // 数据写入数据库
        mbrMemberMembershipCommonService.dealMbrMemberShip(dealMbrMemberShipHandle.getMbrMemberMembership());

        mbrMemberMembershipCommonService.dealMbrMemberAddress(dealMbrMemberShipHandle.getMbrMemberAddressList());

        mbrMemberMembershipCommonService.dealMbrMemberBankAccDtl(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberBankAcc(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberContact(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberInfo(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberRemark(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberSuccessor(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberUpaHistory(dealMbrMemberShipHandle);

        mbrMemberMembershipCommonService.dealMbrMemberVisa(dealMbrMemberShipHandle);

    }

    private void syncMbrIpNameData(OrcalMbrIpName orcalMbrIpName) {

        // 1、创建handle
        DealMbrIpHandle dealMbrIpHandle = orcalDealMbrIpHandle.get();
        if(null == dealMbrIpHandle){
            dealMbrIpHandle = new DealMbrIpHandle();
        }

        // 2、获取orcal查询数据
        orcalCommonService.getOrcalMbrIpRelationData(dealMbrIpHandle,orcalMbrIpName);

        // 3、获取money查询数据
        moneyCommonService.getMoneyMbrIpRelationData(dealMbrIpHandle,orcalMbrIpName);

        // 4、初始化handle中的数据
//        dealMbrIpHandle.init();

        // 5、处理handle 处理数据
        dealMbrIpHandle.dealMbrIpData();

        // 6、将handle中的数据入库
//        writeMbrIpToMoneyByMbrIpHandle(dealMbrIpHandle);

        // 7、清空 orcalDealHandleThreadLocal
        orcalDealMbrIpHandle.remove();


    }

    private void syncAgrDate(OrcalAgrContent orcalAgrContent) {

        DealAgrHandle dealAgrHandle = orcalDealHandleThreadLocal.get();
        if(null == dealAgrHandle){
            dealAgrHandle = new DealAgrHandle();
        }

        orcalCommonService.getOrcalAgrRelationData(dealAgrHandle,orcalAgrContent);

        moneyCommonService.getMoneyAgrRelationData(dealAgrHandle,orcalAgrContent);

        dealAgrHandle.init();

        // 处理数据
        dealAgrHandle.dealAgrData();

        // 数据入库
        writeAgrToMoneyDbByAgrHandle(dealAgrHandle);

        // 清空 orcalDealHandleThreadLocal
        orcalDealHandleThreadLocal.remove();
    }

    private void writeAgrToMoneyDbByAgrHandle(DealAgrHandle dealAgrHandle) {

        // TODO
        agrAgreementCommonService.dealAgrAssignor(dealAgrHandle);
        agrAgreementCommonService.dealAgrAssignee(dealAgrHandle);
        agrAgreementCommonService.dealAgrAgrAgreementExtend(dealAgrHandle);
        agrAgreementCommonService.dealAgrAgreementRemark(dealAgrHandle);
        agrAgreementCommonService.dealAgrAgreementSource(dealAgrHandle);
        agrAgreementCommonService.dealAgrAgreementTerritory(dealAgrHandle);
    }

    @Recover
    public void recover(Exception e) {
        e.printStackTrace();
        log.info("回调方法执行！！！");
    }

    public void syncAddWorkData(OrcalWrkWork orcalWrkWork) {

        Map<String, String> ipNameNoBaseNoMap = ipNameNoAndBaseNoMapThreadLocal.get();
        if(null == ipNameNoBaseNoMap || ipNameNoBaseNoMap.size()<1){
            ipNameNoBaseNoMap = new HashMap<>();
        }

        DealWrkWorkHandle dealWrkWorkHandle = orcalWrkWorkHandleThreadLocal.get();
        if(null == dealWrkWorkHandle){
            dealWrkWorkHandle = new DealWrkWorkHandle();
        }

        orcalCommonService.getOrcalWrkWorkReleationData(dealWrkWorkHandle,orcalWrkWork,ipNameNoBaseNoMap); //  这里ipNameNoBaseNoMap 是为了put数据

        moneyCommonService.getMoneyWrkWorkReleationData(dealWrkWorkHandle,orcalWrkWork);

        dealWrkWorkHandle.init();

        dealWrkWorkHandle.dealWrkWork(ipNameNoBaseNoMap); // 这里ipNameNoBaseNoMap 是为了get数据

        // DB操作
        writeWrkWorkToMoneyDbByWorkHandle(dealWrkWorkHandle);

        // ES处理作品
        eSService.addNewWrkWorkToES(dealWrkWorkHandle.getMoneyWrkWork());

        ipNameNoAndBaseNoMapThreadLocal.remove();

        orcalWrkWorkHandleThreadLocal.remove();

    }

    private void writeWrkWorkToMoneyDbByWorkHandle(DealWrkWorkHandle dealWrkWorkHandle) {

        wrkWorkCommonService.dealWrkWork(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkIpShare(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkTitle(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkArtist(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkIsrc(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkComponentList(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkAristMerge(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkRemark(dealWrkWorkHandle);

        wrkWorkCommonService.delWrkWorkRights(dealWrkWorkHandle);

        wrkWorkCommonService.dealWrkWorkSource(dealWrkWorkHandle);

    }
}
