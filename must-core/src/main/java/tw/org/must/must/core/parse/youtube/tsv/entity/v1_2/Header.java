package tw.org.must.must.core.parse.youtube.tsv.entity.v1_2;

public class Header extends BaseTsvEntity {
    //#HEAD	MessageVersion	Profile	ProfileVersion	MessageId	MessageCreatedDateTime
    // FileNumber	NumberofFiles	UsageStartDate	UsageEndDate	SenderPartyId	SenderName
    // ServiceDescription	RecipientPartyId	RecipientName	RepresentedRepertoire

    private String MessageVersion;
    private String Profile;
    private String ProfileVersion;
    private String MessageId;
    private String MessageCreatedDateTime;
    private String FileNumber;
    private String NumberofFiles;
    private String UsageStartDate;
    private String UsageEndDate;
    private String SenderPartyId;
    private String SenderName;
    private String ServiceDescription;
    private String RecipientPartyId;
    private String RecipientName;
    private String RepresentedRepertoire;



    public String getMessageVersion() {
        return MessageVersion;
    }

    public void setMessageVersion(String messageVersion) {
        MessageVersion = messageVersion;
    }

    public String getProfile() {
        return Profile;
    }

    public void setProfile(String profile) {
        Profile = profile;
    }

    public String getProfileVersion() {
        return ProfileVersion;
    }

    public void setProfileVersion(String profileVersion) {
        ProfileVersion = profileVersion;
    }

    public String getMessageId() {
        return MessageId;
    }

    public void setMessageId(String messageId) {
        MessageId = messageId;
    }

    public String getMessageCreatedDateTime() {

        return MessageCreatedDateTime;
    }

    public void setMessageCreatedDateTime(String messageCreatedDateTime) {
        MessageCreatedDateTime = messageCreatedDateTime;
    }

    public String getFileNumber() {
        return FileNumber;
    }

    public void setFileNumber(String fileNumber) {
        FileNumber = fileNumber;
    }

    public String getNumberofFiles() {
        return NumberofFiles;
    }

    public void setNumberofFiles(String numberofFiles) {
        NumberofFiles = numberofFiles;
    }

    public String getUsageStartDate() {
        return UsageStartDate;
    }

    public void setUsageStartDate(String usageStartDate) {
        UsageStartDate = usageStartDate;
    }

    public String getUsageEndDate() {
        return UsageEndDate;
    }

    public void setUsageEndDate(String usageEndDate) {
        UsageEndDate = usageEndDate;
    }

    public String getSenderPartyId() {
        return SenderPartyId;
    }

    public void setSenderPartyId(String senderPartyId) {
        SenderPartyId = senderPartyId;
    }

    public String getSenderName() {
        return SenderName;
    }

    public void setSenderName(String senderName) {
        SenderName = senderName;
    }

    public String getServiceDescription() {
        return ServiceDescription;
    }

    public void setServiceDescription(String serviceDescription) {
        ServiceDescription = serviceDescription;
    }

    public String getRecipientPartyId() {
        return RecipientPartyId;
    }

    public void setRecipientPartyId(String recipientPartyId) {
        RecipientPartyId = recipientPartyId;
    }

    public String getRecipientName() {
        return RecipientName;
    }

    public void setRecipientName(String recipientName) {
        RecipientName = recipientName;
    }

    public String getRepresentedRepertoire() {
        return RepresentedRepertoire;
    }

    public void setRepresentedRepertoire(String representedRepertoire) {
        RepresentedRepertoire = representedRepertoire;
    }
}
